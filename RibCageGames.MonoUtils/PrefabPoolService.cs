using System;
using System.Collections.Generic;
using UnityEngine.Events;
using UnityEngine.SceneManagement;

namespace RibCageGames.MonoUtils
{
    using Base;
    using UnityEngine;

    /// <summary>
    /// A service to better handle Update style callbacks
    /// </summary>
    [CreateAssetMenu(fileName = "PrefabPoolService", menuName = "RibCageGames/Services/PrefabPoolService")]
    public class PrefabPoolService : BaseService
    {

        [SerializeField] private List<BaseService> m_poolAssets;
        public override void Init(GameObject servicePrefab = null)
        {
            foreach (BaseService poolAsset in m_poolAssets)
            {
                poolAsset.Init();
            }
        }

        public override void StartService(MonoInjector injector)
        {
            foreach (BaseService poolAsset in m_poolAssets)
            {
                poolAsset.StartService(injector);
            }
        }

        public override void Dispose()
        {
            foreach (BaseService poolAsset in m_poolAssets)
            {
                poolAsset.Dispose();
            }
        }
    }

    public interface IPoolable
    {
        void ActivatePoolable();
        void DeactivatePoolable();
        void ResetPoolable(bool resetMovement = true);
        void Initialize();
        UnityEvent OnDisable { get; }
    }

    public class PrefabPoolAsset<T> : BaseService where T : MonoBehaviour, IPoolable
    {
        [SerializeField] private PrefabPool<T> m_pool;
        
        public T Prefab => m_pool.m_prefab;
        
        public override void Init(GameObject servicePrefab = null)
        {
            m_pool.Init();
        }

        public override void StartService(MonoInjector injector)
        {
            m_pool.Start();
        }

        public override void Dispose()
        {
            m_pool.Dispose();
        }
        
        public T GetInstance()
        {
            return m_pool.GetInstance();
        }
    }

    [Serializable]
    public class PrefabPool<T> where T : MonoBehaviour, IPoolable
    {
        [SerializeField] internal T m_prefab;
        [SerializeField] private int m_initialPoolSize;
        [SerializeField] private Vector3 m_spawnPosition;
        [SerializeField] private bool m_parentWithSource;
        
        [NonSerialized] private List<T> m_pool;
        [NonSerialized] private List<T> m_availablePool;
        
        public T Prefab => m_prefab;
        
        public void Init()
        {
            m_pool = new List<T>();
            m_availablePool = new List<T>();
        }

        public void Start()
        {
            for (int i = 0; i < m_initialPoolSize; i++)
            {
                T newInstance = CreateInstance();
                newInstance.gameObject.name += i.ToString();
            }
            
            m_availablePool.AddRange(m_pool);
        }

        private T CreateInstance()
        {
            T instance = Object.Instantiate(m_prefab, m_spawnPosition, Quaternion.identity, m_parentWithSource ? m_prefab.transform.parent : null);

            SceneManager.MoveGameObjectToScene(instance.gameObject, ServiceLocator.Get<MonoService>().MonoInjector.gameObject.scene);
            GameObject.DontDestroyOnLoad(instance.gameObject);

            m_pool.Add(instance);
            instance.Initialize();
            instance.DeactivatePoolable();
            instance.OnDisable.AddListener(() => InstanceDisabled(instance));

            return instance;
        }

        private void InstanceDisabled(T instance)
        {
            //Delayed by frame to prevent disable event fighting
            MonoProcess.New().Do(() =>
            {
                instance.DeactivatePoolable();
                m_availablePool.Add(instance); 
            });
        }
        
        public T GetInstance()
        {
            T instance;
            if (m_availablePool.Count < 1)
            {
                instance = CreateInstance();
            }
            else
            {
                instance = m_availablePool[0];
                m_availablePool.RemoveAt(0);
            }
            instance.ResetPoolable();
            return instance;
        }

        public void Dispose()
        {
            m_pool.Clear();
            m_availablePool.Clear();
        }
    }
}
