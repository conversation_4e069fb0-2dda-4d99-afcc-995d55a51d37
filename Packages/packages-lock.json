{"dependencies": {"com.coffee.ui-particle": {"version": "https://github.com/mob-sakai/ParticleEffectForUGUI.git", "depth": 0, "source": "git", "dependencies": {"com.unity.ugui": "1.0.0", "com.unity.modules.particlesystem": "1.0.0"}, "hash": "2ec1e28cde80fb611e9447a798fc53998d3201ec"}, "com.cysharp.unitask": {"version": "https://github.com/Cysharp/UniTask.git?path=src/UniTask/Assets/Plugins/UniTask", "depth": 0, "source": "git", "dependencies": {}, "hash": "f9fd769be7c634610f2a61aa914a1a55f34740e1"}, "com.cysharp.zlinq": {"version": "https://github.com/Cysharp/ZLinq.git?path=src/ZLinq.Unity/Assets/ZLinq.Unity", "depth": 0, "source": "git", "dependencies": {}, "hash": "f317c81f8644dc41cc62ed55363df9d8d9d2cf4e"}, "com.github-glitchenzo.nugetforunity": {"version": "https://github.com/GlitchEnzo/NuGetForUnity.git?path=/src/NuGetForUnity", "depth": 0, "source": "git", "dependencies": {}, "hash": "a7c6b49a0141a5bff9b1983e38137522ef61977d"}, "com.needle.shadergraph-markdown": {"version": "https://github.com/needle-tools/shadergraph-markdown.git", "depth": 0, "source": "git", "dependencies": {}, "hash": "1ce7be8e292a6139354d3de85f11e4fb006b8c1f"}, "com.ribcagegames": {"version": "file:C:/NotWork/AlphaNomos/RibCageGamesCore", "depth": 0, "source": "local", "dependencies": {}}, "com.unity.2d.sprite": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.2d.tilemap": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.tilemap": "1.0.0", "com.unity.modules.uielements": "1.0.0"}}, "com.unity.ads": {"version": "3.7.5", "depth": 0, "source": "registry", "dependencies": {"com.unity.ugui": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.ai.navigation": {"version": "2.0.8", "depth": 0, "source": "registry", "dependencies": {"com.unity.modules.ai": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.bindings.openimageio": {"version": "1.0.0", "depth": 1, "source": "registry", "dependencies": {"com.unity.collections": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.burst": {"version": "1.8.21", "depth": 1, "source": "registry", "dependencies": {"com.unity.mathematics": "1.2.1", "com.unity.modules.jsonserialize": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.cinemachine": {"version": "3.1.4", "depth": 0, "source": "registry", "dependencies": {"com.unity.splines": "2.0.0", "com.unity.modules.imgui": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.collections": {"version": "2.5.7", "depth": 0, "source": "registry", "dependencies": {"com.unity.burst": "1.8.19", "com.unity.mathematics": "1.3.2", "com.unity.test-framework": "1.4.6", "com.unity.nuget.mono-cecil": "1.11.5", "com.unity.test-framework.performance": "3.0.3"}, "url": "https://packages.unity.com"}, "com.unity.editorcoroutines": {"version": "1.0.0", "depth": 1, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.ext.nunit": {"version": "2.0.5", "depth": 1, "source": "builtin", "dependencies": {}}, "com.unity.ide.rider": {"version": "3.0.36", "depth": 0, "source": "registry", "dependencies": {"com.unity.ext.nunit": "1.0.6"}, "url": "https://packages.unity.com"}, "com.unity.ide.visualstudio": {"version": "2.0.23", "depth": 0, "source": "registry", "dependencies": {"com.unity.test-framework": "1.1.9"}, "url": "https://packages.unity.com"}, "com.unity.inputsystem": {"version": "1.14.0", "depth": 0, "source": "registry", "dependencies": {"com.unity.modules.uielements": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.mathematics": {"version": "1.3.2", "depth": 1, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.memoryprofiler": {"version": "1.1.6", "depth": 0, "source": "registry", "dependencies": {"com.unity.burst": "1.8.0", "com.unity.collections": "1.2.3", "com.unity.mathematics": "1.2.1", "com.unity.profiling.core": "1.0.0", "com.unity.editorcoroutines": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.nuget.mono-cecil": {"version": "1.11.5", "depth": 1, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.postprocessing": {"version": "3.4.0", "depth": 0, "source": "registry", "dependencies": {"com.unity.modules.physics": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.probuilder": {"version": "6.0.5", "depth": 0, "source": "registry", "dependencies": {"com.unity.shadergraph": "17.0.3", "com.unity.modules.imgui": "1.0.0", "com.unity.modules.physics": "1.0.0", "com.unity.settings-manager": "1.0.3"}, "url": "https://packages.unity.com"}, "com.unity.profiling.core": {"version": "1.0.2", "depth": 1, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.recorder": {"version": "5.1.2", "depth": 0, "source": "registry", "dependencies": {"com.unity.timeline": "1.8.7", "com.unity.collections": "1.2.4", "com.unity.bindings.openimageio": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.render-pipelines.core": {"version": "17.0.4", "depth": 1, "source": "builtin", "dependencies": {"com.unity.burst": "1.8.20", "com.unity.mathematics": "1.3.2", "com.unity.ugui": "2.0.0", "com.unity.collections": "2.4.3", "com.unity.modules.physics": "1.0.0", "com.unity.modules.terrain": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0", "com.unity.rendering.light-transport": "1.0.1"}}, "com.unity.render-pipelines.universal": {"version": "17.0.4", "depth": 0, "source": "builtin", "dependencies": {"com.unity.render-pipelines.core": "17.0.4", "com.unity.shadergraph": "17.0.4", "com.unity.render-pipelines.universal-config": "17.0.3"}}, "com.unity.render-pipelines.universal-config": {"version": "17.0.3", "depth": 1, "source": "builtin", "dependencies": {"com.unity.render-pipelines.core": "17.0.3"}}, "com.unity.rendering.light-transport": {"version": "1.0.1", "depth": 2, "source": "builtin", "dependencies": {"com.unity.collections": "2.2.0", "com.unity.mathematics": "1.2.4", "com.unity.modules.terrain": "1.0.0"}}, "com.unity.searcher": {"version": "4.9.3", "depth": 2, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.settings-manager": {"version": "2.1.0", "depth": 1, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.shadergraph": {"version": "17.0.4", "depth": 1, "source": "builtin", "dependencies": {"com.unity.render-pipelines.core": "17.0.4", "com.unity.searcher": "4.9.3"}}, "com.unity.splines": {"version": "2.8.1", "depth": 1, "source": "registry", "dependencies": {"com.unity.mathematics": "1.2.1", "com.unity.modules.imgui": "1.0.0", "com.unity.settings-manager": "1.0.3"}, "url": "https://packages.unity.com"}, "com.unity.terrain-tools": {"version": "5.2.1", "depth": 0, "source": "registry", "dependencies": {"com.unity.modules.terrain": "1.0.0", "com.unity.modules.terrainphysics": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.test-framework": {"version": "1.5.1", "depth": 0, "source": "builtin", "dependencies": {"com.unity.ext.nunit": "2.0.3", "com.unity.modules.imgui": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0"}}, "com.unity.test-framework.performance": {"version": "3.1.0", "depth": 1, "source": "registry", "dependencies": {"com.unity.test-framework": "1.1.33", "com.unity.modules.jsonserialize": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.textmeshpro": {"version": "5.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.ugui": "2.0.0"}}, "com.unity.timeline": {"version": "1.8.8", "depth": 0, "source": "registry", "dependencies": {"com.unity.modules.audio": "1.0.0", "com.unity.modules.director": "1.0.0", "com.unity.modules.animation": "1.0.0", "com.unity.modules.particlesystem": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.ugui": {"version": "2.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.ui": "1.0.0", "com.unity.modules.imgui": "1.0.0"}}, "com.unity.visualeffectgraph": {"version": "17.0.4", "depth": 0, "source": "builtin", "dependencies": {"com.unity.shadergraph": "17.0.4", "com.unity.render-pipelines.core": "17.0.4"}}, "com.unity.xr.legacyinputhelpers": {"version": "2.1.10", "depth": 0, "source": "registry", "dependencies": {"com.unity.modules.vr": "1.0.0", "com.unity.modules.xr": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.modules.ai": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.androidjni": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.animation": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.assetbundle": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.audio": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.cloth": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.physics": "1.0.0"}}, "com.unity.modules.director": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.audio": "1.0.0", "com.unity.modules.animation": "1.0.0"}}, "com.unity.modules.hierarchycore": {"version": "1.0.0", "depth": 1, "source": "builtin", "dependencies": {}}, "com.unity.modules.imageconversion": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.imgui": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.jsonserialize": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.particlesystem": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.physics": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.physics2d": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.screencapture": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.imageconversion": "1.0.0"}}, "com.unity.modules.subsystems": {"version": "1.0.0", "depth": 1, "source": "builtin", "dependencies": {"com.unity.modules.jsonserialize": "1.0.0"}}, "com.unity.modules.terrain": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.terrainphysics": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.physics": "1.0.0", "com.unity.modules.terrain": "1.0.0"}}, "com.unity.modules.tilemap": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.physics2d": "1.0.0"}}, "com.unity.modules.ui": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.uielements": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.ui": "1.0.0", "com.unity.modules.imgui": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0", "com.unity.modules.hierarchycore": "1.0.0"}}, "com.unity.modules.umbra": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.unityanalytics": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.unitywebrequest": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0"}}, "com.unity.modules.unitywebrequest": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.unitywebrequestassetbundle": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.assetbundle": "1.0.0", "com.unity.modules.unitywebrequest": "1.0.0"}}, "com.unity.modules.unitywebrequestaudio": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.unitywebrequest": "1.0.0", "com.unity.modules.audio": "1.0.0"}}, "com.unity.modules.unitywebrequesttexture": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.unitywebrequest": "1.0.0", "com.unity.modules.imageconversion": "1.0.0"}}, "com.unity.modules.unitywebrequestwww": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.unitywebrequest": "1.0.0", "com.unity.modules.unitywebrequestassetbundle": "1.0.0", "com.unity.modules.unitywebrequestaudio": "1.0.0", "com.unity.modules.audio": "1.0.0", "com.unity.modules.assetbundle": "1.0.0", "com.unity.modules.imageconversion": "1.0.0"}}, "com.unity.modules.vehicles": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.physics": "1.0.0"}}, "com.unity.modules.video": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.audio": "1.0.0", "com.unity.modules.ui": "1.0.0", "com.unity.modules.unitywebrequest": "1.0.0"}}, "com.unity.modules.vr": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.jsonserialize": "1.0.0", "com.unity.modules.physics": "1.0.0", "com.unity.modules.xr": "1.0.0"}}, "com.unity.modules.wind": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.xr": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.physics": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0", "com.unity.modules.subsystems": "1.0.0"}}}}