using System.Collections.Generic;
using UnityEngine;

public abstract class Powerup : MonoScriptableObject
{
    [SerializeField] private string m_name;
    [SerializeField] [TextArea] private string m_description;
    [SerializeField] private Powerup m_powerupPreviousLevel;
    [SerializeField] private List<Powerup> m_powerupEvolutions;
    
    public Powerup PowerupPreviousLevel => m_powerupPreviousLevel;
    public List<Powerup> PowerupEvolutions => m_powerupEvolutions;
    public string DisplayName => m_name;
    public string Description => m_description;
    public abstract PowerUpType Type { get; }
    public abstract bool IsNamedPowerup { get; }
    
    public override void Initialize() { }
    public virtual void UpgradeBonusLevel() { }
    public abstract void SelectPowerup();
}

public abstract class BonusEffect
{
    public abstract PowerUpType BonusType { get; }

    public abstract void RemoveBonus();

    public virtual float FillAmount => 1f;
}

public enum PowerUpType
{
    Reverb = 1,
    Delay = 2,
    Flanger = 3,
    WeaponBuff = 10,
    Plugin = 20,
    MaxHealth = 50,
    PowerupUpgrade = 100,
    
    DelayUpgrade = 110,
    ReverbUpgrade = 120,
    FlangerUpgrade = 130,
}
