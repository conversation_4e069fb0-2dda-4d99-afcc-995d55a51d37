using BehaviorDesigner.Runtime.Tasks;
using RibCageGames.Base;
using UnityEngine;

public class MoveAwayFromPlayerBehavior : AnimatedBehavior
{
    [SerializeField] private float m_speed;
    [SerializeField] private float m_targetDistance;
    [SerializeField] private bool m_useCustomCurve;
    [SerializeField] private AnimationCurve m_targetCurve;
    [SerializeField] private float m_minimalViableMovementValue = 0.5f;
    [SerializeField] private float m_maximalDuration;
    [SerializeField] private bool m_facePlayer;

    private bool m_durationExceeded;
    private MonoProcess m_durationProcess;
    private Vector3 m_heading;
    private bool m_headingSet;
    public override void OnStart()
    {
        m_durationExceeded = false;
        m_headingSet = false;
        base.OnStart();
        m_combatEntityOwner.SetSpeed(m_speed);
        m_durationProcess = MonoProcess
            .WaitForSecondsProcess(m_maximalDuration)
            .Do(() => m_durationExceeded = true);
    }

    public override TaskStatus OnUpdate()
    {
        if (m_cancelBehavior || m_combatEntityOwner.IsBusy) { return TaskStatus.Failure; }

        base.OnUpdate();
        
        if (!m_headingSet)
        {
            m_headingSet = true;
            m_heading = - m_combatEntityOwner.SelfToPlayerVector;
        }
        
        Vector3 direction = m_combatEntityOwner.CalculateMovementDirection(
            - m_combatEntityOwner.SelfToPlayerVector, m_heading,
            m_minimalViableMovementValue,
            m_useCustomCurve ? m_targetCurve : null);
        
        if (direction.sqrMagnitude < 0.1f)
        {
            return TaskStatus.Failure;
        }
        
        m_heading = direction;
        
        m_combatEntityOwner.SetDirection(direction);
        m_combatEntityOwner.FaceDirection(m_facePlayer ? - direction : direction);
        
        return m_durationExceeded || m_combatEntityOwner.PlayerDistance > m_targetDistance ?
                    TaskStatus.Success :
                    TaskStatus.Running;
    }

    public override void OnEnd()
    {
        base.OnEnd();
        m_combatEntityOwner.SetSpeed(0f);
        m_durationProcess?.Stop();
    }
}
