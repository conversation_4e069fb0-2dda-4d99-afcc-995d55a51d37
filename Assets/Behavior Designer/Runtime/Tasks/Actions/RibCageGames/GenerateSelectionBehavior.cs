using UnityEngine;
using <PERSON>haviorDesign<PERSON>.Runtime;
using BehaviorDesigner.Runtime.Tasks;

public class GenerateSelectionBehavior : Selector
{
    [SerializeField] private SharedBehaviour m_owner;

    protected BaseEnemyController m_combatEntityOwner;
    
    public override void OnAwake()
    {
        base.OnAwake();
        m_combatEntityOwner = ((BaseEnemyController) m_owner.Value);
    }
    
    public override void OnStart()
    {
        m_combatEntityOwner.SetSelectionValue();
        base.OnStart();
    }
}
