using BehaviorDesigner.Runtime.Tasks;
using RibCageGames.Base;
using UnityEngine;

public class IdleBehavior : AnimatedBehavior
{
    [SerializeField] private float m_maximalDuration;

    private bool m_durationExceeded;
    private MonoProcess m_durationProcess;

    public override void OnStart()
    {
        base.OnStart();
        m_combatEntityOwner.SetSpeed(0f);
        m_durationProcess = MonoProcess
            .WaitForSecondsProcess(m_maximalDuration)
            .Do(() => m_durationExceeded = true);
    }

    public override TaskStatus OnUpdate()
    {
        if (m_cancelBehavior || m_combatEntityOwner.IsBusy)
        {
            return TaskStatus.Failure;
        }

        base.OnUpdate();

        return m_durationExceeded ? TaskStatus.Success : TaskStatus.Running;
    }

    public override void OnEnd()
    {
        base.OnEnd();
        m_durationProcess?.Stop();
    }
}