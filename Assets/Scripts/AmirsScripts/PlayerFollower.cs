using DG.Tweening;
using RibCageGames.Base;
using UnityEngine;
using UnityEngine.Serialization;

public class PlayerFollower : MonoBehaviour
{
    [FormerlySerializedAs("_player")] [SerializeField] private Transform m_player;
    [SerializeField][Range(0f, 1f)] private float m_damping = 0.25f;

    private MonoService m_monoService;

    // Start is called before the first frame update
    private void Start()
    {
        ServiceLocator.Get<PlayerService>().OnPlayerRegistered.AddListener(
            (player) =>
            {
                m_player = player.transform;
            });
        
        m_monoService = ServiceLocator.Get<MonoService>();
        m_monoService.OnUpdate.AddListener(FollowUpdate);

        transform.position = m_player.transform.position;
    }

    private void FollowUpdate(float deltaTime)
    {
        //player horizontal plane to follow
        Vector3 _playerPos = m_player.transform.position;
        //transform.position = new Vector3(_playerPos.x, transform.position.y, _playerPos.z);
        //transform.DOMove(new Vector3(transform.position.x, _playerPos.y, transform.position.z), _verticalDamping);
        transform.position = Vector3.Lerp(transform.position, _playerPos, m_damping);
        
        //transform.position = m_player.transform.position;
    }   
}
