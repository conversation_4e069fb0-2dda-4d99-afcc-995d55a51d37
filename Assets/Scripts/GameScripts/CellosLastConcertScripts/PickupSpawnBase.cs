using Cysharp.Threading.Tasks;
using RibCageGames.MonoUtils;
using RibCageGames.UI;
using UnityEngine;
using UnityEngine.Events;

/// <summary>
/// Abstract base class for different types of items that can be picked up.
/// Combines PowerUpPickup functionality with IPoolable implementation.
/// </summary>
public abstract class PickupSpawnBase : MonoBehaviour, IPoolable
{
    [Header("Pickup Components")]
    [SerializeField] protected Collider m_collider;
    [SerializeField] protected OnTriggerEnterListener m_triggerEnter;
    [SerializeField] protected OnTriggerExitListener m_triggerExit;
    [SerializeField] protected ContextualObjectMenuTrigger m_contextualObjectMenuTrigger;
    [SerializeField] protected ParticleSystem m_pickupVisual;
    [SerializeField] protected GameObject m_visual;

    [Header("Pickup Settings")]
    [SerializeField] protected bool m_activateOnStart = false;
    [SerializeField] protected float m_pickupDisableDelay = 1f;

    [Header("Events")]
    [SerializeField] protected UnityEvent m_onPickup;
    [SerializeField] protected UnityEvent m_onActivated;
    [SerializeField] protected UnityEvent m_onDeactivated;

    // Service references
    protected readonly ServiceReference<PopupService> m_popupService = new();

    // State tracking
    protected ContextualObjectMenu m_menu;
    protected bool m_active;
    protected bool m_initialized;

    // IPoolable implementation
    public UnityEvent OnDisable { get; } = new();

    #region Properties

    public float PickupDisableDelay => m_pickupDisableDelay;
    
    /// <summary>
    /// Gets the pickup collider
    /// </summary>
    public Collider Collider => m_collider;

    /// <summary>
    /// Gets the pickup visual effects
    /// </summary>
    public ParticleSystem PickupVisual => m_pickupVisual;

    /// <summary>
    /// Gets the visual GameObject
    /// </summary>
    public GameObject Visual => m_visual;

    /// <summary>
    /// Gets the pickup event
    /// </summary>
    public UnityEvent OnPickup => m_onPickup;

    /// <summary>
    /// Gets whether the pickup is currently active
    /// </summary>
    public bool IsActive => m_active;

    #endregion

    #region Unity Lifecycle

    protected virtual void Start()
    {
        Initialize();
        if (m_activateOnStart)
        {
            Activate();
        }
    }

    #endregion

    #region Initialization

    /// <summary>
    /// Initializes the pickup components and event listeners
    /// </summary>
    public virtual void Initialize()
    {
        if (m_initialized)
        {
            return;
        }

        m_initialized = true;

        m_popupService.Value?.TryGetElement(out m_menu);
        // Set up trigger listeners
        if (m_triggerEnter != null)
        {
            m_triggerEnter.TriggerEnter.AddListener(ActivateMenu);
        }

        if (m_triggerExit != null)
        {
            m_triggerExit.TriggerExit.AddListener(DeactivateMenu);
        }

        // Perform derived class initialization
        OnInitialize();
    }

    /// <summary>
    /// Override this method to perform derived class-specific initialization
    /// </summary>
    protected virtual void OnInitialize()
    {
        // Override in derived classes
    }

    #endregion

    #region Menu Management

    protected virtual void ActivateMenu(Collider collider)
    {
        if (m_active && m_menu != null && m_contextualObjectMenuTrigger != null)
        {
            m_menu.ActivateContext(m_contextualObjectMenuTrigger);
        }
    }

    protected virtual void DeactivateMenu(Collider collider)
    {
        if (m_menu != null && m_contextualObjectMenuTrigger != null)
        {
            m_menu.DeactivateContext(m_contextualObjectMenuTrigger);
        }
    }

    #endregion

    #region Helper Methods

    /// <summary>
    /// Disables the interaction components (collider, visual, menu)
    /// </summary>
    /// <param name="manageMenu">Whether to manage the contextual menu</param>
    protected virtual void DisableInteractionComponents(bool manageMenu)
    {
        if (manageMenu)
        {
            DeactivateMenu(null);
        }

        if (m_collider != null)
        {
            m_collider.enabled = false;
        }

        if (m_visual != null)
        {
            m_visual.SetActive(false);
        }
    }

    #endregion

    #region Pickup Logic

    /// <summary>
    /// Performs the pickup action. Override OnPickupAction for custom pickup behavior.
    /// </summary>
    public virtual async void Pickup()
    {
        if (!m_active)
        {
            return;
        }

        // Disable interaction components
        DisableInteractionComponents(true);

        // Play pickup effect
        if (m_pickupVisual != null)
        {
            m_pickupVisual.Play();
        }

        // Perform the actual pickup action
        OnPickupAction();

        // Invoke pickup event
        m_onPickup?.Invoke();

        // Wait for pickup delay
        await UniTask.WaitForSeconds(m_pickupDisableDelay);

        // Deactivate the pickup
        Deactivate(true);
    }

    /// <summary>
    /// Override this method to implement specific pickup behavior
    /// </summary>
    protected abstract void OnPickupAction();

    #endregion

    #region Activation/Deactivation

    /// <summary>
    /// Activates the pickup
    /// </summary>
    public virtual void Activate()
    {
        Initialize();

        if (m_collider != null)
        {
            m_collider.enabled = true;
        }

        if (m_visual != null)
        {
            m_visual.SetActive(true);
        }

        m_active = true;
        m_onActivated?.Invoke();

        OnActivated();
    }

    /// <summary>
    /// Deactivates the pickup
    /// </summary>
    /// <param name="manageMenu">Whether to manage the contextual menu</param>
    public virtual void Deactivate(bool manageMenu = false)
    {
        // Disable interaction components
        DisableInteractionComponents(manageMenu);

        m_active = false;
        m_onDeactivated?.Invoke();

        OnDeactivated();
    }

    /// <summary>
    /// Override this method for custom activation behavior
    /// </summary>
    protected virtual void OnActivated()
    {
        // Override in derived classes
    }

    /// <summary>
    /// Override this method for custom deactivation behavior
    /// </summary>
    protected virtual void OnDeactivated()
    {
        // Override in derived classes
    }

    #endregion

    #region IPoolable Implementation

    /// <summary>
    /// Activates the poolable object
    /// </summary>
    public virtual void ActivatePoolable()
    {
        gameObject.SetActive(true);
        Activate();
    }

    /// <summary>
    /// Deactivates the poolable object
    /// </summary>
    public virtual void DeactivatePoolable()
    {
        Deactivate(true);
        gameObject.SetActive(false);
        OnDisable?.Invoke();
    }

    /// <summary>
    /// Resets the poolable object to its initial state
    /// </summary>
    /// <param name="resetMovement">Whether to reset movement (unused in base class)</param>
    public virtual void ResetPoolable(bool resetMovement = true)
    {
        // Reset to initial state
        m_active = false;
        m_initialized = false;

        // Reset components
        if (m_collider != null)
        {
            m_collider.enabled = false;
        }

        if (m_visual != null)
        {
            m_visual.SetActive(false);
        }

        if (m_pickupVisual != null && m_pickupVisual.isPlaying)
        {
            m_pickupVisual.Stop();
        }

        // Perform derived class reset
        OnResetPoolable(resetMovement);
    }

    /// <summary>
    /// Override this method for custom reset behavior
    /// </summary>
    /// <param name="resetMovement">Whether to reset movement</param>
    protected virtual void OnResetPoolable(bool resetMovement = true)
    {
        // Override in derived classes
    }

    #endregion
}
