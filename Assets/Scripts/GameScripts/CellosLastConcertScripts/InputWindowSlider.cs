using Cysharp.Threading.Tasks;
using RibCageGames.Base;
using RibCageGames.Music;
using UnityEngine;
using UnityEngine.UI;

public class InputWindowSlider : MonoBehaviour
{
    [SerializeField] private Slider m_slider;
    [SerializeField] private Button m_decreaseButton;
    [SerializeField] private Button m_increaseButton;
    
    [SerializeField] private int m_currentNotch = 10;
    
    [SerializeField] private int m_notches = 20;

    private BeatSystem m_beatSystem;
    
    private void Awake()
    {
        m_beatSystem = ServiceLocator.Get<BeatSystem>();
        m_decreaseButton?.onClick.AddListener(() => ChangeValue(-1));
        m_increaseButton?.onClick.AddListener(() => ChangeValue(1));
    }

    private async void OnEnable()
    {
        if (m_slider != null)
        {
            await UniTask.DelayFrame(1);
            m_slider.value = m_beatSystem.NormalizedInputWindowOffset;
            m_currentNotch = Mathf.RoundToInt(m_beatSystem.NormalizedInputWindowOffset * m_notches);
        }
    }
    
    public void ChangeValue(int sign)
    {
        m_currentNotch += sign > 0 ? 1 : -1;
        m_currentNotch = Mathf.Clamp(m_currentNotch, 0, m_notches);

        float value = (float) m_currentNotch / m_notches;
        m_slider.value = value;
        m_beatSystem.SetInputWindowOffset(value);
    }
}
