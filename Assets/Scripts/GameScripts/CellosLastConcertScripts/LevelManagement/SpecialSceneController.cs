using System.Threading;
using Cysharp.Threading.Tasks;
using UnityEngine;
using UnityEngine.Events;

/// <summary>
/// Abstract base class for special scene controllers that can replace the regular level chunk setup.
/// When a SpecialSceneController prefab is assigned to a LevelChunkBase, it will be spawned instead of running the normal setup logic.
/// </summary>
public abstract class SpecialSceneController : MonoBehaviour
{
    [Header("Special Scene Controller")]
    
    // References that will be set by the level chunk
    protected LevelChunkReferneceHolder m_referenceHolder;
    protected GeneratedLevelChunk m_chunkData;
    protected CancellationToken m_cancellationToken;
    
    /// <summary>
    /// Gets whether the controller has been initialized
    /// </summary>
    public bool IsInitialized { get; private set; }
    
    /// <summary>
    /// Gets the reference holder for this scene
    /// </summary>
    protected LevelChunkReferneceHolder ReferenceHolder => m_referenceHolder;
    
    /// <summary>
    /// Gets the chunk data for this scene
    /// </summary>
    protected GeneratedLevelChunk ChunkData => m_chunkData;
    
    /// <summary>
    /// Gets the cancellation token for this scene
    /// </summary>
    protected CancellationToken CancellationToken => m_cancellationToken;

    public UnityEvent OnSpecialSceneCompleted { get; } = new();

    #region Initialization
    
    /// <summary>
    /// Initializes the special scene controller with the necessary references.
    /// Called by the LevelChunkBase when the controller is spawned.
    /// </summary>
    /// <param name="referenceHolder">The level chunk reference holder</param>
    /// <param name="chunkData">The generated level chunk data</param>
    /// <param name="cancellationToken">Cancellation token for the scene</param>
    public async UniTask Initialize(LevelChunkReferneceHolder referenceHolder, GeneratedLevelChunk chunkData, CancellationToken cancellationToken)
    {
        if (IsInitialized)
        {
            Debug.LogWarning($"SpecialSceneController {name} is already initialized");
            return;
        }
        
        m_referenceHolder = referenceHolder;
        m_chunkData = chunkData;
        m_cancellationToken = cancellationToken;
        
        // Perform custom initialization
        await OnInitialize();
        
        IsInitialized = true;
    }
    
    /// <summary>
    /// Override this method to perform custom initialization logic
    /// </summary>
    protected virtual async UniTask OnInitialize()
    {
        // Override in derived classes
        await UniTask.CompletedTask;
    }
    
    #endregion
    
    #region Scene Control
    
    /// <summary>
    /// Starts the special scene logic. This replaces the normal level chunk setup.
    /// </summary>
    public async UniTask StartScene()
    {
        // Perform the special scene logic
        await OnStartScene();
    }
    
    /// <summary>
    /// Override this method to implement the special scene logic
    /// </summary>
    protected abstract UniTask OnStartScene();
    
    /// <summary>
    /// Called when the scene should be cleaned up
    /// </summary>
    public virtual void CleanupScene()
    {
        OnCleanup();
        
        IsInitialized = false;
    }
    
    /// <summary>
    /// Override this method to perform custom cleanup logic
    /// </summary>
    protected virtual void OnCleanup()
    {
        // Override in derived classes
    }
    
    #endregion
    
    #region Unity Lifecycle
    
    protected virtual void OnDestroy()
    {
        if (IsInitialized)
        {
            CleanupScene();
        }
    }
    
    #endregion
    
    #region Debug Methods
    
    [ContextMenu("Debug: Log State")]
    protected void DebugLogState()
    {
        Debug.Log($"SpecialSceneController {name} State:");
        Debug.Log($"  IsInitialized: {IsInitialized}");
        Debug.Log($"  HasReferenceHolder: {m_referenceHolder != null}");
        Debug.Log($"  HasChunkData: {m_chunkData != null}");
    }
    
    #endregion
}
