using System;
using UnityEngine;
using System.Collections.Generic;
using System.Threading.Tasks;
using RibCageGames.Combat;
using RibCageGames.MonoUtils;
using Random = UnityEngine.Random;

public class VoronoiMeshCellSpawner : MonoBehaviour
{
    [SerializeField] private List<PropLayerSettings> m_propLayers;
    
    [SerializeField] private Transform m_propTestParent;
    
    [SerializeField] private MeshRenderer targetMeshRenderer;
    [SerializeField] private MeshFilter targetMeshFilter;
    [SerializeField] private LayerMask m_raycastLayers;
    [SerializeField] private Vector3 m_isolationOffset;
    [SerializeField] private Vector3 m_centerPoint;
    [SerializeField] private Vector3 m_gridSize;
    [SerializeField] private Vector3 m_cellSize;
    [SerializeField] private Vector3 m_cellOverlapSize;
    [SerializeField] private List<Vector3> cellPositions;
    [SerializeField] private List<Color> cellColors;
    [SerializeField] private List<Vector3> cellNormals;

    private Mesh mesh;
    private Color[] vertexColors;
    private Vector3[] vertices;
    private int[] triangles;
    private List<PropReferenceHolder> m_activeProps = new List<PropReferenceHolder>();

    public async Task PopulateProps()
    {
        await ComputeVoronoiColoredCells();
        PlaceProps();
    }
    
    private void OnDrawGizmosSelected()
    {
        for (int i = 0; i < cellPositions.Count; i++)
        {
            Gizmos.color = cellColors[i];
            Gizmos.DrawSphere(cellPositions[i], 0.3f);
        }
    }

    [ContextMenu(nameof(ComputeVoronoiColoredCells))]
    public async Task ComputeVoronoiColoredCells()
    {
        targetMeshFilter.gameObject.SetActive(false);
        await Task.Yield();
        targetMeshFilter.transform.position += m_isolationOffset;
        await Task.Yield();
        targetMeshFilter.gameObject.SetActive(true);
        
        mesh = targetMeshFilter.sharedMesh;
        vertexColors = mesh.colors;
        vertices = mesh.vertices;
        triangles = mesh.triangles;

        cellPositions = new List<Vector3>();
        cellNormals = new List<Vector3>();
        cellColors = new List<Color>();

        for (var i = 0; i < vertexColors.Length; i++)
        {
            if (vertexColors[i].maxColorComponent > 0.2f)
            {
                if (AddIfNotTooClose(cellPositions,
                        targetMeshFilter.transform.TransformPoint(vertices[i]) - m_isolationOffset, 0.2f))
                {
                    cellColors.Add(vertexColors[i]);
                    cellNormals.Add(Vector3.up);
                }
            }
        }
        
        Vector2Int gridSize = new Vector2Int((int)(m_gridSize.x / m_cellSize.x), (int)(m_gridSize.z / m_cellSize.z));
        
        for (int i = 0; i < gridSize.x; i++)
        {
            for (int j = 0; j < gridSize.y; j++)
            {
                Vector3 baseOffset = new Vector3(
                    ((i - (gridSize.x / 2)) * m_cellSize.x),
                    0f,
                    ((j - (gridSize.y / 2)) * m_cellSize.z));
                Vector3 pos = targetMeshFilter.transform.position + 
                              baseOffset +
                              m_cellOverlapSize.x * (m_cellSize.x * Vector3.right * Random.value) + 
                              m_cellOverlapSize.z * (m_cellSize.z * Vector3.forward * Random.value);
                (Color, Vector3, Vector3) res = GetColorAtWorldPosition(pos);
                if (res.Item1.maxColorComponent > 0.2f)
                {
                    if (AddIfNotTooClose(cellPositions, res.Item2 - m_isolationOffset, 0.2f))
                    {
                        cellColors.Add(res.Item1);
                        cellNormals.Add(res.Item3);
                    }
                }
            }
        }
        
        await Task.Yield();
        targetMeshFilter.transform.position -= m_isolationOffset;
    }

    private bool AddIfNotTooClose(List<Vector3> list, Vector3 newPoint, float minDistance)
    {
        foreach (Vector3 existingPoint in list)
        {
            float sqrDistance = (newPoint - existingPoint).sqrMagnitude;

            if (sqrDistance < minDistance * minDistance)
            {
                return false;
            }
        }
        list.Add(newPoint);
        return true;
    }

    /// <summary>
    /// Gets the interpolated vertex color at a specific world position on the mesh.
    /// Requires a MeshCollider on the GameObject.
    /// </summary>
    /// <param name="worldPosition">The world position on the mesh surface to sample.</param>
    /// <returns>The interpolated Color at the given position, or Color.clear if no hit or no vertex colors.</returns>
    public (Color, Vector3, Vector3) GetColorAtWorldPosition(Vector3 worldPosition)
    {
        if (vertexColors == null || vertexColors.Length == 0)
        {
            Debug.LogWarning("Cannot get color: Mesh does not have vertex colors.");
            return (Color.clear, Vector3.zero, Vector3.zero);
        }

        // We need to perform a raycast to get the hit information, especially the triangle index and barycentric coordinates.
        // For simplicity, we'll cast a ray from above the point downwards.
        // You might need to adjust the ray origin and direction based on your use case.
        Ray ray = new Ray(worldPosition + Vector3.up * 100f, Vector3.down); // Ray from above, pointing down
        RaycastHit hit;

        // Perform the raycast on this GameObject's collider
        if (Physics.Raycast(ray, out hit, 200f, m_raycastLayers,
                QueryTriggerInteraction.Ignore)) // Max distance should be greater than the ray origin offset
        {
            // Make sure the ray hit THIS mesh collider
            if (hit.collider.gameObject == targetMeshRenderer.gameObject)
            {

                // Get the triangle's vertex indices
                int triangleIndex = hit.triangleIndex;

                if (triangleIndex < 0 || triangleIndex * 3 + 2 > triangles.Length)
                {
                    return (Color.clear, Vector3.zero, Vector3.zero);
                }

                int v1Index = triangles[triangleIndex * 3];
                int v2Index = triangles[triangleIndex * 3 + 1];
                int v3Index = triangles[triangleIndex * 3 + 2];

                // Get the colors of the three vertices of the triangle
                Color c1 = vertexColors[v1Index];
                Color c2 = vertexColors[v2Index];
                Color c3 = vertexColors[v3Index];

                // Interpolate the colors using the barycentric coordinates from the hit
                // hit.barycentricCoordinate gives us the (u, v, w) weights
                Color interpolatedColor =
                    c1 * hit.barycentricCoordinate.x +
                    c2 * hit.barycentricCoordinate.y +
                    c3 * hit.barycentricCoordinate.z;

                return (interpolatedColor, hit.point, hit.normal);
                //}
            }
        }

        Debug.LogWarning($"Raycast did not hit this mesh at the given position for {ray.origin}. on {targetMeshFilter.transform.position}");
        return (Color.clear, Vector3.zero, Vector3.zero); // Return clear if no hit or hit a different object
    }

    [ContextMenu("PlaceProps")]
    private void PlaceProps()
    {
        if (!Application.isPlaying)
        {
            List<GameObject> children = new List<GameObject>();
            for (int i = 0; i < m_propTestParent.transform.childCount; i++)
            {
                children.Add(m_propTestParent.GetChild(i).gameObject);
            }

            foreach (GameObject child in children)
            {
                DestroyImmediate(child);
            }
        }

        // Track spawned positions for each layer to check minimal distance
        Dictionary<PropLayerSettings, List<Vector3>> layerSpawnedPositions = new Dictionary<PropLayerSettings, List<Vector3>>();
        foreach (PropLayerSettings layer in m_propLayers)
        {
            layerSpawnedPositions[layer] = new List<Vector3>();
        }

        for (var i = 0; i < cellPositions.Count; i++)
        {
            Vector3 position = cellPositions[i];
            Color color = cellColors[i];

            float selection = Random.value;
            foreach (PropLayerSettings propLayer in m_propLayers)
            {
                if (IsAcceptedForLayer(color, propLayer.LayerVertexColor))
                {
                    if (propLayer.SelectionChance > Random.value)
                    {
                        if (IsTooCloseToExistingSpawns(position, propLayer, layerSpawnedPositions[propLayer]))
                        {
                            continue; // Skip this spawn, too close to existing props
                        }
                        
                        foreach (PropSelectionRange prop in propLayer.Props)
                        {
                            if (prop.PropSelectionRangeMax > selection && prop.PropSelectionRangeMin < selection)
                            {
                                Quaternion rotation =
                                    Quaternion.Euler(
                                        Mathf.Lerp(prop.MinEular.x, prop.MaxEular.x, Random.value),
                                        Mathf.Lerp(prop.MinEular.y, prop.MaxEular.y, Random.value),
                                        Mathf.Lerp(prop.MinEular.z, prop.MaxEular.z, Random.value));
                                
                                Vector3 scale = Vector3.one
                                                * Mathf.Lerp(prop.PropScaleMin, prop.PropScaleMax, Random.value)
                                                * Mathf.Lerp(prop.PropScaleMinColorMultiplier, 1f, color.maxColorComponent);
                                
                                if (Application.isPlaying)
                                {
                                    PropReferenceHolder propInstance = prop.Pool.GetInstance();
                                    propInstance.transform.position = position;
                                    if (prop.AllighToGroundNormal)
                                    {
                                        propInstance.transform.up = cellNormals[i];
                                    }
                                    propInstance.transform.rotation *= rotation;
                                    propInstance.transform.localScale = scale;
                                    propInstance.gameObject.SetActive(true);
                                    m_activeProps.Add(propInstance);
                                }
                                else
                                {
                                    PropReferenceHolder propInstance = Instantiate(prop.Pool.Prefab, position, rotation, m_propTestParent);
                                    if (prop.AllighToGroundNormal)
                                    {
                                        propInstance.transform.up = cellNormals[i];
                                    }
                                    propInstance.transform.rotation *= rotation;
                                    propInstance.transform.localScale = scale;
                                }

                                // Add this position to the layer's spawned positions for distance checking
                                layerSpawnedPositions[propLayer].Add(position);

                                //Debug.LogError($"Spawning prop on {position} and breaking");
                                //Debug.LogError($"Spawning prop on {position} at index [{i}]");

                                break;
                            }
                        }
                        
                        break;
                    }
                }
            }
        }
    }

    private bool IsAcceptedForLayer(Color sampleColor, Color referenceColor)
    {
        return (sampleColor.r / sampleColor.maxColorComponent > 0.9f && referenceColor.r > 0.5f)
               || (sampleColor.g / sampleColor.maxColorComponent > 0.9f && referenceColor.g > 0.5f)
               || (sampleColor.b / sampleColor.maxColorComponent > 0.9f && referenceColor.b > 0.5f);
    }

    /// <summary>
    /// Checks if the proposed position is too close to any existing spawns in the same layer
    /// </summary>
    /// <param name="proposedPosition">The position where we want to spawn a new prop</param>
    /// <param name="layer">The layer settings containing the minimal distance</param>
    /// <param name="existingPositions">List of already spawned positions in this layer</param>
    /// <returns>True if too close to existing spawns, false if position is valid</returns>
    private bool IsTooCloseToExistingSpawns(Vector3 proposedPosition, PropLayerSettings layer, List<Vector3> existingPositions)
    {
        float minDistanceSquared = layer.MinimalDistance * layer.MinimalDistance; // Use squared distance for performance

        foreach (Vector3 existingPosition in existingPositions)
        {
            float distanceSquared = (proposedPosition - existingPosition).sqrMagnitude;
            if (distanceSquared < minDistanceSquared)
            {
                return true; // Too close to an existing spawn
            }
        }

        return false; // Position is valid
    }

    private void OnDestroy()
    {
        foreach (PropReferenceHolder prop in m_activeProps)
        {
            prop.OnDisable?.Invoke();
        }
    }

    private void OnValidate()
    {
        foreach (PropLayerSettings layer in m_propLayers)
        {
            if (layer.NormalizeSelectionChance)
            {
                for (var i = 0; i < layer.Props.Count; i++)
                {
                    PropSelectionRange prop = layer.Props[i];
                    prop.PropSelectionRangeMin = i * (1f / layer.Props.Count);
                    prop.PropSelectionRangeMax = (i + 1) * (1f / layer.Props.Count);
                    prop.MaxEular = new Vector3(0f, 180f, 0f);
                    prop.MinEular = new Vector3(0f, -180f, 0f);
                }
            }
        }
    }

    [Serializable]
    private class PropLayerSettings
    {
        [SerializeField] private Color m_layerVertexColor;
        [SerializeField] private bool m_normalizeSelectionChance;
        [SerializeField][Range(0f, 1f)] private float m_selectionChance;
        [SerializeField] private float m_minimalDistance;
        [SerializeField] private List<PropSelectionRange> m_props;
        
        public Color LayerVertexColor => m_layerVertexColor;
        public float SelectionChance => m_selectionChance;
        public bool NormalizeSelectionChance => m_normalizeSelectionChance;
        public List<PropSelectionRange> Props => m_props;
        public float MinimalDistance => m_minimalDistance;
    }
    
    [Serializable]
    private class PropSelectionRange
    {
        [SerializeField][Range(0f, 1f)] private float m_propSelectionRangeMin;
        [SerializeField][Range(0f, 1f)] private float m_propSelectionRangeMax;
        [SerializeField] private float m_propScaleMinColorMultiplier = 0.1f;
        [SerializeField] private float m_propScaleMin = 1f;
        [SerializeField] private float m_propScaleMax = 1f;
        [SerializeField] private PropPrefabPool m_pool;
        [SerializeField] private Vector3 m_minEular;
        [SerializeField] private Vector3 m_maxEular;
        [SerializeField] private bool m_allighToGroundNormal = true;
        
        public float PropSelectionRangeMin
        {
            get => m_propSelectionRangeMin;
            set => m_propSelectionRangeMin = value;
        }

        public float PropSelectionRangeMax
        {
            get => m_propSelectionRangeMax;
            set => m_propSelectionRangeMax = value;
        }
        
        public Vector3 MinEular
        {
            get => m_minEular;
            set => m_minEular = value;
        }

        public Vector3 MaxEular
        {
            get => m_maxEular;
            set => m_maxEular = value;
        }

        public float PropScaleMinColorMultiplier => m_propScaleMinColorMultiplier;
        public float PropScaleMin => m_propScaleMin;
        public float PropScaleMax => m_propScaleMax;
        public bool AllighToGroundNormal => m_allighToGroundNormal;
        public PropPrefabPool Pool => m_pool;
    }
}