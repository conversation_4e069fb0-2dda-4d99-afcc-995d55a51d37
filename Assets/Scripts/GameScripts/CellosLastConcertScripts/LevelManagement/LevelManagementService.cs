using System;
using System.Collections.Generic;
using System.Linq;
using Cysharp.Threading.Tasks;
using RibCageGames.Base;
using RibCageGames.Music;
using RibCageGames.UI;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.SceneManagement;

#if UNITY_EDITOR
using UnityEditor;
#endif

[CreateAssetMenu(fileName = "LevelManagementService", menuName = "RibCageGames/Services/LevelManagementService")]
public class LevelManagementService : BaseService
{
    [SerializeField] private bool m_playMusicOnLevelLoad;
    [SerializeField] private int m_firstLevelIndex = 0;
    
    //[SerializeField] private bool m_startFromPoolLevel;
    
    [SerializeField] private bool m_useDemoTimer;
    [SerializeField] private List<LevelPersistantElement> m_levelPersistantElement; 
    [SerializeField] private List<SceneReference> m_scenes;
    [SerializeField] private LevelChunkBase m_hubLevelChunk;
    [SerializeField] private LevelSelectionPool m_tutorialLevelPool;
    [SerializeField] private LevelSelectionPool m_levelPool;

    [Header("Fade settings")] [SerializeField]
    private float m_fadeInDuration = 1f;

    [SerializeField] private AnimationCurve m_fadeInCurve;
    [SerializeField] private float m_fadeOutDuration = 2f;
    [SerializeField] private AnimationCurve m_fadeOutCurve;

    private SceneReference m_currentlyLoaded = null;
    [NonSerialized] private bool m_tutorialPassed = false;
    [SerializeField] private bool m_skipTutorial = false;
    private bool m_loading;
    private bool m_playCinematics = true;
    private bool m_autoLoadCore = true;
    private ScreenFade m_screenFade;
    private float m_timeInGame;
    private int m_deaths;
    
    private LevelSelectionPool LevelPool => m_tutorialPassed || m_skipTutorial ? m_levelPool: m_tutorialLevelPool;

    public ScreenFade ScreenFade => m_screenFade;
    public float TimeInGame => m_timeInGame;
    public bool PlayCinematics => GetPlayCinematics();
    public bool AutoLoadCore => GetAutoLoadCore();
    public bool IsLoading
    {
        get => m_loading;
        set => m_loading = value;
    }
    public SceneReference CurrentlyLoaded => m_currentlyLoaded;
    public List<SceneReference> ScenesReferences => m_scenes;
    public int CurrentlyLoadedIndex => m_scenes.IndexOf(m_currentlyLoaded);
    public string[] ScenesReferenceNames => m_scenes.Select(sr => sr.ScenePath).ToArray();
    public string[] FriendlyScenesReferenceNames => ScenesReferenceNames.Select(s => s.Split('/').Last().Split('.').First()).ToArray();
    public UnityEvent OnLevelReset { get; } = new UnityEvent();

    //TODO: use for in-scene play start for testing
    private int m_sceneToLoad;

    public override void Init(GameObject servicePrefab = null)
    {
        m_screenFade = servicePrefab.GetComponent<ScreenFade>();
        
        if (!m_autoLoadCore) { return; }
        m_sceneToLoad = m_firstLevelIndex;
    }

    public void ActivateLoadedScene(Scene scene)
    {
        if (scene.buildIndex != 0)
        {
            m_currentlyLoaded = m_scenes.Find(sc => sc.ScenePath.Equals(scene.path));
            //m_screenFade.PerformFadeAsync(1f, 0f, m_fadeInDuration, m_fadeInCurve).Forget();
            SceneManager.SetActiveScene(scene);
            OnLevelReset?.Invoke();
        }
    }

    private PopupService m_popupService;
    public override void StartService(MonoInjector injector)
    {
        if (!m_autoLoadCore) { return; }

        m_popupService = ServiceLocator.Get<PopupService>();
        ServiceLocator.Get<MonoService>().OnUpdate.AddListener((delta) =>
        {
            m_timeInGame += delta;
            m_popupService.UpdateUITime(m_timeInGame);
        });
        
        m_screenFade.SetFade(1f);
        m_loading = false;
        m_currentlyLoaded = null;
        //LoadFirstScene();

        //if (m_startFromPoolLevel)
        //{
        //    LoadFirstPathScene();
        //}
        //else
        //{
            //LoadScene(m_sceneToLoad).Forget();
        //}
        
        LoadFirstScene();
    }

    private async void LoadFirstScene()
    {
        await LoadScene(m_sceneToLoad);
        FadeIn();
    }

    private async UniTask LoadScene(int index) // -1 used for soft reloading current level
    {
        await SceneManager.LoadSceneAsync(ScenesReferences[index], LoadSceneMode.Additive);

        ActivateLoadedScene(SceneManager.GetSceneByPath(ScenesReferences[index].ScenePath));
    }
    
    public void LoadBaseWorld()
    {
        //Load hub
        if (m_tutorialPassed || m_skipTutorial)
        {
            LoadHub();
        }
        //Load game directly
        else
        {
            LoadFirstPathScene();
        }
    }

    public void LoadFirstPathScene()
    {
        m_timeInGame = 0f;
        m_deaths = 0;

        if (m_playMusicOnLevelLoad)
        {
            //TODO: play music manually
        }

        Debug.LogError($"Creating worlds from {LevelPool.name}");
        LevelPool.CreateWorldPaths(null);

        LevelPool.LoadFirstChunk();
        //if (m_sceneToLoad == m_levelChunkQueue.Peek().chunk.ContainingLevelIndex)
        //{
        //    LoadNextLevel(false);   
        //}
        //else
        //{
        //    LoadLevel(m_sceneToLoad);
        //}
    }

    //public void LoadNextLevel()
    //{
    //    ServiceLocator.Get<InputService>().DisableInput();
    //    LoadNextLevel(false);
    //}

    public List<(GeneratedLevelChunk, Func<Transform ,bool, UniTask>)> GetLevelChoices()
    {
        m_lastLevelLoaded = LevelPool.GetLevelChoices(m_scenes.IndexOf(m_currentlyLoaded));
        //Debug.LogError($"Get level choices includes {m_lastLevelLoaded[0].Item1.ChunkAsset.name}");
        return m_lastLevelLoaded;
    }

    List<(GeneratedLevelChunk, Func<Transform ,bool, UniTask>)> m_lastLevelLoaded;

    public void LoadNextLevel()
    {
        if (m_lastLevelLoaded == null || m_lastLevelLoaded.Count < 1)
        {
            GetLevelChoices();
        }
        m_lastLevelLoaded[0].Item2.Invoke(null, false);
    }
    
    public override void Dispose()
    {
        m_currentlyLoaded = null;
    }
    
    public void SetInitialScene(Scene scene)
    {
        m_sceneToLoad = m_scenes.FindIndex(sc => sc.ScenePath.Equals(scene.path));
    }

    public void SetPlayCinematic(bool playCinematic)
    {
        m_playCinematics = playCinematic;
    }
    
    public void SetAutoLoadCore(bool autoLoad)
    {
        m_autoLoadCore = autoLoad;
    }

    private bool GetPlayCinematics()
    {
#if UNITY_EDITOR
        return m_playCinematics;
#else
        return true;
#endif
    }
    
    private bool GetAutoLoadCore()
    {
#if UNITY_EDITOR
        return m_autoLoadCore;
#else
        return true;
#endif
    }
    
    public void ReloadDemo()
    {
        m_popupService.DeactivateMainGameUI();
        ServiceLocator.Get<BeatSystem>().DemoReset();
        ServiceLocator.Get<PlayerService>().ClearPowerUps();
        ServiceLocator.Get<PlayerService>().DeactivatePlayer();
        LoadFirstPathScene();
    }

    public async void LoadLevel(int index) // -1 used for soft reloading current level
    {
        if (m_loading)
        {
            return;
        }
        
        m_loading = true;
        
        if (m_currentlyLoaded != null)
        {
            OnLevelReset?.Invoke();
            SceneManager.UnloadSceneAsync(m_currentlyLoaded);
        }
        
        await SceneManager.LoadSceneAsync(m_scenes[index], LoadSceneMode.Additive);

        ActivateLoadedScene(SceneManager.GetSceneByPath(m_scenes[index].ScenePath));
        m_loading = false;
        //m_screenFade.PerformFadeAsync(1f, 0f, m_fadeInDuration, m_fadeInCurve).Forget();
    }

    private void ResetPlayer()
    {
        PlayerService ps = ServiceLocator.Get<PlayerService>(); //TODO: cache this
        ps.ClearPowerUps();
        ps.ResetPlayerPreviousHealth();
        ps.PlayerController.ResetCombatEntity(true, true);
    }

    public void PlayerDied()
    {
        //await ScreenFade.PerformFadeAsync(0f, 1f);
        //await m_lastLevelLoaded[0].Item2.Invoke(ServiceLocator.Get<PlayerService>().PlayerController.transform, true);
        //LoadFirst level
        m_tutorialPassed = true;
        //m_hubLevelChunk
            
        GeneratedLevelChunk hubChunk = new GeneratedLevelChunk();
        hubChunk.m_chunkAsset = m_hubLevelChunk;
        hubChunk.m_combatLevel = 1;
        //hubChunk.m_itemPool = m_item;
        //hubChunk.ChunkReward = PickupType.RandomSelection;
        m_hubLevelChunk.LoadChunkAsset(hubChunk, true, false, Vector3.zero, Vector3.zero);
        //LoadFirstPathScene();
        //await ScreenFade.PerformFadeAsync(1f, 0f);
    }

    private void LoadHub()
    {
        GeneratedLevelChunk hubChunk = new GeneratedLevelChunk();
        hubChunk.m_chunkAsset = m_hubLevelChunk;
        hubChunk.m_combatLevel = 0;
        m_hubLevelChunk.LoadChunkAsset(hubChunk, true, false, Vector3.zero, Vector3.zero);
    }

    public LevelChunkReferneceHolder LevelReferneceHolder { get; private set; }

    public async void NotifyReferenceHolderCreated(LevelChunkReferneceHolder levelChunkReferneceHolder)
    {
        LevelReferneceHolder = levelChunkReferneceHolder;

        await UniTask.WaitForSeconds(1f);
        
        LevelReferneceHolder?.OnSceneLoadedEvent.Invoke();
    }

    public EncounterSettings GetCombatEncounter(int combatLevel)
    {
        return LevelPool.GetEncounterByLevel(combatLevel);
    }

    public void FadeIn()
    {
        ScreenFade.PerformFadeAsync(1f, 0f, m_fadeInDuration, m_fadeInCurve).Forget();
    }
}

#if UNITY_EDITOR

//Make a base class for this in Editor asmdef
[CustomEditor(typeof(LevelManagementService))]
public class LevelManagementServiceEditor : Editor
{
    
    //TODO: use this for default properties: 
    /*
    using UnityEngine;
    using UnityEditor;

    public static class SerializedObjectExtensions
    {
        public static IEnumerable<SerializedProperty> GetPropertiesWithAttribute<T>(this SerializedObject serializedObject) where T : Attribute
        {
            var properties = serializedObject.GetIterator();
            properties.Next(true);
    
            while (properties.Next(false))
            {
                var property = properties.Copy();
                var fieldInfo = property.serializedObject.targetObject.GetType().GetField(property.name);
    
                if (fieldInfo != null && fieldInfo.GetCustomAttributes(typeof(T), true).Length > 0)
                {
                    yield return property;
                }
            }
        }
    }
     */
    
    private SerializedProperty m_firstLevelIndexProperty;
    private List<SerializedProperty> m_defaultProperties;
    private LevelManagementService m_target;

    private void OnEnable()
    {
        m_firstLevelIndexProperty = serializedObject.FindProperty("m_firstLevelIndex");

        m_defaultProperties = new List<SerializedProperty>();

        m_defaultProperties.Add(serializedObject.FindProperty("m_playMusicOnLevelLoad"));
        m_defaultProperties.Add(serializedObject.FindProperty("m_useDemoTimer"));
        m_defaultProperties.Add(serializedObject.FindProperty("m_levelPersistantElement"));
        m_defaultProperties.Add(serializedObject.FindProperty("m_scenes"));
        m_defaultProperties.Add(serializedObject.FindProperty("m_levelPool"));
        m_defaultProperties.Add(serializedObject.FindProperty("m_tutorialLevelPool"));
        m_defaultProperties.Add(serializedObject.FindProperty("m_skipTutorial"));
        m_defaultProperties.Add(serializedObject.FindProperty("m_hubLevelChunk"));
        m_defaultProperties.Add(serializedObject.FindProperty("m_fadeInDuration"));
        m_defaultProperties.Add(serializedObject.FindProperty("m_fadeInCurve"));
        m_defaultProperties.Add(serializedObject.FindProperty("m_fadeOutDuration"));
        m_defaultProperties.Add(serializedObject.FindProperty("m_fadeOutCurve"));


        m_target = (LevelManagementService)target;
    }

    public override void OnInspectorGUI()
    {
        serializedObject.Update();
                
        m_firstLevelIndexProperty.intValue =
            EditorGUILayout.Popup(new GUIContent("First Level Loaded"), m_firstLevelIndexProperty.intValue, m_target.FriendlyScenesReferenceNames);

        //ShowList(serializedObject.FindProperty("m_levelChunks"));
        
        foreach (SerializedProperty property in m_defaultProperties)
        {
            EditorGUILayout.PropertyField(property);
        }
        
        serializedObject.ApplyModifiedProperties();
    }
    
    public void ShowList (SerializedProperty list, bool showListSize = true, bool showListLabel = true, bool showElementLabels = true) {
        if (showListLabel) {
            //EditorGUILayout.PropertyField(list, false);
            //list.isExpanded =
            list.isExpanded = EditorGUILayout.Foldout(list.isExpanded, list.displayName);
            EditorGUI.indentLevel += 1;
        }
        if (!showListLabel || list.isExpanded) {
            if (showListSize) {
                EditorGUILayout.PropertyField(list.FindPropertyRelative("Array.size"));
            }
            ShowElements(list, showElementLabels);
        }
        if (showListLabel) {
            EditorGUI.indentLevel -= 1;
        }
    }
    
    private void ShowElements (SerializedProperty list, bool showElementLabels) {

        for (int i = 0; i < list.arraySize; i++) {
            //if (showElementLabels) {
                //EditorGUILayout.PropertyField(list.GetArrayElementAtIndex(i));
            //}
            //else {
                //EditorGUILayout.PropertyField(list.GetArrayElementAtIndex(i), GUIContent.none);
            //}
            EditorGUILayout.PropertyField(list.GetArrayElementAtIndex(i));

            //SerializedProperty chunkIndexProp = list.GetArrayElementAtIndex(i).FindPropertyRelative("m_sceneChunkIndex");
            SerializedProperty sceneIndexProp = list.GetArrayElementAtIndex(i).FindPropertyRelative("m_sceneIndex");
            
            sceneIndexProp.intValue = EditorGUILayout.Popup(new GUIContent("Containing scene"), sceneIndexProp.intValue, m_target.FriendlyScenesReferenceNames);
            //EditorGUILayout.PropertyField(sceneIndexProp);
        }
        
        
        //[SerializeField] private int m_sceneIndex;
        //[SerializeField] private int m_sceneChunkIndex;
    }
}
#endif
