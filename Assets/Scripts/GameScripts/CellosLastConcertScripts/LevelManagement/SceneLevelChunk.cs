using Cysharp.Threading.Tasks;
using RibCageGames.Base;
using RibCageGames.Input;
using UnityEngine;
using UnityEngine.SceneManagement;

[CreateAssetMenu(fileName = "SceneLevelChunk", menuName = "AlphaNomos/Settings/SceneLevelChunk")]
public class SceneLevelChunk : LevelChunkBase
{
    public override async UniTask LoadChunkAsset(GeneratedLevelChunk chunkData,
        bool firstChunk, bool reload, Vector3 stitchPosition, Vector3 stitchVector)
    {
        await LoadChunkScene(firstChunk, reload);
        
        await UniTask.WaitUntil(() => m_levelManagementService.Value.LevelReferneceHolder != null);
        
        m_levelManagementService.Value.LevelReferneceHolder.SetChunkAsset(this);
        
        PlaceChunkAsset(stitchPosition, stitchVector);
        SetupLevelChunk(m_levelManagementService.Value.LevelReferneceHolder, chunkData).Forget();
        m_levelManagementService.Value.NotifyReferenceHolderCreated(null);
    }

    protected async UniTask LoadChunkScene(bool firstChunk, bool reload)
    {
        await m_levelManagementService.Value.ScreenFade.PerformFadeAsync(0f, 1f);
        
        if (!reload)
        {
            OnSceneReferencesLoaded.AddSingleUseListener(() =>
            {
                if (PlayerSpawnPosition != null)
                {
                    ServiceLocator.Get<PlayerService>().PositionPlayer(PlayerSpawnPosition.transform);
                }
                ServiceLocator.Get<InputService>().EnableInput();
                OnSceneLoadedEvent?.Invoke();
            });
            await LoadScene(ContainingLevelIndex);
        }
        else
        {
            await SetupLevelChunk(null); //Reloads the chunk simulating a regular load, used primarily for demos
            OnSceneLoadedEvent?.Invoke();
        }
    }

    public async UniTask LoadScene(int index) // -1 used for soft reloading current level
    {
        if (m_levelManagementService.Value.CurrentlyLoaded != null)
        {
            m_levelManagementService.Value.OnLevelReset?.Invoke();
            SceneManager.UnloadSceneAsync(m_levelManagementService.Value.CurrentlyLoaded);
        }
        await SceneManager.LoadSceneAsync(m_levelManagementService.Value.ScenesReferences[index], LoadSceneMode.Additive);
        m_levelManagementService.Value.ActivateLoadedScene(SceneManager.GetSceneByPath(m_levelManagementService.Value.ScenesReferences[index].ScenePath));
    }

    public override void PlaceChunkAsset(Vector3 stitchPosition, Vector3 stitchVector)
    {
    }
}
