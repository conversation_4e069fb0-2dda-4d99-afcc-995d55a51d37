using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

[CreateAssetMenu(fileName = "ItemGenerationPoolAsset", menuName = "RibCageGames/AlphaNomos/ItemGenerationPoolAsset")]
public class ItemGenerationPoolAsset : ScriptableObject
{
    [SerializeField] private ItemGenerationPool m_pool;
    public ItemGenerationPool Pool => m_pool;
}

[Serializable]
public class ItemGenerationPool
{
    
}