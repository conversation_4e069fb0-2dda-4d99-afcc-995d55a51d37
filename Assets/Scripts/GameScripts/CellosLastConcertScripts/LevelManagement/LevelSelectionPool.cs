using System;
using System.Collections.Generic;
using System.Linq;
using Cysharp.Threading.Tasks;
using RibCageGames.Base;
using RibCageGames.MonoUtils;
using UnityEngine;
using UnityEngine.Serialization;
using ZLinq;
using Random = UnityEngine.Random;

[CreateAssetMenu(fileName = "LevelSelectionPool", menuName = "RibCageGames/Settings/LevelSelectionPool")]
public class LevelSelectionPool : MonoScriptableObject
{
    [SerializeField] private ProceduralChunkPool m_proceduralChunkPoolPast;
    [SerializeField] private ProceduralChunkPool m_proceduralChunkPoolFuture;
    [SerializeField] private LevelChunkBase m_bossChunk;
    [SerializeField] private LevelChunkBase m_startChunk;
    [SerializeField] private ItemPool m_itemPool;
    [SerializeField] private List<CombatEncounterList> m_combatEncounters;
    
    [SerializeField] private int m_maxGenericChunks;
    [SerializeField] private int m_minGenericChunks;
    
    [SerializeField] private List<RewardOptionSet> m_possibleRewards;
    [SerializeField] private List<RewardOptionSet> m_possibleSecrets;
    
    
    [SerializeField] private int m_specificChunkDistributionDelta = 1;
    
    [FormerlySerializedAs("m_testInjectionChunks")]
    [Header("Testing")]
    [SerializeField] private List<LevelChunkBase> m_injectionChunks;
    [SerializeField] private bool m_useFixedChunks;
    
    public LevelChunkBase StartChunk => m_startChunk;
    
    public List<RewardOptionSet> PossibleRewards => m_possibleRewards;
    public List<RewardOptionSet> PossibleSecrets => m_possibleSecrets;
    
    private WorldPathChunkList m_currentFutureChunks;
    private WorldPathChunkList m_currentPastChunks;

    public override void Initialize() {}

    public void CreateWorldPaths(List<LevelChunkBase> injectedChunks)
    {
        if (m_maxGenericChunks < 1)
        {
            m_currentPastChunks = new WorldPathChunkList(MakePath(m_proceduralChunkPoolPast, 0, m_useFixedChunks ? m_injectionChunks : injectedChunks));
            m_currentFutureChunks = new WorldPathChunkList(MakePath(m_proceduralChunkPoolFuture, 0, m_useFixedChunks ? m_injectionChunks : injectedChunks));
        }
        else
        {
            (List<LevelChunkBase>, List<LevelChunkBase>) specificChunkGroups =
                SplitListWithDeltaControl(m_useFixedChunks ? m_injectionChunks : injectedChunks, m_specificChunkDistributionDelta);

            m_currentPastChunks =
                new WorldPathChunkList(MakePath(m_proceduralChunkPoolPast, Random.Range(m_minGenericChunks, m_maxGenericChunks),
                    specificChunkGroups.Item1));
            m_currentFutureChunks =
                new WorldPathChunkList(MakePath(m_proceduralChunkPoolFuture, Random.Range(m_minGenericChunks, m_maxGenericChunks),
                    specificChunkGroups.Item2));
        }
    }

    [ContextMenu("DebugWorldPaths")]
    public void DebugWorldPaths()
    {
        CreateWorldPaths(m_injectionChunks);

        string debug = $"Past list ({m_currentPastChunks.ChunkList.Count}): ";
        foreach (GeneratedLevelChunk chunk in m_currentPastChunks.ChunkList)
        {
            debug += chunk.ChunkAsset.name + ", ";
        }

        debug += $"\nFuture list ({m_currentFutureChunks.ChunkList.Count}): ";
        foreach (GeneratedLevelChunk chunk in m_currentFutureChunks.ChunkList)
        {
            debug += chunk.ChunkAsset.name + ", ";
        }
        Debug.LogError(debug);
    }
    
    public List<(GeneratedLevelChunk targetChunk, Func<Transform ,bool, UniTask> portalAction)> GetLevelChoices(int currentlyLoadedIndex)
    {
        List<(GeneratedLevelChunk targetChunk, Func<Transform ,bool, UniTask> portalAction)> chunks = new List<(GeneratedLevelChunk targetChunk, Func<Transform ,bool, UniTask> portalAction)>();

        //Control reward choices properly, not reward every time
        //List<PickupType> rewards = m_itemPool.GetRewardChoices(2);
        
        if (m_currentFutureChunks.HasChunk)
        {
            GeneratedLevelChunk chunk = m_currentFutureChunks.NextChunk;
            //chunk.ChunkReward = rewards[0];
            chunks.Add((chunk, async (portalTransform, reset) =>
            {
                //Debug.LogError($"Load level: {chunk.ChunkAsset.name}");
                bool firstChunk = chunk.ChunkAsset.ContainingLevelIndex != currentlyLoadedIndex;
                if (!reset)
                {
                    m_currentFutureChunks.ChunkSelected();
                }
                
                Vector3 position = Vector3.zero;
                Vector3 forward = Vector3.forward;

                if (portalTransform != null)
                {
                    position = portalTransform.position;
                    forward = portalTransform.forward;
                }
                
                await chunk.ChunkAsset.LoadChunkAsset(chunk, firstChunk, reset, position, forward);
            }));
        }
        
        if (m_currentPastChunks.HasChunk)
        {
            GeneratedLevelChunk chunk = m_currentPastChunks.NextChunk;
            //chunk.m_chunkReward = rewards[Mathf.Min(1, rewards.Count - 1)];
            chunks.Add((chunk, async (portalTransform, reset) =>
            {
                //Debug.LogError($"Load level: {chunk.ChunkAsset.name}");
                bool firstChunk = chunk.ChunkAsset.ContainingLevelIndex != currentlyLoadedIndex;

                if (!reset)
                {
                    m_currentPastChunks.ChunkSelected();
                }

                Vector3 position = Vector3.zero;
                Vector3 forward = Vector3.forward;

                if (portalTransform != null)
                {
                    position = portalTransform.position;
                    forward = portalTransform.forward;
                }
                await chunk.ChunkAsset.LoadChunkAsset(chunk,
                    firstChunk, reset, position, forward);
                //await chunk.ChunkAsset.LoadChunkAsset(null, chunk.CombatLevel, rewards[1], firstChunk, reset, position, forward);
            }));
        }

        if (chunks.Count < 2)
        {
            var generatedBossChunk = new GeneratedLevelChunk { m_chunkAsset = m_bossChunk, m_combatLevel = 0, m_itemPool = m_itemPool, };
            chunks.Add((generatedBossChunk, async (portalTransform, reset) =>
            {
                bool firstChunk = m_bossChunk.ContainingLevelIndex != currentlyLoadedIndex;
                Vector3 position = Vector3.zero;
                Vector3 forward = Vector3.forward;

                if (portalTransform != null)
                {
                    position = portalTransform.position;
                    forward = portalTransform.forward;
                }
                
                await m_bossChunk.LoadChunkAsset(generatedBossChunk, true, reset, position, forward);
                if (!firstChunk)
                {
                    //m_bossChunk.PlaceChunkAsset(portalTransform.position, portalTransform.forward);
                }
            }));
        }

        return chunks;
    }

    public EncounterSettings GetEncounterByLevel(int level)
    {
        return m_combatEncounters[Mathf.Clamp(level, 0, m_combatEncounters.Count)].RandomEncounter;
    }
    
    private List<GeneratedLevelChunk> MakePath(ProceduralChunkPool pool, int genericChunkNumber, List<LevelChunkBase> specificChunks)
    {
        List<GeneratedLevelChunk> resultList = new List<GeneratedLevelChunk>();
        
        string debugPath = "generated path is:";
        if (genericChunkNumber < 1)
        {
            foreach (LevelChunkBase chunk in m_injectionChunks)
            {
                resultList.Add(new GeneratedLevelChunk
                {
                    m_chunkAsset = chunk,
                    m_combatLevel = 0,
                    m_itemPool = m_itemPool,
                    //m_chunkRewardSet = null,
                });
                debugPath += chunk.name;
            }
            Debug.Log(debugPath);
            return resultList;
        }

        int specificChunksAdded = 0;
        int nextSpecificChunkPosition = Random.Range(0, genericChunkNumber / 2);
        
        for (int i = 0; i < genericChunkNumber; i++)
        {
            int combatLevel =
                Mathf.RoundToInt(((float)i / genericChunkNumber).CastToRange(0f, 1f, 0f, m_combatEncounters.Count));

            Debug.LogError($"Set combat level to {combatLevel} from {i} and {genericChunkNumber} and {m_combatEncounters.Count}");
            GeneratedLevelChunk generatedChunk = new GeneratedLevelChunk
            {
                m_chunkAsset = pool.RandomChunk,
                m_combatLevel = combatLevel,
                m_itemPool = m_itemPool,
                m_chunkRewardSet = m_possibleRewards[i + 1],
                m_chunkSecretRewardSet = m_possibleSecrets[i + 1],
                //m_chunkReward = PickupType.RandomSelection,
            };
            resultList.Add(generatedChunk);
            
            debugPath += generatedChunk.ChunkAsset.name;
            
            if (i == nextSpecificChunkPosition && specificChunks != null && specificChunks.Count > 0 && specificChunksAdded <= specificChunks.Count)
            {
                debugPath += specificChunks[0].name;
                resultList.Add(new GeneratedLevelChunk
                {
                    m_chunkAsset = specificChunks[0],
                    m_combatLevel = combatLevel,
                    m_itemPool = m_itemPool,
                });
                debugPath += specificChunks[0].name;
                specificChunks.RemoveAt(0);
                specificChunksAdded++;
                nextSpecificChunkPosition = Random.Range(i, genericChunkNumber);
            }
        }

        Debug.LogError(debugPath);
        return resultList;
    }
    
    private (List<T>, List<T>) SplitListWithDeltaControl<T>(List<T> originalList, int maxDelta)
    {
        if (originalList == null)
        {
            return (new List<T>(), new List<T>());
        }

        int originalCount = originalList.Count;
        if (originalCount == 0)
        {
            return (new List<T>(), new List<T>());
        }

        List<T> list1 = new List<T>();
        List<T> list2 = new List<T>();
        List<T> tempOriginal = originalList.OrderBy(x => Random.value).ToList(); // Create a shuffled copy

        int list1Count = 0;
        int list2Count = 0;

        foreach (var item in tempOriginal)
        {
            if (list1Count <= list2Count && (list2Count - list1Count) < maxDelta)
            {
                list1.Add(item);
                list1Count++;
            }
            else
            {
                list2.Add(item);
                list2Count++;
            }
        }

        return (list1, list2);
    }

    public void LoadFirstChunk()
    {
        GeneratedLevelChunk firstChunk = new GeneratedLevelChunk();
        firstChunk.m_chunkAsset = StartChunk;
        firstChunk.m_combatLevel = 0;
        firstChunk.m_itemPool = m_itemPool;
        firstChunk.m_chunkRewardSet = PossibleRewards[0];
        firstChunk.m_chunkSecretRewardSet = PossibleSecrets[0];
        StartChunk.LoadChunkAsset(firstChunk, true, false, Vector3.zero, Vector3.zero);
        //StartChunk.ActivateEndOfChunk();
    }
}

[Serializable]
public class RewardOptionSet
{
    [SerializeField] private List<RewardOption> m_rewardOptions;
    public List<RewardOption> RewardOptions => m_rewardOptions;
    
    internal PickupType GetRandomReward(ItemPool itemPool)
    {
        float summedChance = 0f;
        List<RewardOption> rewards = m_rewardOptions.AsValueEnumerable().Where(x =>
        {
            bool used = itemPool.IsRewardAvailable(x.Reward);
            if (used)
            {
                summedChance += x.Chance;
            }
            return used;
        }).ToList(); 
        
        float randomValue = Random.value;
        float cumulativeChance = 0f;
        
        foreach (RewardOption option in rewards)
        {
            cumulativeChance += option.Chance / summedChance;
            if (randomValue <= cumulativeChance)
            {
                return option.Reward;
            }
        }
        
        return m_rewardOptions.Last().Reward;
    }
}

[Serializable]
public class RewardOption
{
    [SerializeField] private PickupType m_reward;
    [SerializeField][Range(0f, 1f)] private float m_chance;
    
    public PickupType Reward => m_reward;
    public float Chance => m_chance;
}

[Serializable]
internal class WorldPathChunkList
{
    [SerializeField] internal List<GeneratedLevelChunk> m_chunks;

    private int m_lastSelection;

    internal WorldPathChunkList(List<GeneratedLevelChunk> chunks)
    {
        m_chunks = chunks.CopyFromByValue();
        m_lastSelection = -1;
    }
    
    internal bool HasChunk => m_chunks.Count > 0;

    internal List<GeneratedLevelChunk> ChunkList => m_chunks;
    
    internal GeneratedLevelChunk NextChunk {
        get
        {
            m_lastSelection = (m_lastSelection + 1) % m_chunks.Count;
            return m_chunks[m_lastSelection];
        }
    }

    public void ChunkSelected()
    {
        m_chunks.RemoveAt(m_lastSelection);
        m_lastSelection--;
    }
}

[Serializable]
internal class ProceduralChunkPool
{
    [SerializeField] internal List<LevelChunkBase> m_chunks;
    [SerializeField] internal bool m_allowAdjacentRepeat;

    private int m_lastSelection = -1;
    
    internal LevelChunkBase RandomChunk {
        get
        {
            int selection = m_lastSelection = 
                Mathf.Clamp(
                (m_lastSelection + Random.Range(
                m_allowAdjacentRepeat? 0 : 1, m_chunks.Count)) % m_chunks.Count,
                0, m_chunks.Count - 1);
            return m_chunks[selection];
        }
    }
}

[Serializable]
internal class CombatEncounterList
{
    [SerializeField] internal List<EncounterSettings> m_encounters;

    private int m_lastSelection = -1;
    
    internal EncounterSettings RandomEncounter {
        get
        {
            int selection;
            m_lastSelection = selection = (m_lastSelection + Random.Range(1, m_encounters.Count)) % m_encounters.Count;
            return m_encounters[selection];
        }
    }
}

[Serializable]
public class GeneratedLevelChunk : IConcreteCloneable<GeneratedLevelChunk>
{
    [NonSerialized] internal ItemPool m_itemPool;
    [SerializeField] internal LevelChunkBase m_chunkAsset;
    [SerializeField] internal int m_combatLevel;
    [SerializeField] internal RewardOptionSet m_chunkRewardSet;
    [SerializeField] internal SecretTye m_chunkSecret;
    [SerializeField] internal RewardOptionSet m_chunkSecretRewardSet;
    
    public LevelChunkBase ChunkAsset => m_chunkAsset;
    public int CombatLevel => m_combatLevel;
    public PickupType ChunkReward
    {
        get => m_chunkRewardSet?.GetRandomReward(m_itemPool) ?? PickupType.None;
        //internal set => m_chunkReward = value;
    }
    
    public SecretTye ChunkSecret
    {
        get => SecretTye.SequentialButton;//m_chunkSecret;
        internal set => m_chunkSecret = value;
    }
    
    public PickupType ChunkSecretReward
    {
        get => m_chunkSecretRewardSet?.GetRandomReward(m_itemPool) ?? PickupType.None;
        //internal set => m_chunkSecretReward = value;
    }

    public GeneratedLevelChunk Copy()
    {
        return new GeneratedLevelChunk
        {
            m_chunkAsset = this.ChunkAsset,
            m_combatLevel = this.CombatLevel,
            m_chunkRewardSet = this.m_chunkRewardSet,
            m_chunkSecret = this.ChunkSecret,
            m_chunkSecretRewardSet = this.m_chunkSecretRewardSet,
            m_itemPool = this.m_itemPool,
        };
    }

    public enum SecretTye
    {
        None = 0,
        SequentialButton = 10,
        MusicalButton = 10,
        ComboChallenge = 10,
    }
}