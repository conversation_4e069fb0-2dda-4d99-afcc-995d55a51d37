using System;
using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using UnityEngine;
using UnityEngine.Events;

public class LevelChunkReferneceHolder : MonoBehaviour
{
    [SerializeField] private GameObject m_playerSpawnPosition;
    [SerializeField] private GameObject m_chunkStartBarrier;
    [SerializeField] private GameObject m_chunkStartPortal;
    [SerializeField] private GameObject m_chunkEndBarrier;
    [SerializeField] private GameObject m_chunkEndPortal;
    
    [SerializeField] private List<OnTriggerEnterListener> m_chunkEndPortals;
    
    [SerializeField] private List<CombatEncounter> m_chunkEncounter;
    [SerializeField] private RewardHandler m_rewardHandler;
    [SerializeField] private EncounterGate m_endOfEncounterGate;
    [SerializeField] private FocusCameraShot m_endEncounterFocusShot;
    [SerializeField] private List<LevelPersistantElementReference> m_persistantElementReferences;
    [SerializeField] private List<VoronoiMeshCellSpawner> m_environmentPropSpawner;
    [SerializeField] private SecretSequenceController m_secretSequenceController;
    [SerializeField] private UnityEvent m_onSceneLoadedEvent;
    
    public GameObject PlayerSpawnPosition => m_playerSpawnPosition;
    public GameObject ChunkStartBarrier => m_chunkStartBarrier;
    public GameObject ChunkStartPortal => m_chunkStartPortal;
    public GameObject ChunkEndBarrier => m_chunkEndBarrier;
    public GameObject ChunkEndPortal => m_chunkEndPortal;
    public List<CombatEncounter> ChunkEncounters => m_chunkEncounter;
    public RewardHandler RewardHandler => m_rewardHandler;
    public EncounterGate EndOfEncounterGate => m_endOfEncounterGate;
    public FocusCameraShot EndEncounterFocusShot => m_endEncounterFocusShot;
    public List<LevelPersistantElementReference> PersistantElementReferences => m_persistantElementReferences;
    public List<OnTriggerEnterListener> ChunkEndPortals => m_chunkEndPortals;
    public SecretSequenceController SecretSequenceController => m_secretSequenceController;
    public List<VoronoiMeshCellSpawner> EnvironmentPropSpawner => m_environmentPropSpawner;

    public UnityEvent OnSceneLoadedEvent => m_onSceneLoadedEvent;
    
    [NonSerialized] private LevelChunkBase m_levelChunkAsset;
    
    private ServiceReference<LevelManagementService> m_levelManagementService = new();
    
    private void Awake()
    {
        if(m_chunkStartBarrier) m_chunkStartBarrier.SetActive(false);
        if(m_chunkStartPortal) m_chunkStartPortal.SetActive(false);
        if(m_chunkEndBarrier) m_chunkEndBarrier.SetActive(false);
        if(m_chunkEndPortal) m_chunkEndPortal.SetActive(false);
        foreach (CombatEncounter encounter in m_chunkEncounter)
        {
            encounter?.gameObject.SetActive(false);
        }
        
        foreach (OnTriggerEnterListener portal in m_chunkEndPortals)
        {
            portal?.gameObject.SetActive(false);
        }
    }

    private async void Start()
    {
        //TODO: Tell level management service we're here and need the settings
        foreach (VoronoiMeshCellSpawner spawner in m_environmentPropSpawner)
        {
            await spawner.PopulateProps();
        }
        
        m_levelManagementService.Value.NotifyReferenceHolderCreated(this);
    }

    [ContextMenu("SpawnProps")]
    public void SpawnProps()
    {
        foreach (VoronoiMeshCellSpawner spawner in m_environmentPropSpawner)
        {
            spawner?.PopulateProps();
        }
    }

    private void OnDestroy()
    {
        m_levelChunkAsset?.UnloadSceneReferences();
    }

    public void DeactivateAll()
    {
        if(m_chunkStartBarrier) { m_chunkStartBarrier.SetActive(false); }
        if(m_chunkStartPortal) { m_chunkStartPortal.SetActive(false); }
        if(m_chunkEndBarrier) { m_chunkEndBarrier.SetActive(false); }
        if(m_chunkEndPortal) { m_chunkEndPortal.SetActive(false); }
        
        foreach (CombatEncounter encounter in m_chunkEncounter)
        {
            encounter.gameObject.SetActive(false);
            encounter.ResetEncounter();
        }
    }
    
    public void SetChunkAsset(LevelChunkBase chunkData)
    {
        m_levelChunkAsset = chunkData;
    }
}
