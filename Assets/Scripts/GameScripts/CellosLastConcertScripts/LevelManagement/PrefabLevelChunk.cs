using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using RibCageGames.Base;
using UnityEditor;
using UnityEngine;

[CreateAssetMenu(fileName = "PrefabLevelChunk", menuName = "AlphaNomos/Settings/PrefabLevelChunk")]
public class PrefabLevelChunk : SceneLevelChunk
{
    [SerializeField] private LevelChunkReferneceHolder m_environmentPrefab;
    
    private LevelChunkReferneceHolder m_prefabInstance;

    public override async UniTask LoadChunkAsset(GeneratedLevelChunk chunkData,
        bool firstChunk, bool reload, Vector3 stitchPosition, Vector3 stitchVector)
    {
        //Not same scene, load new scene

        if (firstChunk)
        {
            await LoadChunkScene(firstChunk, reload);
        }

        m_prefabInstance = Instantiate(m_environmentPrefab);

        await UniTask.WaitUntil(() => m_levelManagementService.Value.LevelReferneceHolder != null);

        if (m_levelManagementService.Value.LevelReferneceHolder != m_prefabInstance)
        {
            Debug.LogError($"LevelReferneceHolder missmatch");
        }

        m_levelManagementService.Value.NotifyReferenceHolderCreated(null);

        m_prefabInstance.SetChunkAsset(this);

        m_prefabInstance.name += $"_{name}";
        PlaceChunkAsset(stitchPosition, stitchVector);
        
        SetupLevelChunk(m_prefabInstance, chunkData).Forget();
    }

    public override void PlaceChunkAsset(Vector3 stitchPosition, Vector3 stitchVector)
    {
        return;
        m_prefabInstance.transform.rotation =
            Quaternion.LookRotation(stitchVector);// * m_prefabInstance.transform.rotation;
        m_prefabInstance.transform.position = stitchPosition + 
                                              (m_prefabInstance.transform.position - m_prefabInstance.PlayerSpawnPosition.transform.position);
    }
}

#if UNITY_EDITOR
[CustomEditor(typeof(PrefabLevelChunk), editorForChildClasses: true)]
public class PrefabLevelChunkEditor : Editor
{
    private SerializedProperty m_levelIndexProperty;
    private LevelChunkBase m_target;
    private LevelManagementService m_levelManagementService;
    private List<SerializedProperty> m_defaultProperties;
    
    private void OnEnable()
    {
        m_defaultProperties = new List<SerializedProperty>();
        
        m_levelIndexProperty = serializedObject.FindProperty("m_containingLevelIndex");
        
        m_defaultProperties.Add(serializedObject.FindProperty("m_environmentPrefab"));

        m_levelManagementService = ServiceLocator.EditorGet<LevelManagementService>();
        
        m_target = (LevelChunkBase) target;
    }
    
    public override void OnInspectorGUI()
    {
        serializedObject.Update();

        m_levelIndexProperty.intValue =
            EditorGUILayout.Popup(new GUIContent("ContainingLevel"), m_levelIndexProperty.intValue,
                m_levelManagementService.FriendlyScenesReferenceNames);
        
        foreach (SerializedProperty property in m_defaultProperties)
        {
            EditorGUILayout.PropertyField(property);
        }

        serializedObject.ApplyModifiedProperties();
    }
}
#endif
