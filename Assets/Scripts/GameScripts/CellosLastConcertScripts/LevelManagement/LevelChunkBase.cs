using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using Cysharp.Threading.Tasks;
using RibCageGames.Base;
using UnityEditor;
using UnityEngine;
using UnityEngine.Events;

public abstract class LevelChunkBase : MonoScriptableObject, IConcreteCloneable<LevelChunkBase>
{
    [SerializeField] private int m_containingLevelIndex;
    [SerializeField] private SpecialSceneController m_specialSceneController;
    protected UnityEvent OnSceneReferencesLoaded { get; } = new();
    private UnityEvent OnSceneReferencesUnloaded { get; } = new();
    
    [NonSerialized] private LevelChunkReferneceHolder m_loadedReferenceHolder;
    
    [NonSerialized] private bool? m_activateStart;
    [NonSerialized] private bool? m_activateEnd;
    [NonSerialized] private bool? m_isLastInChunk;
    
    public int ContainingLevelIndex => m_containingLevelIndex;
    protected bool Loaded => m_loadedReferenceHolder != null;
    protected GameObject PlayerSpawnPosition => Loaded? m_loadedReferenceHolder.PlayerSpawnPosition : null;
    protected GameObject ChunkStartBarrier => Loaded? m_loadedReferenceHolder.ChunkStartBarrier : null;
    protected GameObject ChunkStartPortal => Loaded? m_loadedReferenceHolder.ChunkStartPortal : null;
    protected List<CombatEncounter> ChunkEncounters  => Loaded? m_loadedReferenceHolder.ChunkEncounters : null;
    //protected PowerupSelection PowerupSelection => Loaded? m_loadedReferenceHolder.PowerupSelection : null;
    protected RewardHandler RewardHandler => Loaded? m_loadedReferenceHolder.RewardHandler : null;
    protected EncounterGate EndOfEncounterGate => Loaded? m_loadedReferenceHolder.EndOfEncounterGate : null;
    //protected FocusCameraShot EndEncounterFocusShot => Loaded? m_loadedReferenceHolder.EndEncounterFocusShot : null;
    protected List<OnTriggerEnterListener> ChunkEndPortals => Loaded? m_loadedReferenceHolder.ChunkEndPortals : null;
    protected SecretSequenceController SecretSequenceController => Loaded? m_loadedReferenceHolder.SecretSequenceController : null;
    protected UnityEvent OnSceneLoadedEvent => Loaded? m_loadedReferenceHolder.OnSceneLoadedEvent : null;
    private CancellationTokenSource m_instanceCancelledSource;
    protected ServiceReference<LevelManagementService> m_levelManagementService = new();

    public override void Initialize() { }
    
    public abstract UniTask LoadChunkAsset(GeneratedLevelChunk chunkData, bool firstChunk, bool reload, Vector3 stitchPosition, Vector3 stitchVector);
    
    public abstract void PlaceChunkAsset(Vector3 stitchPosition, Vector3 stitchVector);

    public async UniTask SetupLevelChunk(LevelChunkReferneceHolder holder = null, GeneratedLevelChunk chunkData = null)
    {
        m_instanceCancelledSource?.Cancel();
        m_instanceCancelledSource = null;
        
        if (holder != null)
        {
            m_loadedReferenceHolder = holder;
        }
        
        OnSceneReferencesLoaded?.Invoke();

        await UniTask.DelayFrame(1);

        m_instanceCancelledSource = new CancellationTokenSource();
        
        //Get choices for next level
        List<(GeneratedLevelChunk targetChunk, Func<Transform ,bool, UniTask> portalAction)> levelChoices = m_levelManagementService.Value.GetLevelChoices();

        List<OnTriggerEnterListener> activePortals = new List<OnTriggerEnterListener>();

        if (ChunkEndPortals != null && ChunkEndPortals.Count > 0)
        {
            for (var i = 0; i < ChunkEndPortals.Count; i++)
            {
                if (i < levelChoices.Count)
                {
                    var i1 = i;
                    OnTriggerEnterListener portal = ChunkEndPortals[i1];
                    portal.TriggerEnter.AddCancellableListener((_) =>
                    {
                        foreach (OnTriggerEnterListener activePortal in activePortals)
                        {
                            activePortal.enabled = false;
                        }

                        levelChoices[i1].portalAction.Invoke(portal.transform, false);
                    }, m_instanceCancelledSource.Token);

                    activePortals.Add(portal);
                    portal.gameObject.SetActive(true);
                    //portal.enabled = false;
                    //Setup portal hints
                }
                else
                {
                    //SChunkEndPortals[i].gameObject.SetActive(false);
                    //Setup closed portals
                }
                ChunkEndPortals[i].enabled = false;
            }
        }

        void ActivatePortals()
        {
            foreach (OnTriggerEnterListener portal in activePortals)
            {
                portal.enabled = true;
            }
        }
        
        if(m_specialSceneController != null)
        {
            SpecialSceneController specialSceneController = Instantiate(m_specialSceneController, RewardHandler.SpawnLocator.transform);
            
            await specialSceneController.Initialize(m_loadedReferenceHolder, chunkData, m_instanceCancelledSource.Token);

            await specialSceneController.StartScene();
            
            await specialSceneController.OnSpecialSceneCompleted.WaitForEventTrigger(m_instanceCancelledSource.Token);
            ActivatePortals();
            return;
        }
        
        UnityEvent lastEncounterEvent = null;
        if (ChunkEncounters != null && ChunkEncounters.Count > 0 && chunkData != null)
        {
            for (int i = 0; i < ChunkEncounters.Count; i++)
            {
                var encounter = m_levelManagementService.Value.GetCombatEncounter(chunkData.CombatLevel);
                ChunkEncounters[i].ResetEncounter();
                ChunkEncounters[i].SetEncounterSettings(encounter);
                ChunkEncounters[i].gameObject.SetActive(true);
            }
            lastEncounterEvent = ChunkEncounters.Last().OnEncounterDeactivated;
        }

        UnityEvent<PickupType, PickupSpawnBase> powerupSelectionEvent = null;
        Action spawnReward = null;
        if (RewardHandler != null && chunkData != null && chunkData.ChunkReward != PickupType.None)
        {
            RewardHandler rewardHandler = RewardHandler;
            spawnReward = () => rewardHandler.SpawnPickup(chunkData.ChunkReward);
            powerupSelectionEvent = rewardHandler.OnPickupCollected;
        }
        
        if(chunkData != null && chunkData.ChunkSecret != GeneratedLevelChunk.SecretTye.None && chunkData.ChunkSecretReward != PickupType.None && chunkData.ChunkSecret != GeneratedLevelChunk.SecretTye.None)
        {
            SecretSequenceController secretSequence = SecretSequenceController;
            secretSequence?.StartSequence(Math.Clamp(chunkData.CombatLevel, 2, 5));
            secretSequence?.OnSequenceCompleted.AddSingleUseListener(() =>
            {
                secretSequence.RewardHandler.SpawnPickup(chunkData.ChunkSecretReward);
            }, m_instanceCancelledSource.Token);
            //SecretSequenceController?.ActivateSecretSequence(chunkData.ChunkSecret, chunkData.ChunkSecretReward);
        }
        
        await UniTask.DelayFrame(1);
        
        foreach (VoronoiMeshCellSpawner spawner in m_loadedReferenceHolder.EnvironmentPropSpawner)
        {
            await spawner.PopulateProps();
        }

        UnlinkChunkReference();
        //End of initial setup start waiting for events

        m_levelManagementService.Value.FadeIn();


        //If encounters are present wait for the last one to finish
        if (lastEncounterEvent != null)
        {
            await lastEncounterEvent.WaitForEventTrigger(cancellationToken: m_instanceCancelledSource.Token);
        }
        
        //Wait for reward pickup if applicable
        if (spawnReward != null && powerupSelectionEvent != null)
        {
            spawnReward.Invoke();
            await powerupSelectionEvent.WaitForEventTrigger(cancellationToken: m_instanceCancelledSource.Token);
        }
        
        //Activate portals
        if (!m_instanceCancelledSource.IsCancellationRequested)
        {
            ActivatePortals();
        }
    }

    public void UnlinkChunkReference()
    {
        m_loadedReferenceHolder = null;
    }
    
    public void UnloadSceneReferences()
    {
        m_loadedReferenceHolder = null;
        OnSceneReferencesUnloaded?.Invoke();
    }
    
    public void ActivateStartOfChunk()
    {
        if (Loaded)
        {
            if (ChunkStartBarrier != null)
            {
                ChunkStartBarrier.SetActive(true);
            }
            
            if (ChunkStartPortal != null)
            {
                ChunkStartPortal.SetActive(true);
            }
        }
        else
        {
            m_activateStart = true;
        }
    }

    public void DeactivateStartOfChunk()
    {
        if (Loaded)
        {
            if (ChunkStartBarrier != null)
            {
                ChunkStartBarrier.SetActive(false);
            }
            
            if (ChunkStartPortal != null)
            {
                ChunkStartPortal.SetActive(false);
            }
        }
        else
        {
            m_activateStart = false;
        }
    }

    public LevelChunkBase Copy()
    {
        return this;
    }
}

#if UNITY_EDITOR
[CustomEditor(typeof(LevelChunkBase), editorForChildClasses: true)]
public class LevelChunkBaseEditor : Editor
{
    private SerializedProperty m_levelIndexProperty;
    private LevelChunkBase m_target;
    private LevelManagementService m_levelManagementService;
    private List<SerializedProperty> m_defaultProperties;
    
    private void OnEnable()
    {
        m_defaultProperties = new List<SerializedProperty>();
        m_levelIndexProperty = serializedObject.FindProperty("m_containingLevelIndex");
        m_levelManagementService = ServiceLocator.EditorGet<LevelManagementService>();
        m_target = (LevelChunkBase) target;
    }
    
    public override void OnInspectorGUI()
    {
        serializedObject.Update();

        m_levelIndexProperty.intValue =
            EditorGUILayout.Popup(new GUIContent("ContainingLevel"), m_levelIndexProperty.intValue,
                m_levelManagementService.FriendlyScenesReferenceNames);
        
        foreach (SerializedProperty property in m_defaultProperties)
        {
            EditorGUILayout.PropertyField(property);
        }

        serializedObject.ApplyModifiedProperties();
    }
}
#endif