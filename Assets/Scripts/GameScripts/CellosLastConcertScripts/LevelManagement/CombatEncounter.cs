using System.Collections.Generic;
using System.Linq;
using System.Threading;
using Cysharp.Threading.Tasks;
using RibCageGames.Animation;
using RibCageGames.Base;
using RibCageGames.Behaviour;
using RibCageGames.Combat;
using UnityEngine;
using UnityEngine.Events;

public class CombatEncounter : MonoBehaviour
{
    [SerializeField] private OnTriggerEnterListener m_activatingTrigger;
    [SerializeField] private UnityEvent m_onEncounterActivated;
    [SerializeField] private UnityEvent m_onEncounterDeactivated;
    [SerializeField] private List<EncounterGate> m_gates;
    [SerializeField] private List<Transform> m_spawnLocators;
    [SerializeField] private List<CombatEntity> m_preloadedEnemies;
    [SerializeField] private Transform m_staticObstacleNodesHolder;
    [SerializeField] private List<Transform> m_staticObstacleNodes;
    [SerializeField] private EncounterSettings m_settings;
    [SerializeField] private FocusCameraShot m_lastEnemyDeathCameraShot;
    
    [SerializeField] private Animator m_animator;
    
    [SerializeField][AnimatorParameterControl("m_animator")] private AnimatorParameterControl m_barrierOnParameter;
    [SerializeField][AnimatorParameterControl("m_animator")] private AnimatorParameterControl m_barrierOffParameter;
    
    private List<BaseEnemyController> m_activeEnemies = new();
    private int m_currentWaveIndex = 0;
    private bool m_wasActivated;
    private bool m_active = false;
    private ServiceReference<PlayerService> m_playerService = new();
    private ServiceReference<PointOfInterestService> m_pointOfInterestService = new();
    private ServiceReference<InventoryService> m_inventoryService = new();
    private CancellationTokenSource m_activeEncounterSource;
    
    public UnityEvent OnEncounterActivated => m_onEncounterActivated;
    public UnityEvent OnEncounterDeactivated => m_onEncounterDeactivated;

    private void Start()
    {
        m_barrierOnParameter.Initialize(m_animator);
        m_barrierOffParameter.Initialize(m_animator);
        
        m_activeEncounterSource = new CancellationTokenSource();
        m_activeEnemies = new List<BaseEnemyController>();
        
        ServiceLocator.Get<LevelManagementService>().OnLevelReset.AddListener(ResetEncounter);

        foreach (CombatEntity preloadedEnemy in m_preloadedEnemies)
        {
            preloadedEnemy.Initialize();
        }
        
        m_animator?.gameObject.SetActive(false);
    }

    public void InitializeEncounter()
    {
        m_activatingTrigger.TriggerEnter.AddListener(x => { ActivateEncounter(); });
    }

    public void ActivateEncounter()
    {
        if (m_active) { return; }
        
        m_wasActivated = true;
        m_playerService.Value.OnEncounterStarted.Invoke();
        
        m_pointOfInterestService.Value.AddPointList(PointOfInterestTypes.Terrain, m_staticObstacleNodes);
        
        foreach (EncounterGate gate in m_gates)
        {
            gate.Activate();
        }

        ActivateWave(m_currentWaveIndex);
        m_active = true;

        if (m_animator)
        {
            m_animator.gameObject.SetActive(true);
        }
        m_barrierOnParameter.ApplyParameter();
        
        m_onEncounterActivated?.Invoke();

        AttackAllowanceCheckProcess();
    }

    private async void AttackAllowanceCheckProcess()
    {
        while (m_active)
        {
            await UniTask.WaitForSeconds(1f);
            int attackingEnemies = m_activeEnemies.Count(x => x.AttackingAllowed);
            if (attackingEnemies < m_settings.EnemyMaximalParallelAttacks)
            {
                for (int i = attackingEnemies; i < m_settings.EnemyMaximalParallelAttacks; i++)
                {
                    RunAttackAllowanceProcess(m_activeEncounterSource.Token);
                }
            }
        }
    }

    private async void RunAttackAllowanceProcess(CancellationToken token)
    {
        int enemyIndex = GetEnemyAtIndexCircular(m_activeEnemies, 0);
        
        if (enemyIndex < 0)
        {
            return;
        }
        BaseEnemyController attackingEnemy = m_activeEnemies[enemyIndex];
        attackingEnemy.AttackingAllowed = true;
        
        CancellationTokenSource enemyAttackSource = new CancellationTokenSource();
        bool transferAllowance = false;

        RegisterEnemyListeners();

        while (!token.IsCancellationRequested)
        {
            await UniTask.WaitUntil(() => transferAllowance);
            if (token.IsCancellationRequested)
            {
                break;
            }
            
            //Debug.Log($"RunAttackAllowanceProcess transferAllowance received");

            enemyAttackSource = new CancellationTokenSource();
            transferAllowance = false;
            
            if (m_activeEnemies.Count > 1)
            {
                enemyIndex = GetEnemyAtIndexCircular(m_activeEnemies, enemyIndex);
                if (enemyIndex < 0)
                {
                    //Debug.Log($"RunAttackAllowanceProcess No candidate found, all busy");
                    return;
                }

                var x = attackingEnemy;
                attackingEnemy.AttackingAllowed = false;
                attackingEnemy = m_activeEnemies[enemyIndex];
                attackingEnemy.AttackingAllowed = true;
                RegisterEnemyListeners();
                //Debug.Log($"RunAttackAllowanceProcess Transfer allowance form {x} to {attackingEnemy}");
            }
            else if (m_activeEnemies.Count > 0)
            {
                attackingEnemy = m_activeEnemies[0];
                attackingEnemy.AttackingAllowed = true;
                RegisterEnemyListeners();
                //Debug.Log($"RunAttackAllowanceProcess Only 1 enemy, setting allowance to {attackingEnemy}");
            }
            else
            {
                //Debug.Log($"RunAttackAllowanceProcess No enemies left, finishing");
                return;
            }
        }


        void RegisterEnemyListeners()
        {
            attackingEnemy.OnDead.AddCancellableListener((_) =>
            {
                enemyAttackSource.Cancel();
                transferAllowance = true;
            }, enemyAttackSource.Token);

            attackingEnemy.OnMoveCancelled.AddCancellableListener((move) =>
            {
                if (move.HitBoxCount > 0)
                {
                    enemyAttackSource.Cancel();
                    transferAllowance = true;
                }
            }, enemyAttackSource.Token);

            attackingEnemy.OnMoveEnded.AddCancellableListener((move) =>
            {
                if (move.HitBoxCount > 0)
                {
                    enemyAttackSource.Cancel();
                    transferAllowance = true;
                }
            }, enemyAttackSource.Token);
        }
    }

    private int GetEnemyAtIndexCircular(List<BaseEnemyController> enemies, int startIndex)
    {
        int count = enemies.Count;
        if (count < 1)
        {
            return -1;
        }
        
        for (int i = 0, j = startIndex % count; i < count; i++, j = (j + 1) % count)
        {
            if (!enemies[j].AttackingAllowed)
            {
                return j;
            }
        }

        return -1;
    }
    
    private async void ActivateWave(int waveIndex)
    {
        if (waveIndex == 0)
        {
            foreach (BaseEnemyController preloadedEnemy in m_preloadedEnemies)
            {
                preloadedEnemy.ResetPoolable(false);
                preloadedEnemy.ActivatePoolable();

                preloadedEnemy.OnDead.AddSingleUseListener(EnemyDied, m_activeEncounterSource.Token);
                preloadedEnemy.Engage();
                preloadedEnemy.AttackingAllowed = false;
                m_activeEnemies.Add(preloadedEnemy);
            }
        }
        
        if (m_settings.Waves.Count < 1)
        {
            return;
        }

        if (waveIndex > 1)
        {
            m_settings[waveIndex - 1].OnWaveCompleted.Invoke();    
        }
        await SpawnWave(m_settings[waveIndex].EnemySpawns);
        
        m_settings[waveIndex].OnWaveSpawned.Invoke(m_activeEnemies);
        
        for (int i = 0; i < m_settings.EnemyMaximalParallelAttacks; i++)
        {
            RunAttackAllowanceProcess(m_activeEncounterSource.Token);
        }
    }

    private async UniTask SpawnWave(List<EncounterSettings.EncounterEnemySpawn> encounterEnemySpawns, int startingIndex = 0)
    {
        int i;
        for (i = startingIndex; i < encounterEnemySpawns.Count && i < startingIndex + m_spawnLocators.Count; i++)
        {
            await UniTask.WaitForSeconds(Random.Range(0.1f, 0.3f));
            SpawnEnemy(encounterEnemySpawns[i]);
        }

        //Handles more spawns then spots
        if (i < encounterEnemySpawns.Count)
        {
            MonoProcess.WaitForSecondsProcess(3f).Do(()=>
            {
                SpawnWave(encounterEnemySpawns, i).Forget();
            });
        }
    }

    private void SpawnEnemy(EncounterSettings.EncounterEnemySpawn enemySpawn)
    {
        BaseEnemyController enemy = ((BaseEnemyController) enemySpawn.EnemyPool.GetInstance());
        int locatorIndex = enemySpawn.LocatorIndex % m_spawnLocators.Count;
        enemy.transform.position = m_spawnLocators[locatorIndex].position;
        enemy.transform.rotation = m_spawnLocators[locatorIndex].rotation;
        enemy.ResetPoolable(true);
        enemy.ActivatePoolable();

        enemy.OnDead.AddSingleUseListener(EnemyDied, m_activeEncounterSource.Token);
        enemy.Engage();
        enemy.AttackingAllowed = false;
        m_activeEnemies.Add(enemy);
    }

    public void ResetEncounter()
    {
        m_currentWaveIndex = 0;
        foreach (EncounterGate gate in m_gates)
        {
            gate.Deactivate(true);
        }
        
        if (m_wasActivated)
        {
            m_wasActivated = false;
            if (m_activatingTrigger)
            {
                m_activatingTrigger.gameObject.SetActive(true);
            }
            m_pointOfInterestService.Value.RemovePointList(PointOfInterestTypes.Terrain, m_staticObstacleNodes);
        }
        

        //Debug.LogError($"Deactivating enemies");
        foreach (CombatEntity enemy in m_preloadedEnemies)
        {
            enemy.DeactivatePoolable();
        }
        
        foreach (CombatEntity enemy in m_activeEnemies)
        {
            enemy.DeactivatePoolable();
        }
        m_activeEnemies.Clear();
        m_activeEncounterSource?.Cancel();
        m_activeEncounterSource = new CancellationTokenSource();
        
        m_active = false;
    }

    public void EnemyDied(CombatEntity combatEntity)
    {
        m_inventoryService.Value.ShowRunCurrency(combatEntity.transform.position, Random.Range(5, 50));
        m_activeEnemies.Remove((BaseEnemyController) combatEntity);
        if (m_activeEnemies.Count < 1)
        {
            m_currentWaveIndex++;
        }
        else
        {
            return;
        }

        if (m_currentWaveIndex < m_settings.Waves.Count)
        {
            MonoProcess.New().WaitForFrame(2).Do(() =>  ActivateWave(m_currentWaveIndex));
        }
        else
        {
            if (m_lastEnemyDeathCameraShot != null)
            {
                m_lastEnemyDeathCameraShot.ActivateFocusShot(combatEntity.transform);
                MonoProcess.WaitForSecondsProcess(m_lastEnemyDeathCameraShot.ShotDuration)
                    .Do(DeactivateEncounter);
            }
            else
            {
                MonoProcess.WaitForSecondsProcess(1f).Do(DeactivateEncounter);   
            }
        }
    }

    private async void DeactivateEncounter()
    {
        foreach (EncounterGate gate in m_gates)
        {
            gate?.Deactivate(false);
        }

        m_pointOfInterestService.Value.RemovePointList(PointOfInterestTypes.Terrain, m_staticObstacleNodes);
        m_playerService.Value.OnEncounterComplete?.Invoke();
        m_settings.OnEncounterCompleted?.Invoke();
        m_onEncounterDeactivated?.Invoke();
        if (m_activatingTrigger)
        {
            m_activatingTrigger.gameObject.SetActive(false);
        }
        
        m_barrierOffParameter.ApplyParameter();
        await UniTask.WaitForSeconds(1f);
        m_animator?.gameObject.SetActive(false);
    }

    private void OnValidate()
    {
        if ((m_staticObstacleNodes == null || m_staticObstacleNodes.Count < 1)
            && m_staticObstacleNodesHolder != null)
        {
            m_staticObstacleNodes = new List<Transform>();

            for (int i = 0; i < m_staticObstacleNodesHolder.childCount; i++)
            {
                m_staticObstacleNodes.Add(m_staticObstacleNodesHolder.GetChild(i));
            }
        }
    }

    private void OnDrawGizmos()
    {
        if (m_staticObstacleNodes.Count < 1) { return; }
        Gizmos.color = Color.blue;
        for (int i = 0; i < m_staticObstacleNodes.Count - 1; i++)
        {
            Gizmos.DrawRay(m_staticObstacleNodes[i].position,
                m_staticObstacleNodes[i + 1].position - m_staticObstacleNodes[i].position);
        }
        
        Gizmos.DrawRay(m_staticObstacleNodes[0].position,
            m_staticObstacleNodes[m_staticObstacleNodes.Count - 1].position - m_staticObstacleNodes[0].position);
    }

    public void SetEncounterSettings(EncounterSettings encounterSettings)
    {
        m_settings = encounterSettings;
    }
}
