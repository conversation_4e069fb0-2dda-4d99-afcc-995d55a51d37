using System;
using System.Threading;
using RibCageGames.Base;
using RibCageGames.Combat;
using RibCageGames.Music;
using UnityEngine;

[CreateAssetMenu(fileName = "OnDamageTriggerMoveEffect", menuName = "AlphaNomos/MoveEffects/OnDamageTriggerMoveEffect")]
public class OnDamageTriggerMoveEffect : MoveEffect
{
    [SerializeField] private bool m_requireOnBeat;
    [SerializeField] private float m_maximalDuration;
    
    [SerializeField] private ActionData m_move;

    private BeatSystem m_beatSystem;
    private PlayerService m_playerService;
    
    public override void Initialize()
    {
        base.Initialize();
        if (m_requireOnBeat)
        {
            m_beatSystem = ServiceLocator.Get<BeatSystem>();
            m_playerService = ServiceLocator.Get<PlayerService>();
        }
    }

    public override void Activate(CombatEntity owner)
    {
        CancellationTokenSource source = new CancellationTokenSource();
        source.CancelAfter(TimeSpan.FromSeconds(m_maximalDuration));

        bool active = true;
        
        owner.OnDamageTaken.AddListenerUntil((damage) =>
        {
            if(!active) { return; }
            
            if (!m_requireOnBeat || m_playerService.PlayerController.CurrentMoveJust)
            {
                active = false;
                owner.PerformCombatAction(m_move);
            } 
        }, (_) => !active, source.Token);
    }
}

