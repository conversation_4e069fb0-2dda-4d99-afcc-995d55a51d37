using System;
using System.Collections;
using System.Collections.Generic;
using RibCageGames.Base;
using UnityEngine;
using UnityEngine.UI;

public class OwlClawBarFill : MonoBehaviour
{
    [SerializeField] private OwlClawWeaponMoveSelectionStrategy m_owlClawStrategy;
    [SerializeField] private Image m_owlClawBar;
    
    private void Start()
    {
        ServiceLocator.Get<MonoService>().OnUpdate.AddListener(ControlledUpdate);
    }

    private void ControlledUpdate(float deltaTime)
    {
        m_owlClawBar.fillAmount = m_owlClawStrategy.CurrentRevAmount;
    }
}
