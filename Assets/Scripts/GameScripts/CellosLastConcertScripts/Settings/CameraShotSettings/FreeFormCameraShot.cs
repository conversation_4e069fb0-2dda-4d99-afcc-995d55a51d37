using RibCageGames.Base;
using Unity.Cinemachine;
using UnityEngine;

[CreateAssetMenu(fileName = "FreeFormCameraShot", menuName = "AlphaNomos/Settings/FreeFormCameraShot")]
public class FreeFormCameraShot : CameraShotSettings
{
    private readonly ServiceReference<CameraService> m_cameraService = new();

    public void ********************(CinemachineCamera camera)
    {
        m_cameraService.Value.********************(camera, this);
    }
}
