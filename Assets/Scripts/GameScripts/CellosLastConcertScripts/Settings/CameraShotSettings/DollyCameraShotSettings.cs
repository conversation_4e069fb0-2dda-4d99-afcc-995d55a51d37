using System.Collections;
using System.Collections.Generic;
using RibCageGames.Base;
using UnityEngine;

[CreateAssetMenu(fileName = "DollyCameraShot", menuName = "AlphaNomos/Settings/DollyCameraShotSettings")]
public class DollyCameraShotSettings : CameraShotSettings
{   
    [SerializeField] private List<Vector3> m_dollyWaypoints;
    [SerializeField] private List<Vector3> m_waypointRotations;
    
    public List<Vector3> DollyWaypoints => m_dollyWaypoints;
    public List<Vector3> DollyWaypointRotations => m_waypointRotations;

    private CameraService m_cameraService;
    private bool m_initialized;
    
    private void Initialize()
    {
        if (m_initialized) { return; }

        m_initialized = true;
        m_cameraService = ServiceLocator.Get<CameraService>();
    }

    public void ActivateFocusShot()
    {
        Initialize();
        m_cameraService.ActivateDollyCameraShot(this);
    }
}

