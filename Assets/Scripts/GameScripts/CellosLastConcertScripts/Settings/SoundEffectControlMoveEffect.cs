using System;
using System.Collections.Generic;
using RibCageGames.Base;
using RibCageGames.Combat;
using RibCageGames.Input;
using RibCageGames.Sound;
using UnityEngine;
using UnityEngine.Events;

[CreateAssetMenu(fileName = "SoundEffectControlMoveEffect", menuName = "AlphaNomos/MoveEffects/SoundEffectControlMoveEffect")]
public class SoundEffectControlMoveEffect : MoveEffect
{
    [SerializeField] private SoundEffect m_sourceEffect;
    [SerializeField] private float m_fadeDuration;
    [SerializeField] private float m_startTime;
    
    [SerializeField] private bool m_autoStop;
    [SerializeField] private float m_stopTime;
    
    [SerializeField] private bool m_logicStop;

    [NonSerialized] private SoundService m_soundService;
    [NonSerialized] private Action m_soundCancel;
        
    private SoundService SoundService
    {
        get
        {
            if (m_soundService == null)
            {
                m_soundService = ServiceLocator.Get<SoundService>();
            }

            return m_soundService;
        }   
    }
        
    private void OnEnable()
    {
        m_soundService = ServiceLocator.Get<SoundService>();
    }

    public Action PlaySound()
    {
        m_soundCancel = SoundService.PlayClipWithCallback(m_sourceEffect);

        if (m_autoStop)
        {
            MonoProcess stopProcess = MonoProcess.WaitForSecondsProcess(m_stopTime)
                .Do(() => m_soundCancel.Invoke());
            m_soundCancel += () => stopProcess.Stop();
        }
        
        return m_soundCancel;
    }

    public void CancelSound()
    {
        m_soundCancel?.Invoke();
        m_soundCancel = null;
    }
        
    public void FadeOutSound(float fadeDuration)
    {
        m_soundCancel?.Invoke();
        m_soundCancel = null;
    }
    
    public override void Activate(CombatEntity owner)
    {
        CharacterControllerBase ownerPlayer = (CharacterControllerBase)owner;
        
        //ownerPlayer.ActivateComboBoost(PowerUpType.WeaponBuff, m_multiplierBonus, true);
        Action cancel = null;
        
        PlaySound();

        if (m_logicStop)
        {
            UnityAction<ActionData> stopSound = null;
            UnityAction<ActionData, PlayerInputActions> stopSoundConditional = null;
            stopSound = (x) =>
            {
                FadeOutSound(m_fadeDuration);
                cancel?.Invoke();
            };

            stopSoundConditional = (action, input) =>
            {
                if (input != PlayerInputActions.LightAttack || !ownerPlayer.CurrentMoveJust)
                {
                    FadeOutSound(m_fadeDuration);
                    cancel?.Invoke();
                }
            };

            cancel += () => ownerPlayer.OnMoveEnded.RemoveListener(stopSound);
            cancel += () => ownerPlayer.OnMoveCancelled.RemoveListener(stopSound);
            cancel += () => ownerPlayer.OnMoveStarted.RemoveListener(stopSoundConditional);

            ownerPlayer.OnMoveEnded.AddListener(stopSound);
            ownerPlayer.OnMoveCancelled.AddListener(stopSound);
            ownerPlayer.OnMoveStarted.AddListener(stopSoundConditional);
        }
        
        //ownerPlayer.OnMoveStarted.AddListenerUntil((move, initiatingAction) =>
        //{
        //    if (move.HitBoxCount > 0)
        //    {
        //        
        //    }
        //}, (move, initiatingAction) => move.HitBoxCount > 0);
    }
}
