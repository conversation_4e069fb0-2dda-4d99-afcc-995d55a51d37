using System;
using System.Threading;
using Cysharp.Threading.Tasks;
using RibCageGames.Combat;
using UnityEngine;

public class DangerIndicatorController : MonoBehaviour, IAnimatedVisualEffect
{
    [Header("References")]
    [SerializeField] private MeshRenderer m_meshRenderer;

    [<PERSON><PERSON>("Animation Settings")]
    [SerializeField] private float m_animationDuration = 2f;
    [SerializeField] private AnimationCurve m_animationCurve = AnimationCurve.EaseInOut(0f, 0f, 1f, 1f);

    [Header("Material Properties")]
    [SerializeField] private MaterialPropertyConfig[] m_propertyConfigs;

    private Material m_materialInstance;
    private CancellationTokenSource m_cancellationTokenSource;
    private bool m_isPlaying;

    [Serializable]
    public class MaterialPropertyConfig
    {
        [SerializeField] private string m_propertyName = "_Alpha";
        [SerializeField] private MaterialPropertyType m_propertyType = MaterialPropertyType.Float;
        [SerializeField][Range(0.1f, 1f)] private float m_durationModifier = 1f;
        [SerializeField] private float m_startValue = 0f;
        [SerializeField] private float m_endValue = 1f;
        [SerializeField] private Color m_startColor = Color.clear;
        [SerializeField] private Color m_endColor = Color.red;
        [SerializeField] private Vector4 m_startVector = Vector4.zero;
        [SerializeField] private Vector4 m_endVector = Vector4.one;
        
        public string PropertyName => m_propertyName;
        public MaterialPropertyType PropertyType => m_propertyType;
        public float StartValue => m_startValue;
        public float EndValue => m_endValue;
        public Color StartColor => m_startColor;
        public Color EndColor => m_endColor;
        public Vector4 StartVector => m_startVector;
        public Vector4 EndVector => m_endVector;
        public float DurationModifier => m_durationModifier;
    }

    public enum MaterialPropertyType
    {
        Float,
        Color,
        Vector,
    }

    public bool IsPlaying => m_isPlaying;

    private void Awake()
    {
        // Create material instance to avoid modifying the shared material
        if (m_meshRenderer != null && m_meshRenderer.material != null)
        {
            m_materialInstance = new Material(m_meshRenderer.material);
            m_meshRenderer.material = m_materialInstance;
        }

        m_meshRenderer.gameObject.SetActive(false);
    }

    public void Play()
    {
        if (m_isPlaying)
        {
            Stop();
        }

        m_isPlaying = true;
        m_cancellationTokenSource = new CancellationTokenSource();
        AnimatePropertiesAsync(m_cancellationTokenSource.Token).Forget();
        m_meshRenderer.gameObject.SetActive(true);
    }

    public void Stop()
    {
        if (m_cancellationTokenSource != null)
        {
            m_cancellationTokenSource.Cancel();
            m_cancellationTokenSource.Dispose();
            m_cancellationTokenSource = null;
        }

        m_isPlaying = false;
        m_meshRenderer.gameObject.SetActive(false);

        // Reset to start values
        if (m_materialInstance != null)
        {
            SetProperties(0f);
        }
    }

    private async UniTaskVoid AnimatePropertiesAsync(CancellationToken cancellationToken)
    {
        float elapsedTime = 0f;

        while (elapsedTime < m_animationDuration && !cancellationToken.IsCancellationRequested)
        {
            float normalizedTime = elapsedTime / m_animationDuration;
            float curveValue = m_animationCurve.Evaluate(normalizedTime);

            if (m_materialInstance != null)
            {
                SetProperties(curveValue);
            }

            elapsedTime += Time.deltaTime;
            await UniTask.NextFrame(cancellationToken).SuppressCancellationThrow();
        }

        // Ensure final values are set if not cancelled
        if (!cancellationToken.IsCancellationRequested && m_materialInstance != null)
        {
            SetProperties(1f);
        }

        m_isPlaying = false;
        m_meshRenderer.gameObject.SetActive(false);
        SetProperties(0f);
    }

    private void SetProperties(float curveValue)
    {
        foreach (var config in m_propertyConfigs)
        {
            SetMaterialProperty(config, curveValue);
        }
    }

    private void SetMaterialProperty(MaterialPropertyConfig config, float t)
    {
        if (m_materialInstance == null) return;

        switch (config.PropertyType)
        {
            case MaterialPropertyType.Float:
                float floatValue = Mathf.Lerp(config.StartValue, config.EndValue, t * (1f / config.DurationModifier));
                m_materialInstance.SetFloat(config.PropertyName, floatValue);
                break;

            case MaterialPropertyType.Color:
                Color colorValue = Color.Lerp(config.StartColor, config.EndColor, t * (1f / config.DurationModifier));
                m_materialInstance.SetColor(config.PropertyName, colorValue);
                break;

            case MaterialPropertyType.Vector:
                Vector4 vectorValue = Vector4.Lerp(config.StartVector, config.EndVector, t * (1f / config.DurationModifier));
                m_materialInstance.SetVector(config.PropertyName, vectorValue);
                break;
        }
    }

    private void OnDestroy()
    {
        // Cancel any running animation
        if (m_cancellationTokenSource != null)
        {
            m_cancellationTokenSource.Cancel();
            m_cancellationTokenSource.Dispose();
            m_cancellationTokenSource = null;
        }

        // Clean up material instance
        if (m_materialInstance != null)
        {
            DestroyImmediate(m_materialInstance);
        }
    }

    // Editor helper methods
    [ContextMenu("Play Animation")]
    private void PlayFromEditor()
    {
        if (Application.isPlaying)
        {
            Play();
        }
    }

    [ContextMenu("Stop Animation")]
    private void StopFromEditor()
    {
        if (Application.isPlaying)
        {
            Stop();
        }
    }
}
