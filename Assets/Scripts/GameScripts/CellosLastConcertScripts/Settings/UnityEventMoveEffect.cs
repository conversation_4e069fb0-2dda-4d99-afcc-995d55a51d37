using System.Collections;
using System.Collections.Generic;
using RibCageGames.Combat;
using UnityEngine;
using UnityEngine.Events;

[CreateAssetMenu(fileName = "UnityEventMoveEffect", menuName = "AlphaNomos/MoveEffects/UnityEventMoveEffect")]
public class UnityEventMoveEffect : MoveEffect
{
    [SerializeField] private UnityEvent m_event;
    
    public override void Activate(CombatEntity owner)
    {
        m_event?.Invoke();
    }
}
