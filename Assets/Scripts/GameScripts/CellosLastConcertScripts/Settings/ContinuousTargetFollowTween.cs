namespace RibCageGames.MonoUtils
{
    using DG.Tweening;
    using UnityEngine;
    using System;

    [CreateAssetMenu(fileName = "ContinuousTargetFollowTween", menuName = "RibCageGames/Utilities/ContinuousTargetFollowTween")]
    
    //TODO: make combat entity TweenerSettings
    public class ContinuousTargetFollowTween : MonoTweenerSettings
    {

        [SerializeField] private float m_targetFinalDistance;
        [SerializeField] private float m_maximalTravelDistance;
        [SerializeField] private bool m_lookAtOnly;
        [SerializeField] private Vector3 m_movementMultiplier = new Vector3(0f, -0.1f, 0f);
        
        [NonSerialized] private PlayerService m_playerService;

        public override Sequence RunWithValue(Transform targetTransform, Vector3 value, Action onComplete = null,
            Vector3 overrideForward = new(), Transform otherEntity = null,
            Func<Transform> getDynamicTarget = null, Action<Vector3> outputAction = null)
        {
            Sequence sequence = DOTween.Sequence(targetTransform);
            Tweener tween = null;
            switch (TweenType)
            {
                case TweenerType.Move:

                    Transform target;
                    if (getDynamicTarget != null && (target = getDynamicTarget.Invoke()) != null)
                    {
                            tween = TweenerMove(targetTransform, m_relative
                                    ? targetTransform.position
                                      + value
                                    : value,
                                m_duration,
                                overrideForward,
                                target,
                                outputAction);
                    }
                    else
                    {
                        tween = base.TweenerMove(targetTransform, m_relative
                                ? targetTransform.position
                                  + value
                                : value,
                            m_duration,
                            overrideForward);
                    }

                    break;
                case TweenerType.Rotate:
                    break;
                case TweenerType.Scale:
                    break;
            }

            tween?.SetDelay(m_delay);
            tween?.SetEase(m_ease);
            if (m_from)
            {
                tween.From();
            }

            if (tween != null && onComplete != null)
            {
                tween.onComplete += onComplete.Invoke;
            }

            sequence.Insert(0f, tween);
            return sequence;
        }
        
        protected override Tweener TweenerMove(Transform targetTransform, Vector3 value, float duration,
            Vector3 overrideForward = new Vector3(), Transform otherEntity = null,
            Action<Vector3> outputAction = null)
        {
            Vector3 initialPosition = targetTransform.position;
            Vector3 savedValue = initialPosition;
            float distanceTraveled = 0f;
            
            Tweener tweener = DOVirtual.Float(0f, 1f, duration, newValue =>
            {
                //TODO: Move to rotation implementation
                if (m_lookAtOnly)
                {
                    Vector3 currentPosition = targetTransform.position;
                    Vector3 targetPosition = otherEntity.position;
                    Vector3 targetVector = targetPosition - currentPosition;
                    outputAction?.Invoke(targetVector);
                }
                else if (distanceTraveled < m_maximalTravelDistance)
                {
                    Vector3 currentPosition = targetTransform.position;
                    Vector3 targetPosition = otherEntity.position;
                    Vector3 targetVector = targetPosition - currentPosition;

                    Vector3 pos = Vector3.Lerp(initialPosition,
                        targetPosition -
                        (targetVector.normalized * m_targetFinalDistance),
                        newValue);

                    float travelDistance = (pos - savedValue).magnitude;
                    Vector3 delta = pos - savedValue;
                    delta.y = delta.magnitude * m_movementMultiplier.y;
                    targetTransform.position = currentPosition + delta;
                    
                    outputAction?.Invoke(targetVector);
                    
                    savedValue = pos;
                    distanceTraveled += travelDistance;
                }
            });

            return tweener;
        }
    }
}
