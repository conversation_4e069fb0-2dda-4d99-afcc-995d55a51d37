using RibCageGames.MonoUtils;
using UnityEngine;

/// <summary>
/// A pool of PickupSpawnBase objects for efficient pickup spawning
/// </summary>
[CreateAssetMenu(fileName = "PickupPrefabPool", menuName = "RibCageGames/Settings/PickupPrefabPool")]
public class PickupPrefabPool : PrefabPoolAsset<PickupSpawnBase>
{
    /// <summary>
    /// Gets the concrete type of the pickup prefab
    /// </summary>
    public System.Type ConcreteType => Prefab.GetType();
}
