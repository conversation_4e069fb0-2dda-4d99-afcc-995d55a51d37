using System;
using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using RibCageGames.Editor.EditorUtillities.SerializeInterface;
using UnityEngine;
using UnityEngine.Events;
using Random = UnityEngine.Random;

public class SecretSequenceController : MonoBehaviour
{
    [Header("Sequence Settings")]
    [SerializeField] private List<SecretTriggerHolder> m_secretObjects = new();
    [SerializeField] private List<InterfaceReference<ISoundSource>> m_correctHitSounds = new();
    [SerializeField] private bool m_debugMode = false;
    
    [Header("Events")]
    [SerializeField] private UnityEvent m_onSequenceCompleted;
    [SerializeField] private UnityEvent m_onSequenceReset;
    [SerializeField] private UnityEvent<int> m_onCorrectHit; // Passes current progress
    [SerializeField] private UnityEvent<int> m_onWrongHit; // Passes expected index
    
    [SerializeField] private RewardHandler m_rewardHandler;
    
    public RewardHandler RewardHandler => m_rewardHandler;
    // Current sequence state
    private List<SecretTriggerHolder> m_currentSequence = new();
    private int m_currentSequenceIndex = 0;
    private bool m_sequenceActive = false;
    
    // Event listeners tracking
    private List<UnityAction> m_activeListeners = new();
    
    public UnityEvent OnSequenceCompleted => m_onSequenceCompleted;
    public UnityEvent OnSequenceReset => m_onSequenceReset;
    public UnityEvent<int> OnCorrectHit => m_onCorrectHit;
    public UnityEvent<int> OnWrongHit => m_onWrongHit;
    
    /// <summary>
    /// Gets the current sequence of objects (read-only)
    /// </summary>
    public IReadOnlyList<SecretTriggerHolder> CurrentSequence => m_currentSequence.AsReadOnly();
    
    /// <summary>
    /// Gets the current progress in the sequence (0-based index)
    /// </summary>
    public int CurrentProgress => m_currentSequenceIndex;
    
    /// <summary>
    /// Gets whether a sequence is currently active
    /// </summary>
    public bool IsSequenceActive => m_sequenceActive;

    private void Start()
    {
        foreach (SecretTriggerHolder secretObject in m_secretObjects)
        {
            secretObject.HittableInteractable.gameObject.SetActive(false);
        }
    }

    /// <summary>
    /// Starts a new secret sequence with the specified number of objects
    /// </summary>
    /// <param name="sequenceLength">Number of objects to include in the sequence</param>
    public void StartSequence(int sequenceLength)
    {
        if (sequenceLength <= 0)
        {
            Debug.LogWarning("SecretSequenceController: Sequence length must be greater than 0");
            return;
        }

        if (sequenceLength > m_secretObjects.Count)
        {
            Debug.LogWarning($"SecretSequenceController: Requested sequence length ({sequenceLength}) exceeds available objects ({m_secretObjects.Count})");
            sequenceLength = m_secretObjects.Count;
        }
        
        foreach (SecretTriggerHolder secretObject in m_secretObjects)
        {
            secretObject.HittableInteractable.gameObject.SetActive(false);
        }
        
        // Stop any existing sequence
        StopSequence();
        
        // Select random objects for the sequence
        m_currentSequence = SelectRandomObjects(sequenceLength);
        m_currentSequenceIndex = 0;
        m_sequenceActive = true;
        
        // Add listeners to all objects in the sequence
        SetupSequenceListeners();
        
        if (m_debugMode)
        {
            Debug.Log($"SecretSequenceController: Started sequence with {m_currentSequence.Count} objects");
            for (int i = 0; i < m_currentSequence.Count; i++)
            {
                Debug.Log($"  {i + 1}. {m_currentSequence[i].name}");
            }
        }
    }
    
    /// <summary>
    /// Stops the current sequence and cleans up listeners
    /// </summary>
    public void StopSequence()
    {
        if (!m_sequenceActive)
        {
            return;
        }
        
        CleanupListeners();
        m_currentSequence.Clear();
        m_currentSequenceIndex = 0;
        m_sequenceActive = false;
        
        if (m_debugMode)
        {
            Debug.Log("SecretSequenceController: Sequence stopped");
        }
    }
    
    /// <summary>
    /// Resets the current sequence progress without changing the sequence
    /// </summary>
    public async void ResetSequenceProgress()
    {
        if (!m_sequenceActive)
        {
            return;
        }
        
        m_currentSequenceIndex = 0;
        m_onSequenceReset?.Invoke();
        
        if (m_debugMode)
        {
            Debug.Log("SecretSequenceController: Sequence progress reset");
        }
        
        await UniTask.WaitForSeconds(0.5f);
        foreach (SecretTriggerHolder secretObject in m_currentSequence)
        {
            secretObject.TriggerReset(); // Use parameter 3 for sequence completion
        }
    }
    
    private List<SecretTriggerHolder> SelectRandomObjects(int count)
    {
        // Create a copy of the list to avoid modifying the original
        List<SecretTriggerHolder> availableObjects = new List<SecretTriggerHolder>(m_secretObjects);
        List<SecretTriggerHolder> selectedObjects = new List<SecretTriggerHolder>();
        
        // Randomly select objects
        for (int i = 0; i < count && availableObjects.Count > 0; i++)
        {
            int randomIndex = Random.Range(0, availableObjects.Count);
            selectedObjects.Add(availableObjects[randomIndex]);
            availableObjects.RemoveAt(randomIndex);
        }
        
        return selectedObjects;
    }
    
    private void SetupSequenceListeners()
    {
        CleanupListeners(); // Ensure no duplicate listeners
        
        for (int i = 0; i < m_currentSequence.Count; i++)
        {
            SecretTriggerHolder secretObject = m_currentSequence[i];
            int expectedIndex = i; // Capture the index for the closure

            m_currentSequence[i].HittableInteractable.gameObject.SetActive(true);
            
            UnityAction listener = () => OnObjectHit(secretObject, expectedIndex);
            secretObject.HittableInteractable.OnHit.AddListener(listener);
            m_activeListeners.Add(listener);
        }
    }
    
    private void CleanupListeners()
    {
        for (int i = 0; i < m_activeListeners.Count && i < m_currentSequence.Count; i++)
        {
            if (m_currentSequence[i] != null && m_currentSequence[i].HittableInteractable != null)
            {
                m_currentSequence[i].HittableInteractable.OnHit.RemoveListener(m_activeListeners[i]);
                m_currentSequence[i].HittableInteractable.gameObject.SetActive(false);
            }
        }
        m_activeListeners.Clear();
    }
    
    private void OnObjectHit(SecretTriggerHolder hitObject, int expectedIndex)
    {
        if (!m_sequenceActive)
        {
            return;
        }
        
        if (expectedIndex == m_currentSequenceIndex)
        {
            // Correct hit!
            m_correctHitSounds[m_currentSequenceIndex].Value.Play();

            // Trigger animator parameter for correct hit
            hitObject.TriggerCorrectHit(); // Use parameter 1 for correct hits

            m_currentSequenceIndex++;
            m_onCorrectHit?.Invoke(m_currentSequenceIndex);

            if (m_debugMode)
            {
                Debug.Log($"SecretSequenceController: Correct hit! Progress: {m_currentSequenceIndex}/{m_currentSequence.Count}");
            }

            // Check if sequence is complete
            if (m_currentSequenceIndex >= m_currentSequence.Count)
            {
                CompleteSequence();
            }
        }
        else
        {
            // Wrong hit - reset sequence
            if (m_debugMode)
            {
                Debug.Log($"SecretSequenceController: Wrong hit! Expected index {m_currentSequenceIndex}, got {expectedIndex}. Resetting sequence.");
            }

            // Trigger animator parameter for wrong hit
            hitObject.TriggerWrongHit(); // Use parameter 2 for wrong hits

            m_onWrongHit?.Invoke(expectedIndex);
            ResetSequenceProgress();
        }
    }
    
    private void CompleteSequence()
    {
        if (m_debugMode)
        {
            Debug.Log("SecretSequenceController: Sequence completed successfully!");
        }

        // Trigger completion animation on all objects in sequence
        foreach (SecretTriggerHolder secretObject in m_currentSequence)
        {
            secretObject.TriggerCorrectHit(); // Use parameter 3 for sequence completion
        }

        m_onSequenceCompleted?.Invoke();
        StopSequence();
    }
    
    private void OnDestroy()
    {
        StopSequence();
    }
    
    // Editor helper methods
    [ContextMenu("Start Test Sequence (3 objects)")]
    private void StartTestSequence()
    {
        if (Application.isPlaying)
        {
            StartSequence(3);
        }
    }
    
    [ContextMenu("Stop Sequence")]
    private void StopSequenceFromEditor()
    {
        if (Application.isPlaying)
        {
            StopSequence();
        }
    }
    
    [ContextMenu("Reset Progress")]
    private void ResetProgressFromEditor()
    {
        if (Application.isPlaying)
        {
            ResetSequenceProgress();
        }
    }
}
