using System;
using System.Collections.Generic;
using System.Threading;
using Cysharp.Threading.Tasks;
using RibCageGames.Base;
using UnityEngine;
using UnityEngine.Events;

[CreateAssetMenu(fileName = "InventoryService", menuName = "RibCageGames/Services/InventoryService")]
public class InventoryService : BaseService
{
    [Header("Currency Display")]
    [SerializeField] private float m_defaultRunCurrencyRate = 100f;

    [Header("Events")]
    [SerializeField] private UnityEvent<InventoryItemType, int> m_onItemAdded;
    [SerializeField] private UnityEvent<InventoryItemType, int> m_onItemRemoved;
    [SerializeField] private UnityEvent<CurrencyType, int> m_onCurrencyChanged;
    [SerializeField] private UnityEvent m_onRunInventoryCleared;

    // Currency display
    private ParticleSystem m_runCurrencyParticleSystem;
    private CancellationTokenSource m_showRunCurrencySource;

    // Inventory storage
    private Dictionary<InventoryItemType, int> m_runItems = new Dictionary<InventoryItemType, int>();
    private Dictionary<InventoryItemType, int> m_permanentItems = new Dictionary<InventoryItemType, int>();
    private Dictionary<CurrencyType, int> m_currencies = new Dictionary<CurrencyType, int>();

    #region Properties

    /// <summary>
    /// Event triggered when an item is added to inventory
    /// </summary>
    public UnityEvent<InventoryItemType, int> OnItemAdded => m_onItemAdded;

    /// <summary>
    /// Event triggered when an item is removed from inventory
    /// </summary>
    public UnityEvent<InventoryItemType, int> OnItemRemoved => m_onItemRemoved;

    /// <summary>
    /// Event triggered when currency amount changes
    /// </summary>
    public UnityEvent<CurrencyType, int> OnCurrencyChanged => m_onCurrencyChanged;

    /// <summary>
    /// Event triggered when run inventory is cleared
    /// </summary>
    public UnityEvent OnRunInventoryCleared => m_onRunInventoryCleared;

    #endregion

    #region Item Management

    /// <summary>
    /// Adds an item to the inventory
    /// </summary>
    /// <param name="itemType">Type of item to add</param>
    /// <param name="amount">Amount to add</param>
    /// <param name="isPermanent">Whether this is a permanent item</param>
    public void AddItem(InventoryItemType itemType, int amount = 1, bool isPermanent = false)
    {
        if (amount <= 0) return;

        Dictionary<InventoryItemType, int> targetInventory = isPermanent ? m_permanentItems : m_runItems;

        if (targetInventory.ContainsKey(itemType))
        {
            targetInventory[itemType] += amount;
        }
        else
        {
            targetInventory[itemType] = amount;
        }

        m_onItemAdded?.Invoke(itemType, amount);

        Debug.Log($"InventoryService: Added {amount}x {itemType} ({(isPermanent ? "Permanent" : "Run")})");
    }

    /// <summary>
    /// Removes an item from the inventory
    /// </summary>
    /// <param name="itemType">Type of item to remove</param>
    /// <param name="amount">Amount to remove</param>
    /// <param name="isPermanent">Whether to remove from permanent items</param>
    /// <returns>True if the item was successfully removed</returns>
    public bool RemoveItem(InventoryItemType itemType, int amount = 1, bool isPermanent = false)
    {
        if (amount <= 0) return false;

        Dictionary<InventoryItemType, int> targetInventory = isPermanent ? m_permanentItems : m_runItems;

        if (!targetInventory.ContainsKey(itemType) || targetInventory[itemType] < amount)
        {
            return false; // Not enough items to remove
        }

        targetInventory[itemType] -= amount;

        if (targetInventory[itemType] <= 0)
        {
            targetInventory.Remove(itemType);
        }

        m_onItemRemoved?.Invoke(itemType, amount);

        Debug.Log($"InventoryService: Removed {amount}x {itemType} ({(isPermanent ? "Permanent" : "Run")})");
        return true;
    }

    /// <summary>
    /// Gets the amount of a specific item in inventory
    /// </summary>
    /// <param name="itemType">Type of item to check</param>
    /// <param name="isPermanent">Whether to check permanent items</param>
    /// <returns>Amount of the item</returns>
    public int GetItemAmount(InventoryItemType itemType, bool isPermanent = false)
    {
        Dictionary<InventoryItemType, int> targetInventory = isPermanent ? m_permanentItems : m_runItems;
        return targetInventory.ContainsKey(itemType) ? targetInventory[itemType] : 0;
    }

    /// <summary>
    /// Gets the total amount of an item (run + permanent)
    /// </summary>
    /// <param name="itemType">Type of item to check</param>
    /// <returns>Total amount of the item</returns>
    public int GetTotalItemAmount(InventoryItemType itemType)
    {
        return GetItemAmount(itemType, false) + GetItemAmount(itemType, true);
    }

    /// <summary>
    /// Checks if the player has enough of a specific item
    /// </summary>
    /// <param name="itemType">Type of item to check</param>
    /// <param name="amount">Required amount</param>
    /// <param name="isPermanent">Whether to check permanent items</param>
    /// <returns>True if player has enough items</returns>
    public bool HasItem(InventoryItemType itemType, int amount = 1, bool isPermanent = false)
    {
        return GetItemAmount(itemType, isPermanent) >= amount;
    }

    /// <summary>
    /// Gets all run items
    /// </summary>
    /// <returns>Read-only dictionary of run items</returns>
    public IReadOnlyDictionary<InventoryItemType, int> GetRunItems()
    {
        return m_runItems;
    }

    /// <summary>
    /// Gets all permanent items
    /// </summary>
    /// <returns>Read-only dictionary of permanent items</returns>
    public IReadOnlyDictionary<InventoryItemType, int> GetPermanentItems()
    {
        return m_permanentItems;
    }

    #endregion

    #region Currency Management

    /// <summary>
    /// Adds currency to the player's wallet
    /// </summary>
    /// <param name="currencyType">Type of currency</param>
    /// <param name="amount">Amount to add</param>
    public void AddCurrency(CurrencyType currencyType, int amount)
    {
        if (amount <= 0) return;

        if (m_currencies.ContainsKey(currencyType))
        {
            m_currencies[currencyType] += amount;
        }
        else
        {
            m_currencies[currencyType] = amount;
        }

        m_onCurrencyChanged?.Invoke(currencyType, m_currencies[currencyType]);

        Debug.Log($"InventoryService: Added {amount} {currencyType}. Total: {m_currencies[currencyType]}");
    }

    /// <summary>
    /// Removes currency from the player's wallet
    /// </summary>
    /// <param name="currencyType">Type of currency</param>
    /// <param name="amount">Amount to remove</param>
    /// <returns>True if currency was successfully removed</returns>
    public bool RemoveCurrency(CurrencyType currencyType, int amount)
    {
        if (amount <= 0) return false;

        if (!m_currencies.ContainsKey(currencyType) || m_currencies[currencyType] < amount)
        {
            return false; // Not enough currency
        }

        m_currencies[currencyType] -= amount;

        if (m_currencies[currencyType] <= 0)
        {
            m_currencies.Remove(currencyType);
        }

        m_onCurrencyChanged?.Invoke(currencyType, GetCurrencyAmount(currencyType));

        Debug.Log($"InventoryService: Removed {amount} {currencyType}. Total: {GetCurrencyAmount(currencyType)}");
        return true;
    }

    /// <summary>
    /// Gets the amount of a specific currency
    /// </summary>
    /// <param name="currencyType">Type of currency</param>
    /// <returns>Amount of currency</returns>
    public int GetCurrencyAmount(CurrencyType currencyType)
    {
        return m_currencies.ContainsKey(currencyType) ? m_currencies[currencyType] : 0;
    }

    /// <summary>
    /// Checks if the player has enough currency
    /// </summary>
    /// <param name="currencyType">Type of currency</param>
    /// <param name="amount">Required amount</param>
    /// <returns>True if player has enough currency</returns>
    public bool HasCurrency(CurrencyType currencyType, int amount)
    {
        return GetCurrencyAmount(currencyType) >= amount;
    }

    #endregion

    #region Run Management

    /// <summary>
    /// Clears all run-specific items (called when a run ends)
    /// </summary>
    public void ClearRunInventory()
    {
        m_runItems.Clear();

        // Clear run-specific currencies
        List<CurrencyType> runCurrenciesToRemove = new List<CurrencyType>();
        foreach (var currency in m_currencies)
        {
            if (IsRunSpecificCurrency(currency.Key))
            {
                runCurrenciesToRemove.Add(currency.Key);
            }
        }

        foreach (CurrencyType currencyType in runCurrenciesToRemove)
        {
            m_currencies.Remove(currencyType);
            m_onCurrencyChanged?.Invoke(currencyType, 0);
        }

        m_onRunInventoryCleared?.Invoke();

        Debug.Log("InventoryService: Run inventory cleared");
    }

    /// <summary>
    /// Determines if a currency type is run-specific
    /// </summary>
    /// <param name="currencyType">Currency type to check</param>
    /// <returns>True if run-specific, false if permanent</returns>
    private bool IsRunSpecificCurrency(CurrencyType currencyType)
    {
        switch (currencyType)
        {
            case CurrencyType.RunCurrency:
                return true;
            case CurrencyType.PermanentCoins:
            case CurrencyType.UnlockTokens:
                return false;
            default:
                return true; // Default to run-specific for safety
        }
    }

    #endregion

    #region Currency Display (Existing Functionality)

    public void ShowRunCurrency(Vector3 position, int amount, float duration)
    {
        m_showRunCurrencySource?.Cancel();
        m_showRunCurrencySource = new CancellationTokenSource();
        ShowRunCurrency(position, amount, duration, m_showRunCurrencySource.Token).Forget();
    }
    
    public void ShowRunCurrency(Vector3 position, int amount)
    {
        m_showRunCurrencySource?.Cancel();
        m_showRunCurrencySource = new CancellationTokenSource();
        ShowRunCurrency(position, amount, amount / m_defaultRunCurrencyRate, m_showRunCurrencySource.Token).Forget();
    }

    private async UniTask ShowRunCurrency(Vector3 position, int amount, float duration, CancellationToken token)
    {
        m_runCurrencyParticleSystem.transform.position = position;

        float m_runTime = 0f;
        int amountDropped = 0;
        
        while (!token.IsCancellationRequested && m_runTime < duration)
        {
            float delta = Time.deltaTime;
            int amountSpawned = (int) ((delta * amount) / duration);
            
            amountDropped += amountSpawned;
            m_runCurrencyParticleSystem.Emit(amountSpawned);
            
            m_runTime += delta;
            await UniTask.DelayFrame(1);
        }

        if (amount - amountDropped > 0)
        {
            m_runCurrencyParticleSystem.Emit(amount - amountDropped);
        }
    }

    #endregion

    #region Service Infrastructure
    public override void Init(GameObject servicePrefab = null)
    {
        m_runCurrencyParticleSystem = servicePrefab?.GetComponent<ParticleSystem>();   
    }

    public override void StartService(MonoInjector injector) {}

    public override void Dispose() {}
    #endregion
}
