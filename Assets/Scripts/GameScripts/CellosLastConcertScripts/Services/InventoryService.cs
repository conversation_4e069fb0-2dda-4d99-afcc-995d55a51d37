using System.Collections.Generic;
using System.Threading;
using Cysharp.Threading.Tasks;
using RibCageGames.Base;
using UnityEngine;
using UnityEngine.Events;

[CreateAssetMenu(fileName = "InventoryService", menuName = "RibCageGames/Services/InventoryService")]
public class InventoryService : BaseService
{
    [Header("Currency Display")]
    [SerializeField] private float m_defaultRunCurrencyRate = 100f;

    [Header("Events")]
    private UnityEvent<InventoryItemType, int> m_onItemAdded = new();
    private UnityEvent<InventoryItemType, int> m_onItemRemoved = new();
    private UnityEvent<RunItemType, int> m_onRunItemsChanged = new();
    private UnityEvent m_onRunInventoryCleared = new();

    // Currency display
    private ParticleSystem m_runCurrencyParticleSystem;
    private CancellationTokenSource m_showRunCurrencySource;

    // Inventory storage
    private Dictionary<InventoryItemType, int> m_permanentItems = new();
    private Dictionary<RunItemType, int> m_runItems = new();

    #region Properties

    /// <summary>
    /// Event triggered when an item is added to inventory
    /// </summary>
    public UnityEvent<InventoryItemType, int> OnItemAdded => m_onItemAdded;

    /// <summary>
    /// Event triggered when an item is removed from inventory
    /// </summary>
    public UnityEvent<InventoryItemType, int> OnItemRemoved => m_onItemRemoved;

    /// <summary>
    /// Event triggered when currency amount changes
    /// </summary>
    public UnityEvent<RunItemType, int> OnRunItemsChanged => m_onRunItemsChanged;

    /// <summary>
    /// Event triggered when run inventory is cleared
    /// </summary>
    public UnityEvent OnRunInventoryCleared => m_onRunInventoryCleared;

    #endregion

    #region Item Management

    /// <summary>
    /// Adds an item to the inventory
    /// </summary>
    /// <param name="itemType">Type of item to add</param>
    /// <param name="amount">Amount to add</param>
    /// <param name="isPermanent">Whether this is a permanent item</param>
    public void AddPermanentItem(InventoryItemType itemType, int amount = 1)
    {
        if (amount <= 0) return;

        if (m_permanentItems.ContainsKey(itemType))
        {
            m_permanentItems[itemType] += amount;
        }
        else
        {
            m_permanentItems[itemType] = amount;
        }

        m_onItemAdded?.Invoke(itemType, amount);
    }
    
    
    
    public void AddRunCurrency(int amount, Vector3 position, bool showVisuals = true)
    {
        AddRunItem(RunItemType.RunCurrency, amount);
        if (showVisuals)
        {
            ShowRunCurrency(position, amount);
        }
    }
    
    /// <summary>
    /// Adds an item to the inventory
    /// </summary>
    /// <param name="itemType">Type of item to add</param>
    /// <param name="amount">Amount to add</param>
    /// <param name="isPermanent">Whether this is a permanent item</param>
    public void AddRunItem(RunItemType itemType, int amount = 1)
    {
        if (amount <= 0) return;

        if (m_runItems.ContainsKey(itemType))
        {
            m_runItems[itemType] += amount;
        }
        else
        {
            m_runItems[itemType] = amount;
        }

        Debug.LogError($"m_onRunItemsChanged with {itemType} and {amount}");
        m_onRunItemsChanged?.Invoke(itemType, amount);
    }
    
    public bool RemovePermanentItem(InventoryItemType itemType, int amount = 1)
    {
        if (amount <= 0) return false;

        if (!m_permanentItems.ContainsKey(itemType) || m_permanentItems[itemType] < amount)
        {
            return false; // Not enough items to remove
        }

        m_permanentItems[itemType] -= amount;

        if (m_permanentItems[itemType] <= 0)
        {
            m_permanentItems.Remove(itemType);
        }

        m_onItemRemoved?.Invoke(itemType, amount);

        return true;
    }
    
    public bool RemoveRunItem(RunItemType itemType, int amount = 1)
    {
        if (amount <= 0) return false;

        if (!m_runItems.ContainsKey(itemType) || m_runItems[itemType] < amount)
        {
            return false; // Not enough items to remove
        }

        m_runItems[itemType] -= amount;

        if (m_runItems[itemType] <= 0)
        {
            m_runItems.Remove(itemType);
        }

        m_onRunItemsChanged?.Invoke(itemType, amount);

        return true;
    }
    
    public int GetRunItemCount(RunItemType itemType)
    {
        return m_runItems.TryGetValue(itemType, out var item) ? item : 0;
    }
    
    public int GetPermanentItemCount(InventoryItemType itemType)
    {
        return m_permanentItems.TryGetValue(itemType, out var item) ? item : 0;
    }
    
    public bool HasRunItemAmount(RunItemType itemType, int amount = 1)
    {
        return GetRunItemCount(itemType) >= amount;
    }
    
    public bool HasPermanentItemAmount(InventoryItemType itemType, int amount = 1)
    {
        return GetPermanentItemCount(itemType) >= amount;
    }

    /// <summary>
    /// Gets all run items
    /// </summary>
    /// <returns>Read-only dictionary of run items</returns>
    public IReadOnlyDictionary<RunItemType, int> GetRunItems()
    {
        return m_runItems;
    }

    /// <summary>
    /// Gets all permanent items
    /// </summary>
    /// <returns>Read-only dictionary of permanent items</returns>
    public IReadOnlyDictionary<InventoryItemType, int> GetPermanentItems()
    {
        return m_permanentItems;
    }

    #endregion

    #region Run Management

    /// <summary>
    /// Clears all run-specific items (called when a run ends)
    /// </summary>
    public void ClearRunInventory()
    {
        m_runItems.Clear();

        // Clear run-specific currencies
        List<RunItemType> runCurrenciesToRemove = new List<RunItemType>();

        foreach (RunItemType currencyType in runCurrenciesToRemove)
        {
            m_runItems.Remove(currencyType);
            m_onRunItemsChanged?.Invoke(RunItemType.RunCurrency, 0);
        }

        m_onRunInventoryCleared?.Invoke();

        Debug.Log("InventoryService: Run inventory cleared");
    }

    #endregion

    #region Currency Display

    public void ShowRunCurrency(Vector3 position, int amount, float duration)
    {
        m_showRunCurrencySource?.Cancel();
        m_showRunCurrencySource = new CancellationTokenSource();
        ShowRunCurrency(position, amount, duration, m_showRunCurrencySource.Token).Forget();
    }
    
    public void ShowRunCurrency(Vector3 position, int amount)
    {
        m_showRunCurrencySource?.Cancel();
        m_showRunCurrencySource = new CancellationTokenSource();
        ShowRunCurrency(position, amount, amount / m_defaultRunCurrencyRate, m_showRunCurrencySource.Token).Forget();
    }

    private async UniTask ShowRunCurrency(Vector3 position, int amount, float duration, CancellationToken token)
    {
        m_runCurrencyParticleSystem.transform.position = position;

        float m_runTime = 0f;
        int amountDropped = 0;
        
        while (!token.IsCancellationRequested && m_runTime < duration)
        {
            float delta = Time.deltaTime;
            int amountSpawned = (int) ((delta * amount) / duration);
            
            amountDropped += amountSpawned;
            m_runCurrencyParticleSystem.Emit(amountSpawned);
            
            m_runTime += delta;
            await UniTask.DelayFrame(1);
        }

        if (amount - amountDropped > 0)
        {
            m_runCurrencyParticleSystem.Emit(amount - amountDropped);
        }
    }

    #endregion

    #region Service Infrastructure
    public override void Init(GameObject servicePrefab = null)
    {
        m_runCurrencyParticleSystem = servicePrefab?.GetComponent<ParticleSystem>();   
    }

    public override void StartService(MonoInjector injector) {}

    public override void Dispose() {}
    #endregion
}
