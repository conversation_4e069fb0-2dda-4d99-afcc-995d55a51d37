using System.Threading;
using Cysharp.Threading.Tasks;
using RibCageGames.Base;
using UnityEngine;

[CreateAssetMenu(fileName = "InventoryService", menuName = "RibCageGames/Services/InventoryService")]
public class InventoryService : BaseService
{
    [SerializeField] private float m_defaultRunCurrencyRate = 100f;
    private ParticleSystem m_runCurrencyParticleSystem;
    private CancellationTokenSource m_showRunCurrencySource;

    public void ShowRunCurrency(Vector3 position, int amount, float duration)
    {
        m_showRunCurrencySource?.Cancel();
        m_showRunCurrencySource = new CancellationTokenSource();
        ShowRunCurrency(position, amount, duration, m_showRunCurrencySource.Token).Forget();
    }
    
    public void ShowRunCurrency(Vector3 position, int amount)
    {
        m_showRunCurrencySource?.Cancel();
        m_showRunCurrencySource = new CancellationTokenSource();
        ShowRunCurrency(position, amount, amount / m_defaultRunCurrencyRate, m_showRunCurrencySource.Token).Forget();
    }

    private async UniTask ShowRunCurrency(Vector3 position, int amount, float duration, CancellationToken token)
    {
        m_runCurrencyParticleSystem.transform.position = position;

        float m_runTime = 0f;
        int amountDropped = 0;
        
        while (!token.IsCancellationRequested && m_runTime < duration)
        {
            float delta = Time.deltaTime;
            int amountSpawned = (int) ((delta * amount) / duration);
            
            amountDropped += amountSpawned;
            m_runCurrencyParticleSystem.Emit(amountSpawned);
            
            m_runTime += delta;
            await UniTask.DelayFrame(1);
        }

        if (amount - amountDropped > 0)
        {
            m_runCurrencyParticleSystem.Emit(amount - amountDropped);
        }
    }
    
    
    #region Service Infrastructure
    public override void Init(GameObject servicePrefab = null)
    {
        m_runCurrencyParticleSystem = servicePrefab?.GetComponent<ParticleSystem>();   
    }

    public override void StartService(MonoInjector injector) {}

    public override void Dispose() {}
    #endregion
}
