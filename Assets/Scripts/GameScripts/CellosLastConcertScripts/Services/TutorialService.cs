using System;
using System.Linq;
using RibCageGames.Base;
using UnityEngine;
using UnityEngine.Events;

[CreateAssetMenu(fileName = "TutorialService", menuName = "AlphaNomos/Services/TutorialService")]
public class TutorialService : BaseService
{
    public UnityEvent<TutorialPrompts> OnTutorialShow { get; } = new UnityEvent<TutorialPrompts>();
    public UnityEvent<TutorialPrompts> OnTutorialHide { get; } = new UnityEvent<TutorialPrompts>();

    public override void Init(GameObject servicePrefab = null) { }

    public override void StartService(MonoInjector injector) { }

    public override void Dispose() { }

    public void ShowTutorial(int index)
    {
        OnTutorialShow?.Invoke((TutorialPrompts) index);
    }
    
    public void HideTutorial(int index)
    {
        OnTutorialHide?.Invoke((TutorialPrompts) index);
    }
    
    public void HideAllTutorials()
    {
        foreach (TutorialPrompts value in Enum.GetValues(typeof(TutorialPrompts)).Cast<TutorialPrompts>())
        {
            OnTutorialHide?.Invoke(value);   
        }
    }
    
    public void ShowTutorial(TutorialPrompts prompt)
    {
        OnTutorialShow?.Invoke(prompt);
    }
    
    public void HideTutorial(TutorialPrompts prompt)
    {
        OnTutorialHide?.Invoke(prompt);
    }
}

public enum TutorialPrompts
{
    Movement = 0,
    //Camera = 1,
    Jump = 2,
    LightAttack = 3,
    //HeavyAttack = 4,
    Dash = 5,
    Special = 6,
}

