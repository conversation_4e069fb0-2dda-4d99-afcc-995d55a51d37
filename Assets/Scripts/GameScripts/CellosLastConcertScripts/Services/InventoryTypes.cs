/// <summary>
/// Types of items that can be stored in the inventory
/// </summary>
public enum InventoryItemType
{
    // Run-specific items (cleared at end of run)
    HealthPotion,
    ShieldBoost,
    TemporaryPowerUp,
    RunS<PERSON><PERSON><PERSON>,
    BossReward,
    
    // Permanent unlockables
    CharacterUnlock,
    WeaponUnlock,
    SkinUnlock,
    AchievementToken,
    
    // Permanent upgrades
    PermanentHealthUpgrade,
    PermanentDamageUpgrade,
    PermanentSpeedUpgrade,
    
    // Collectibles
    MusicNote,
    ArtifactFragment,
    LoreItem,
    
    // Crafting materials
    CommonMaterial,
    RareMaterial,
    EpicMaterial,
    LegendaryMaterial,
    
    // Special items
    ReviveToken,
    SkipToken,
    BonusReward
}

/// <summary>
/// Types of currency in the game
/// </summary>
public enum CurrencyType
{
    // Run-specific currency (cleared at end of run)
    RunCurrency,        // Currency earned during a run
    
    // Permanent currencies
    PermanentCoins,     // Main permanent currency
    UnlockTokens,       // Special currency for unlocks
    PremiumCurrency,    // Premium/paid currency
    
    // Special currencies
    AchievementPoints,  // Points from achievements
    SeasonalCurrency,   // Limited-time currency
    EventCurrency       // Event-specific currency
}

/// <summary>
/// Helper class for inventory and currency operations
/// </summary>
public static class InventoryHelper
{
    /// <summary>
    /// Determines if an item type is permanent (survives run end)
    /// </summary>
    /// <param name="itemType">Item type to check</param>
    /// <returns>True if permanent, false if run-specific</returns>
    public static bool IsPermanentItem(InventoryItemType itemType)
    {
        switch (itemType)
        {
            // Run-specific items
            case InventoryItemType.HealthPotion:
            case InventoryItemType.ShieldBoost:
            case InventoryItemType.TemporaryPowerUp:
            case InventoryItemType.RunSpecificKey:
            case InventoryItemType.BossReward:
                return false;
            
            // Permanent items
            case InventoryItemType.CharacterUnlock:
            case InventoryItemType.WeaponUnlock:
            case InventoryItemType.SkinUnlock:
            case InventoryItemType.AchievementToken:
            case InventoryItemType.PermanentHealthUpgrade:
            case InventoryItemType.PermanentDamageUpgrade:
            case InventoryItemType.PermanentSpeedUpgrade:
            case InventoryItemType.MusicNote:
            case InventoryItemType.ArtifactFragment:
            case InventoryItemType.LoreItem:
            case InventoryItemType.CommonMaterial:
            case InventoryItemType.RareMaterial:
            case InventoryItemType.EpicMaterial:
            case InventoryItemType.LegendaryMaterial:
            case InventoryItemType.ReviveToken:
            case InventoryItemType.SkipToken:
            case InventoryItemType.BonusReward:
                return true;
            
            default:
                return false; // Default to run-specific for safety
        }
    }
    
    /// <summary>
    /// Determines if a currency type is permanent (survives run end)
    /// </summary>
    /// <param name="currencyType">Currency type to check</param>
    /// <returns>True if permanent, false if run-specific</returns>
    public static bool IsPermanentCurrency(CurrencyType currencyType)
    {
        switch (currencyType)
        {
            case CurrencyType.RunCurrency:
                return false;
            
            case CurrencyType.PermanentCoins:
            case CurrencyType.UnlockTokens:
            case CurrencyType.PremiumCurrency:
            case CurrencyType.AchievementPoints:
            case CurrencyType.SeasonalCurrency:
            case CurrencyType.EventCurrency:
                return true;
            
            default:
                return false; // Default to run-specific for safety
        }
    }
    
    /// <summary>
    /// Gets a human-readable name for an item type
    /// </summary>
    /// <param name="itemType">Item type</param>
    /// <returns>Display name</returns>
    public static string GetItemDisplayName(InventoryItemType itemType)
    {
        switch (itemType)
        {
            case InventoryItemType.HealthPotion: return "Health Potion";
            case InventoryItemType.ShieldBoost: return "Shield Boost";
            case InventoryItemType.TemporaryPowerUp: return "Temporary Power-Up";
            case InventoryItemType.RunSpecificKey: return "Run Key";
            case InventoryItemType.BossReward: return "Boss Reward";
            case InventoryItemType.CharacterUnlock: return "Character Unlock";
            case InventoryItemType.WeaponUnlock: return "Weapon Unlock";
            case InventoryItemType.SkinUnlock: return "Skin Unlock";
            case InventoryItemType.AchievementToken: return "Achievement Token";
            case InventoryItemType.PermanentHealthUpgrade: return "Permanent Health Upgrade";
            case InventoryItemType.PermanentDamageUpgrade: return "Permanent Damage Upgrade";
            case InventoryItemType.PermanentSpeedUpgrade: return "Permanent Speed Upgrade";
            case InventoryItemType.MusicNote: return "Music Note";
            case InventoryItemType.ArtifactFragment: return "Artifact Fragment";
            case InventoryItemType.LoreItem: return "Lore Item";
            case InventoryItemType.CommonMaterial: return "Common Material";
            case InventoryItemType.RareMaterial: return "Rare Material";
            case InventoryItemType.EpicMaterial: return "Epic Material";
            case InventoryItemType.LegendaryMaterial: return "Legendary Material";
            case InventoryItemType.ReviveToken: return "Revive Token";
            case InventoryItemType.SkipToken: return "Skip Token";
            case InventoryItemType.BonusReward: return "Bonus Reward";
            default: return itemType.ToString();
        }
    }
    
    /// <summary>
    /// Gets a human-readable name for a currency type
    /// </summary>
    /// <param name="currencyType">Currency type</param>
    /// <returns>Display name</returns>
    public static string GetCurrencyDisplayName(CurrencyType currencyType)
    {
        switch (currencyType)
        {
            case CurrencyType.RunCurrency: return "Run Currency";
            case CurrencyType.PermanentCoins: return "Coins";
            case CurrencyType.UnlockTokens: return "Unlock Tokens";
            case CurrencyType.PremiumCurrency: return "Premium Currency";
            case CurrencyType.AchievementPoints: return "Achievement Points";
            case CurrencyType.SeasonalCurrency: return "Seasonal Currency";
            case CurrencyType.EventCurrency: return "Event Currency";
            default: return currencyType.ToString();
        }
    }
}
