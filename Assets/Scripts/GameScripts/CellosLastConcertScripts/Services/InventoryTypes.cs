/// <summary>
/// Types of items that can be stored in the inventory
/// </summary>
public enum InventoryItemType
{
    // Run-specific items (cleared at end of run)
    RunPowerup,
    RunSpecificKey,
    
    // Permanent unlockables
    CharacterUnlock,
    WeaponUnlock,
    SkinUnlock,
    
    // Permanent upgrades
    PowerupEvolution,
    
    // Collectibles
    Trinket,
    TrinketBluprint,
    
    // Crafting materials
    //Enemy components
    
}

/// <summary>
/// Types of currency in the game
/// </summary>
public enum RunItemType
{
    // Run-specific currency (cleared at end of run)
    RunCurrency,// Currency earned during a run
    
    SpecialToken,//Special tokens used instead of junk
    Powerup,
}

/// <summary>
/// Helper class for inventory and currency operations
/// </summary>
public static class InventoryHelper
{
}
