using RibCageGames.Base;
using UnityEngine;

[CreateAssetMenu(fileName = "GameTesterService", menuName = "RibCageGames/Services/GameTesterService")]
public class GameTesterService : BaseService
{
    [SerializeField] private GameTesterMode m_mode;
    [SerializeField] private string m_devToken;
    
    private GameTesterCoroutineRunner m_runner;
    public bool Initialized { get; private set; }
    public bool Authenticated { get; private set; }
    public bool AuthenticationSuccessful { get; private set; }

    public override void Init(GameObject servicePrefab = null)
    {
        if (servicePrefab != null)
        {
            m_runner = servicePrefab.GetComponent<GameTesterCoroutineRunner>();
        }

        Authenticated = false;
        AuthenticationSuccessful = false;
        Initialized = false;
    }

    public override void StartService(MonoInjector injector)
    {
        InitializeGameTester();
    }

    public override void Dispose()
    {
        
    }

    private void InitializeGameTester()
    {
        GameTester.Initialize(m_mode, m_devToken);
        Initialized = true;
    }
    
    public void Auth(string playerPin)
    {
        Debug.LogError($"Authenticating GameTester with {playerPin}");
   
        //var playerPin = m_gameTesterPin ?? string.Empty;
        
        // Set the playerPin or playerToken. This is required.
        GameTester.SetPlayerPin(playerPin);
        // OR
        //GameTester.SetPlayerToken(playerToken);
   
        // Call to test if playerPin or playerToken is valid.
        // This is required. The auth call will return a playerToken that is used in subsequent calls.
        Debug.LogError($"GameTester.Api.Auth");
        m_runner.StartCoroutine(GameTester.Api.Auth(o => 
        {
            if (o.Code == GameTesterResponseCode.InvalidPlayerToken)
            {
                Debug.LogError($"Authentication failed!");
                // Display authentication error, prevent starting the test.
                AuthenticationSuccessful = false;
            }
            else if (o.Code == GameTesterResponseCode.Success)
            {
                Debug.LogError($"Authentication successful!");
                // Authentication success. The test can proceed.
                AuthenticationSuccessful = true;
            }
            
            Debug.LogError($"Done and Authenticated result: {o.Code}");
            
            Authenticated = true;
        }));
    }
}
