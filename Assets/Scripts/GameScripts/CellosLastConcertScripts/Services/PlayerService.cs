using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Cysharp.Threading.Tasks;
using RibCageGames.Base;
using RibCageGames.Combat;
using RibCageGames.Input;
using RibCageGames.Music;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.Serialization;

[CreateAssetMenu(fileName = "PlayerService", menuName = "AlphaNomos/Services/PlayerService")]
public class PlayerService : BaseService, IInventory
{
    [SerializeField][Range(0f, 1f)] private float m_retainedHealthPercentage;
    [SerializeField][Range(0f, 1f)] private float m_minimalHealthPercentage;
    
    [SerializeField] private StyleTierSettings m_tierSettings;
    
    public UnityEvent<CharacterControllerBase> OnPlayerRegistered { get; set; } = new UnityEvent<CharacterControllerBase>();
    public UnityEvent<bool> OnPlayerInput { get; } = new UnityEvent<bool>();
    public UnityEvent<ActionData> OnPlayerMoveStarted { get; } = new UnityEvent<ActionData>();
    public UnityEvent<float, float> OnPlayerHealthChanged { get; private set; } = new UnityEvent<float, float>();

    [SerializeField] public UnityEvent<float> OnCurrentPlayerHealthChanged = new UnityEvent<float>();
    public UnityEvent<Dictionary<PowerUpType, (int, BonusEffect, Powerup)>> OnInventoryChanged { get; private set; } = new UnityEvent<Dictionary<PowerUpType, (int, BonusEffect, Powerup)>>();

    public Dictionary<PowerUpType, (int, BonusEffect, Powerup)> CurrentInventory => m_powerUpLevelDict;

    public UnityEvent<ActionData, List<(DamageableRegistry.IDamageable, float)>> OnAttackLanded { get; } = new UnityEvent<ActionData, List<(DamageableRegistry.IDamageable, float)>>();
    //TODO: these two shouldn't be here
    public UnityEvent OnEncounterStarted { get; private set; } = new UnityEvent();
    public UnityEvent OnEncounterComplete { get; private set; } = new UnityEvent();

    public int MaxHealth => m_player.MaxHealth;
    
    public CharacterControllerBase PlayerController => m_player;
    
    private MonoService m_monoService;
    private BeatSystem m_beatSystem;
    private CharacterControllerBase m_player;
    private Dictionary<PowerUpType, (int level, BonusEffect bonus, Powerup source)> m_powerUpLevelDict;
    private float m_preEncounterHealth;
    private bool m_playerActive = false;

    [SerializeField] private float m_styleAmount = 0f;
    public int StyleLevel { get; private set; }

    [FormerlySerializedAs("m_styleGuage")] [SerializeField] private float m_styleGauge;

    public float StyleGauge => m_styleGauge;
    public StyleTier StyleTierData => m_tierSettings[StyleLevel];
    public Vector3 EncounterStartPosition { get; private set; }
    
    public Dictionary<PowerUpType, (int, BonusEffect, Powerup)> PowerUpLevelDict => m_powerUpLevelDict;

    public int GetPowerUpLevel(PowerUpType type)
    {
        if (m_powerUpLevelDict.TryGetValue(type, out (int, BonusEffect, Powerup) value))
        {
            return value.Item1;
        }
        else
        {
            return 0;
        }
    }
    
    private Dictionary<WeaponTypes, CharacterControllerBase> m_playerObjectsDict;

    public void RegisterPlayer(CharacterControllerBase player, WeaponTypes weaponType)
    {
        m_playerObjectsDict.Add(weaponType, player);
        DeactivatePlayer(player);
    }

    public void SwitchToXyloblades()
    {
        SwitchPlayer(WeaponTypes.Xyloblades);
    }
    
    public void SwitchToSaXword()
    {
        SwitchPlayer(WeaponTypes.SaXword);
    }
    
    public void SwitchToOwlClaw()
    {
        SwitchPlayer(WeaponTypes.OwlClaw);
    }

    public void SwitchPlayer(WeaponTypes weaponType)
    {
        bool switchPlayer = false;
        CharacterControllerBase oldPlayerReference = m_player;
        if (m_player != null)
        {
            oldPlayerReference.OnMoveStarted.RemoveListener(PlayerMoveStarted);
            oldPlayerReference.OnHealthChanged.RemoveListener(OnPlayerHealthChanged.Invoke);
            oldPlayerReference.OnAttackLanded.RemoveListener(OnAttackLanded.Invoke);
            DeactivatePlayer();
            switchPlayer = true;
        }
        m_player = m_playerObjectsDict[weaponType];
        m_player.OnMoveStarted.AddListener(PlayerMoveStarted);
        m_player.OnHealthChanged.AddListener(OnPlayerHealthChanged.Invoke);
        m_player.OnAttackLanded.AddListener(OnAttackLanded.Invoke);
        m_player.gameObject.SetActive(m_playerActive);
        DeactivatePlayer();
        OnPlayerRegistered?.Invoke(m_player);
        
        if (switchPlayer)
        {
            ActivatePlayer();
            m_player.RepositionEntity(oldPlayerReference.transform.position, oldPlayerReference.transform.rotation);
        }
    }

    public void ActivatePlayer()
    {
        if (m_player == null)
        {
            SwitchPlayer(WeaponTypes.SaXword);
        }

        m_playerActive = true;
        m_player.ResetCombatEntity(false);
        m_player.gameObject.SetActive(m_playerActive);
        m_player.ActivateCombatEntity(false);
    }
    
    public void EnableNonMovementControls()
    {
        m_player.RegisterNonMovementControls();
    }
    
    public void EnableMovementControls()
    {
        m_player.RegisterMovementControls();
    }
    
    public void DisableMovementControls()
    {
        m_player.DeregisterMovementControls();
    }
    
    public void ToggleWeaponVisual(bool weaponVisualActive)
    {
        m_player.ToggleWeaponVisual(weaponVisualActive);
    }
    
    public void DisableNonMovementControls()
    {
        m_player.DeregisterNonMovementControls();
    }
    
    public void SetGraceMode(bool state)
    {
        foreach (CharacterControllerBase character in m_playerObjectsDict.Values)
        {
            character.SetGracePeriod(state);
        }
    }
    
    public void SetGodMode(bool state)
    {
        foreach (CharacterControllerBase character in m_playerObjectsDict.Values)
        {
            character.SetGodMode(state);
        }
    }
    
    public void DeactivatePlayer()
    {
        m_playerActive = false;
        if (m_player != null)
        {
            DeactivatePlayer(m_player);
        }
    }
    
    public void DeactivatePlayer(CharacterControllerBase player)
    {
        player.gameObject.SetActive(false);
        player.DeactivateCombatEntity(true);
    }
    
    public void MovePlayer(Transform target)
    {
        _ = ForceMovePlayer(target);
    }
    
    public async Task ForceMovePlayer(Transform target)
    {
        await m_player.ForceMovement(target.position);
    }
    
    public void ControlSpawnAnimation(bool value)
    {
        PlayerController.ControlSpawnAnimation(value);
    }

    private readonly HashSet<ActionData> m_performedMoves = new HashSet<ActionData>();

    private void PlayerMoveStarted(ActionData move, PlayerInputActions initiatingAction)
    {
        if (m_player.CurrentMoveJust)
        {
            if (!m_performedMoves.Contains(move))
            {
                m_styleAmount += StyleTierData.OnBeatMoveFill;
                m_performedMoves.Add(move);
                RemovePerformedMove(move, StyleTierData.MoveRefreshDelay).Forget();
            }
            else
            {
                m_styleAmount += StyleTierData.OnBeatMoveFill * StyleTierData.OnRepetedMultiplier;
            }
        }
        else
        {
            m_styleAmount -= StyleTierData.OffBeatMoveReduction;
        }

        OnPlayerInput?.Invoke(m_player.CurrentMoveJust);
        OnPlayerMoveStarted?.Invoke(move);
    }

    private async UniTaskVoid RemovePerformedMove(ActionData move, float delay)
    {
        await UniTask.WaitForSeconds(delay);
        m_performedMoves.Remove(move);
    }

    public override void Init(GameObject servicePrefab = null)
    {
        m_powerUpLevelDict = new Dictionary<PowerUpType, (int, BonusEffect, Powerup)>();
        m_playerObjectsDict = new Dictionary<WeaponTypes, CharacterControllerBase>();
        OnEncounterStarted.AddListener(SavePlayerPreviousHealth);
        OnEncounterComplete.AddListener(SetPlayerRetainedHealth);
        
        m_monoService = ServiceLocator.Get<MonoService>();
        
        m_monoService.OnUpdate.AddListener(ControlledUpdate);
        m_styleGauge = 0f;
        StyleLevel = 1;
        m_styleAmount = StyleTierData.InitialPercentage;

        OnPlayerHealthChanged.RemoveListener(CallCurrentPlayerHealthEvent);
        OnPlayerHealthChanged.AddListener(CallCurrentPlayerHealthEvent);
    }

    private void CallCurrentPlayerHealthEvent(float oldHp, float newHP)
    {
        OnCurrentPlayerHealthChanged?.Invoke(100f * newHP);
    }

    private void ControlledUpdate(float delta)
    {
        if (m_player == null)
        {
            return;
        }
        if(!m_player.Busy)
        {
            m_styleAmount -= delta * StyleTierData.PerSecondDecrease;
        }
        m_styleGauge = m_styleAmount / StyleTierData.StyleCapacity;

        if (StyleGauge > 1f && StyleLevel < m_tierSettings.LevelCount - 1)
        {
            StyleLevel++;
            m_styleAmount = StyleTierData.InitialPercentage * StyleTierData.StyleCapacity;
            m_player.BaseDamage = StyleTierData.BaseDamageMultiplier;
        }
        else if (StyleGauge < 0f && ((m_player != null && !m_player.GodMode && StyleLevel > 0) || StyleLevel > 1))
        {
            StyleLevel--;
            m_styleAmount = StyleTierData.RetainedPercentage * StyleTierData.StyleCapacity;
            m_player.BaseDamage = StyleTierData.BaseDamageMultiplier;
        }
        else
        {
            m_styleAmount = Mathf.Clamp(m_styleAmount, 0f, StyleTierData.StyleCapacity);
        }

        m_styleGauge = m_styleAmount / StyleTierData.StyleCapacity;
        m_styleGauge = Mathf.Clamp01(StyleGauge);
    }

    public void ResetPlayerPreviousHealth()
    {
        m_preEncounterHealth = m_player.MaxHealth;
    }
    
    private void SavePlayerPreviousHealth()
    {
        m_preEncounterHealth = m_player.CurrentHealth;
        EncounterStartPosition = m_player.ConnectedTransform.position;
    }

    private void SetPlayerRetainedHealth()
    {
        int newHealth =
            (int) Mathf.Max(
                m_player.CurrentHealth + (m_preEncounterHealth - m_player.CurrentHealth) * m_retainedHealthPercentage,
                m_player.MaxHealth * m_minimalHealthPercentage);
        m_player.CurrentHealth = newHealth;
    }

    public override void StartService(MonoInjector injector)
    {
        m_beatSystem = ServiceLocator.Get<BeatSystem>();
    }

    public void PositionPlayer(Transform spawnPoint)
    {
        ActivatePlayer();
        //m_player.RepositionEntity(spawnPoint.position, spawnPoint.rotation);
        m_player.RepositionEntity(spawnPoint.position + Vector3.up * 2f, Quaternion.identity);//spawnPoint.rotation);
        EncounterStartPosition = spawnPoint.position + Vector3.up * 2f;
    }

    public override void Dispose()
    {
        m_player.OnMoveStarted.AddListener(PlayerMoveStarted);
    }
    
    public void ReducePowerupLevel(PowerUpType type, int levelsReduced = 1)
    {
        if(m_powerUpLevelDict.TryGetValue(type, out (int level, BonusEffect bonus, Powerup sourcePowerup) powerup))
        {
            powerup.bonus.RemoveBonus();
            m_powerUpLevelDict.Remove(type);
            
            int newLevel = powerup.level;
            Powerup currentPowerup = powerup.sourcePowerup;
            for (int i = 0; i < levelsReduced; i++)
            {
                currentPowerup = currentPowerup.PowerupPreviousLevel;
                newLevel--;
                if(currentPowerup == null)
                {
                    break;
                }
            }

            if (currentPowerup != null)
            {
                m_powerUpLevelDict[type] = (newLevel, powerup.bonus, powerup.sourcePowerup);
                currentPowerup.UpgradeBonusLevel();
            }
        }
        
        //m_powerUpLevelDict[type] = (level, bonus, sourcePowerup);
        
        //OnInventoryChanged.Invoke(m_powerUpLevelDict);
    }
    
    public void AddBonusEffect(BonusEffect bonus, Powerup sourcePowerup, int level)
    {
        //Debug.LogError($"Adding bonus {bonus.BonusType} with powerup {sourcePowerup.DisplayName} of level {level}");

        if (m_powerUpLevelDict.TryGetValue(sourcePowerup.Type, out (int level, BonusEffect bonus, Powerup sourcePowerup) powerup))
        {
            powerup.bonus.RemoveBonus();
        }

        m_powerUpLevelDict[bonus.BonusType] = (level, bonus, sourcePowerup);

        if (sourcePowerup.Type == PowerUpType.PowerupUpgrade)
        {
            switch (bonus.BonusType)
            {
                case PowerUpType.ReverbUpgrade:
                    m_powerUpLevelDict[PowerUpType.Reverb].source.UpgradeBonusLevel();
                    break;
                case PowerUpType.DelayUpgrade:
                    m_powerUpLevelDict[PowerUpType.Delay].source.UpgradeBonusLevel();
                    break;
                case PowerUpType.FlangerUpgrade:
                    m_powerUpLevelDict[PowerUpType.Flanger].source.UpgradeBonusLevel();
                    break;
            }
        }
        //else if(sourcePowerup.Type != PowerUpType.MaxHealth)
        //{
            OnInventoryChanged.Invoke(m_powerUpLevelDict);
        //}
    }
    
    public void ClearPowerUps()
    {
        foreach ((int, BonusEffect, Powerup) powerup in m_powerUpLevelDict.Values)
        {
            powerup.Item2.RemoveBonus();
        }
        m_powerUpLevelDict.Clear();
        
        OnInventoryChanged.Invoke(m_powerUpLevelDict);
    }
}

public enum WeaponTypes
{
    SaXword = 0,
    Xyloblades = 1,
    OwlClaw = 20,
}
