using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using RibCageGames.Base;
using RibCageGames.Input;
using Unity.Cinemachine;
using UnityEngine;

[CreateAssetMenu(fileName = "CameraService", menuName = "AlphaNomos/Services/CameraService")]
public class CameraService : BaseService
{
    public Vector3 MainCameraHeading
    {
        get
        {
            Vector3 cameraHeading = m_cameraController.Brain.transform.forward;
            cameraHeading.y = 0f;
            
            return cameraHeading;
        }
    }

    public Camera ConcreteCamera => m_cameraController.ConcreteCamera;

    private CameraController m_cameraController;
    private CinemachineVirtualCameraBase m_camera;
    private InputService m_inputService;
    private PlayerFollower m_playerFollower;

    private List<CinemachineBasicMultiChannelPerlin> m_noiseModules;

    public void InjectCamera(CameraController camera)
    {
        m_cameraController = camera;
        m_camera = camera.MainCamera;

        m_noiseModules = new List<CinemachineBasicMultiChannelPerlin>();


        CinemachineCamera mainCamera = (CinemachineCamera) m_camera;

        //for (int i = 0; i < 3; i++)
        //{
        //    m_noiseModules.Add(mainCamera.GetRig(i)
        //        .GetCinemachineComponent<CinemachineBasicMultiChannelPerlin>());
        //}
        m_noiseModules.Add((CinemachineBasicMultiChannelPerlin) mainCamera.GetCinemachineComponent(CinemachineCore.Stage.Noise));
            
        PlayerService playerService = ServiceLocator.Get<PlayerService>();
        playerService.OnPlayerRegistered.AddListener((player) =>
        {
            Transform characterTransform = player.transform;
            if (m_temporaryPlayerCamera != null)
            {
                m_temporaryPlayerCamera.Follow = characterTransform;
                m_temporaryPlayerCamera.LookAt = characterTransform;
            }
        });
    }

    private CinemachineCamera m_temporaryPlayerCamera = null;
    public async void SetTemporaryPlayerCamera(CinemachineCamera camera)
    {
        if (m_temporaryPlayerCamera != null)
        {
            ResetDefaultPlayerCamera(true);
        }

        m_temporaryPlayerCamera = camera;
        m_temporaryPlayerCamera.enabled = true;

        PlayerService playerService = ServiceLocator.Get<PlayerService>();
        CharacterControllerBase character = playerService.PlayerController;

        await UniTask.WaitWhile(() =>
        {
            character = playerService.PlayerController;
            return character == null;
        });

        Transform characterTransform = character.transform;
        
        camera.Follow = characterTransform;
        camera.LookAt = characterTransform;

        //m_noiseModules.Add(camera.GetCinemachineComponent<CinemachineBasicMultiChannelPerlin>());
        m_noiseModules.Add((CinemachineBasicMultiChannelPerlin) camera.GetCinemachineComponent(CinemachineCore.Stage.Noise));
        SetAllRigNoise(null, 0f);
    }

    public void ResetDefaultPlayerCamera()
    {
        ResetDefaultPlayerCamera(true);
    }
    
    public void ResetDefaultPlayerCamera(bool disableComponent)
    {
        if (m_temporaryPlayerCamera != null)
        {
            m_noiseModules.Remove((CinemachineBasicMultiChannelPerlin) m_temporaryPlayerCamera
                .GetCinemachineComponent(CinemachineCore.Stage.Noise));
            if (disableComponent)
            {
                m_temporaryPlayerCamera.enabled = false;
            }
            m_temporaryPlayerCamera = null;
        }
    }

    public override void Init(GameObject servicePrefab = null) { }

    public override void StartService(MonoInjector injector)
    {
        m_inputService = ServiceLocator.Get<InputService>();
        //ServiceLocator.Get<LevelManagementService>().OnLevelReset.AddListener(PositionPlayer);
    }

    public void ShakeCamera(CameraShakeSettings settings)
    {
        float inCounter = 0f; //TODO: fix MonoProcess elapsed time and use instead of counters
        float outCounter = 0f;

        MonoProcess.New().WaitFor(() =>
            {
                inCounter += Time.deltaTime;

                float relativeTime = inCounter / settings.InDuration;

                float curveValue = settings.InCurve.Evaluate(relativeTime);

                SetAllRigNoise(settings, curveValue);

                return relativeTime > 1f;
            }, settings.InDuration)
            .Do(() => SetAllRigNoise(settings, 1f))
            .WaitForSeconds(settings.Duration - settings.InDuration - settings.OutDuration)
            .WaitFor(() =>
            {
                outCounter += Time.deltaTime;

                float relativeTime = outCounter / settings.OutDuration;

                float curveValue = settings.OutCurve.Evaluate(relativeTime);

                SetAllRigNoise(settings, curveValue);

                return relativeTime > 1f;
            }, settings.InDuration)
            .Do(() => SetAllRigNoise(settings, 0f));

    }

    private void SetAllRigNoise(CameraShakeSettings settings, float curveValue)
    {
        foreach (CinemachineBasicMultiChannelPerlin noiseModule in m_noiseModules)
        {
            noiseModule.PivotOffset = curveValue > Mathf.Epsilon ? settings.PivotOffset * curveValue : Vector3.zero;
            noiseModule.AmplitudeGain = curveValue > Mathf.Epsilon ? settings.AmplitudeGain * curveValue : 0f;
            noiseModule.FrequencyGain = curveValue > Mathf.Epsilon ? settings.FrequencyGain * curveValue : 0f;
        }
    }

    public override void Dispose()
    {
        //m_player.OnMoveStarted.AddListener(PlayerMoveStarted);
    }

    public void ActivateFocusCameraShot(Transform target, Vector3 followOffset, Vector3 trackedObjectOffset, FocusCameraShot focusCameraShot, bool disable = true, bool useProxy = false)
    {
        CinemachineCamera focusCamera = m_cameraController.Cameras[useProxy ? CameraShotType.FocusShotProxy : CameraShotType.FocusShot];
        CinemachineCamera proxyCamera = m_cameraController.Cameras[useProxy ? CameraShotType.FocusShot : CameraShotType.FocusShotProxy];
        
        //CinemachineVirtualCamera camera = useProxy ? proxyCamera : focusCamera;

        int activePriority = Mathf.Max(focusCamera.Priority, proxyCamera.Priority);
        int inactivePriority = Mathf.Min(focusCamera.Priority, proxyCamera.Priority);

        focusCamera.Priority = activePriority;
        proxyCamera.Priority = inactivePriority;

        CinemachineFollow focusCameraFollow = (CinemachineFollow) focusCamera.GetCinemachineComponent(CinemachineCore.Stage.Body);
        CinemachineRotationComposer focusCameraAim = (CinemachineRotationComposer) focusCamera.GetCinemachineComponent(CinemachineCore.Stage.Aim);
        
        focusCamera.Follow = target;
        focusCamera.LookAt = target;
        focusCameraFollow.FollowOffset = followOffset;
        focusCameraAim.TargetOffset = trackedObjectOffset;
        focusCameraAim.Composition.ScreenPosition = focusCameraShot.ScreenPosition;
        //focusCameraFollow.TrackerSettings. = followOffset;
        //focusCameraFollow. = followOffset;
        //focusCamera.GetCinemachineComponent<CinemachineComposer>().m_TrackedObjectOffset = trackedObjectOffset;
        //focusCamera.GetCinemachineComponent<CinemachineComposer>().m_ScreenX = focusCameraShot.ScreenPosition.x;
        //focusCamera.GetCinemachineComponent<CinemachineComposer>().m_ScreenY = focusCameraShot.ScreenPosition.y;
        ActivateShotCamera(focusCamera);

        if (focusCameraShot.BlockInput)
        {
            m_inputService.DisableInput(null);
        }

        if(disable)
        {
            MonoProcess.WaitForSecondsProcess(focusCameraShot.ShotDuration)
                .Do(() =>
                {
                    DeactivateShotCamera(focusCamera, focusCameraShot.BlockInput);
                    DeactivateShotCamera(proxyCamera, focusCameraShot.BlockInput);
                });
        }
        
        //MonoProcess.WaitForSecondsProcess(focusCameraShot.ShotDuration)
        //    .Do(() => DeactivateShotCamera(focusCamera));
    }
    
    public void ActivateDollyCameraShot(DollyCameraShotSettings dollyCameraShot)
    {
        /*
        CinemachineSplineDolly camera = (CinemachineSplineDolly) m_cameraController.Cameras[CameraShotType.DollyTrack].GetCinemachineComponent(CinemachineCore.Stage.Body);

        List<CinemachineSmoothPath.Waypoint> waypoints = new List<CinemachineSmoothPath.Waypoint>();

        foreach (Vector3 waypoint in dollyCameraShot.DollyWaypoints)
        {
            CinemachineSmoothPath.Waypoint newWaypoint = new CinemachineSmoothPath.Waypoint {position = waypoint};
            waypoints.Add(newWaypoint);
        }
        
        ((CinemachineSmoothPath) camera.GetCinemachineComponent<CinemachineTrackedDolly>().m_Path).m_Waypoints = waypoints.ToArray();
        
        camera.Spline = dollyCameraShot.
        
        if (dollyCameraShot.BlockInput)
        {
            m_inputService.DisableInput(null);
        }
        
        ActivateShotCamera(camera);

        MonoProcess.WaitForSecondsProcess(dollyCameraShot.ShotDuration)
            .Do(() => DeactivateShotCamera(camera, dollyCameraShot.BlockInput));
            */
    }

    public void ActivateFreeFormShot(CinemachineCamera camera, FreeFormCameraShot settings)
    {
        //Debug.LogError($"Activate cameras shot {camera}");
        ActivateShotCamera(camera);
        
        if (settings.BlockInput)
        {
            m_inputService.DisableInput(null);
        }

        MonoProcess.WaitForSecondsProcess(settings.ShotDuration)
            .Do(() => DeactivateShotCamera(camera, settings.BlockInput));
    }

    private void ActivateShotCamera(CinemachineCamera camera)
    {
        camera.enabled = true;
    }
    
    private void DeactivateShotCamera(CinemachineCamera camera, bool blockInput)
    {
        if (blockInput)
        {
            m_inputService.EnableInput();   
        }
        camera.enabled = false;
    }

    //Theoretical camera swap system that allows smooth blends between camera shots of the same definition using a swapping mechanism
    //private class CameraControl<T> where T : CinemachineVirtualCameraBase
    //{
    //    private T m_activeCamera;
    //    private T m_proxyCamera;
    //    
    //    public CameraControl(T baseCamera)
    //    {
    //        
    //    }
    //}
    public void RegisterPlayerFollower(PlayerFollower playerFollower)
    {
        m_playerFollower = playerFollower;
    }
}

public enum CameraShotType
{
    DollyTrack,
    FocusShot,
    FocusShotProxy,
    FollowShot,
    CompositeShot,
}