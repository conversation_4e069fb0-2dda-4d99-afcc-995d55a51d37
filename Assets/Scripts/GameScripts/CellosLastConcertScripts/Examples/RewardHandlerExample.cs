using UnityEngine;

/// <summary>
/// Example script demonstrating how to use the RewardHandler system
/// </summary>
public class RewardHandlerExample : MonoBehaviour
{
    [Header("References")]
    [SerializeField] private RewardHandler m_rewardHandler;
    
    [Header("Spawn Settings")]
    [SerializeField] private Transform[] m_spawnPoints;
    [SerializeField] private PickupType[] m_availablePickupTypes = { PickupType.Powerup, PickupType.HPUpgrade };
    
    private void Start()
    {
        // Subscribe to reward handler events
        if (m_rewardHandler != null)
        {
            m_rewardHandler.OnPickupSpawned.AddListener(OnPickupSpawned);
            m_rewardHandler.OnPickupCollected.AddListener(OnPickupCollected);
        }
    }
    
    private void OnDestroy()
    {
        // Unsubscribe from events
        if (m_rewardHandler != null)
        {
            m_rewardHandler.OnPickupSpawned.RemoveListener(OnPickupSpawned);
            m_rewardHandler.OnPickupCollected.RemoveListener(OnPickupCollected);
        }
    }
    
    /// <summary>
    /// Called when a pickup is spawned
    /// </summary>
    private void OnPickupSpawned(PickupType pickupType, PickupSpawnBase pickup)
    {
        Debug.Log($"Pickup spawned: {pickupType} at {pickup.transform.position}");
    }
    
    /// <summary>
    /// Called when a pickup is collected
    /// </summary>
    private void OnPickupCollected(PickupType pickupType, PickupSpawnBase pickup)
    {
        Debug.Log($"Pickup collected: {pickupType} by player");
    }
    
    /// <summary>
    /// Spawns a single random pickup at the first spawn point
    /// </summary>
    [ContextMenu("Spawn Random Pickup")]
    public void SpawnRandomPickup()
    {
        if (m_rewardHandler == null || m_spawnPoints.Length == 0 || m_availablePickupTypes.Length == 0)
        {
            Debug.LogWarning("RewardHandlerExample: Missing required references");
            return;
        }
        
        PickupType randomType = m_availablePickupTypes[Random.Range(0, m_availablePickupTypes.Length)];
        m_rewardHandler.SpawnPickup(randomType);
    }
    
    /// <summary>
    /// Clears all active pickups
    /// </summary>
    [ContextMenu("Clear All Pickups")]
    public void ClearAllPickups()
    {
        if (m_rewardHandler != null)
        {
            m_rewardHandler.ClearAllPickups();
        }
    }
}
