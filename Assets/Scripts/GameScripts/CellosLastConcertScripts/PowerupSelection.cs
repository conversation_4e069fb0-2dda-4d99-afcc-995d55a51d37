using System;
using System.Collections.Generic;
using RibCageGames.Base;
using UnityEngine;
using UnityEngine.Events;
using Random = UnityEngine.Random;

public class PowerupSelection : PickupSpawnBase
{
    [SerializeField] private ItemPool m_itemPool;
    [SerializeField] private List<Transform> m_powerupLocators;
    [SerializeField] private List<PowerUpPickup> m_powerupOptions;
    
    private Dictionary<PowerUpType, PowerUpPickup> m_powerupOptionsDict;
    
    private List<PowerUpPickup> m_activePowerupOptions = new();

    public List<PowerUpPickup> ActivePowerupOptions => m_activePowerupOptions;
    
    public override void Initialize()
    {
        if (m_initialized)
        {
            return;
        }
        
        base.Initialize();

        m_powerupOptionsDict = new Dictionary<PowerUpType, PowerUpPickup>();
        foreach (PowerUpPickup powerupOption in m_powerupOptions)
        {
            m_powerupOptionsDict.Add(powerupOption.PowerUp.Type, powerupOption);
            powerupOption.OnPickup.AddListener(() => OnPickupSelected(powerupOption));
            powerupOption.Initialize();
            powerupOption.Deactivate();
        }
    }

    protected override void OnPickupAction()
    {
    }

    public override void Activate()
    {
        base.Activate();

        SetPowerupOptions();
        foreach (PowerUpPickup powerup in m_activePowerupOptions)
        {
            powerup.Activate();
        }
    }

    public void OnPickupSelected(PowerUpPickup selected)
    {
        m_onPickup?.Invoke();
        foreach (PowerUpPickup powerup in m_activePowerupOptions)
        {
            if (powerup != selected)
            {
                powerup.gameObject.SetActive(false);
            }

            powerup.Deactivate(true);
        }
    }

    public void SetPowerupOptions()
    {
        m_activePowerupOptions.Clear();
        
        var items = m_itemPool.GetUniquePowerupChoices(2);
        for (int i = 0; i < items.Count; i++)
        {
            m_powerupOptionsDict[items[i].Type].transform.position = m_powerupLocators[i].transform.position;
            m_powerupOptionsDict[items[i].Type].transform.rotation = m_powerupLocators[i].transform.rotation;
            m_powerupOptionsDict[items[i].Type].gameObject.SetActive(true);
            m_activePowerupOptions.Add(m_powerupOptionsDict[items[i].Type]);
        }
    }
}
