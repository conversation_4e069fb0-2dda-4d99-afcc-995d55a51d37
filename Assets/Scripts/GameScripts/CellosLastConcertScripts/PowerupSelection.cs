using System;
using System.Collections.Generic;
using UnityEngine;

public class PowerupSelection : PickupSpawnBase
{
    [SerializeField] private ItemPool m_itemPool;
    [SerializeField] private List<Transform> m_powerupLocators;
    [SerializeField] private List<PowerUpPickup> m_powerupOptions;
    
    [SerializeField] private int m_defaultPrice = 0;
    [SerializeField] private int m_defaultOptions = 2;
    
    private Dictionary<PowerUpType, PowerUpPickup> m_powerupOptionsDict;
    private List<PowerUpPickup> m_activePowerupOptions = new();
    public List<PowerUpPickup> ActivePowerupOptions => m_activePowerupOptions;
    
    private int m_junkPrice;
    private int m_options;

    protected override void Start()
    {
        base.Start();
        if (m_junkPrice == 0)
        {
            m_junkPrice = m_defaultPrice;
        }

        if (m_options == 0)
        {
            m_options = m_defaultOptions;
        }
    }

    public override void Initialize()
    {
        if (m_initialized)
        {
            return;
        }
        
        base.Initialize();

        m_powerupOptionsDict = new Dictionary<PowerUpType, PowerUpPickup>();
        foreach (PowerUpPickup powerupOption in m_powerupOptions)
        {
            m_powerupOptionsDict.Add(powerupOption.PowerUp.Type, powerupOption);
            powerupOption.OnPickup.AddListener(() => OnPickupSelected(powerupOption));
            powerupOption.Initialize();
            powerupOption.Deactivate();
        }
    }
    
    public void SetAdditionalData(int options, int junkPrice)
    {
        m_options = options;
        m_junkPrice = junkPrice;
    }

    protected override void OnPickupAction()
    {
    }

    public override void Activate()
    {
        base.Activate();

        SetPowerupOptions(2);
        foreach (PowerUpPickup powerup in m_activePowerupOptions)
        {
            powerup.Activate();
        }
    }

    public void OnPickupSelected(PowerUpPickup selected)
    {
        m_onPickup?.Invoke();
        foreach (PowerUpPickup powerup in m_activePowerupOptions)
        {
            if (powerup != selected)
            {
                powerup.gameObject.SetActive(false);
            }

            powerup.Deactivate(true);
        }
    }

    public void SetPowerupOptions(int options)
    {
        m_activePowerupOptions.Clear();
        
        var items = m_itemPool.GetUniquePowerupChoices(options);
        for (int i = 0; i < items.Count; i++)
        {
            m_powerupOptionsDict[items[i].Type].transform.position = m_powerupLocators[i].transform.position;
            m_powerupOptionsDict[items[i].Type].transform.rotation = m_powerupLocators[i].transform.rotation;
            m_powerupOptionsDict[items[i].Type].gameObject.SetActive(true);
            m_activePowerupOptions.Add(m_powerupOptionsDict[items[i].Type]);
            if (m_junkPrice > 0)
            {
                m_powerupOptionsDict[items[i].Type].SetJunkPrice(m_junkPrice);
            }
        }
    }
}
