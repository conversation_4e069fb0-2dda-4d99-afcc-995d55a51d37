using System;
using System.Collections;
using System.Collections.Generic;
using RibCageGames.Base;
using RibCageGames.Input;
using UnityEngine;
using ActionData = RibCageGames.Combat.ActionData;

public class HotSwapInputDisplay : MonoBehaviour
{
    [SerializeField] private List<SingleHotSwapInputOption> m_inputOptionDisplays;
    private CharacterControllerBase m_playerReference;
    private ActionData m_moveExecuting;
    private PlayerInputActions m_executingAction;
    private Dictionary<CharacterControllerBase.CharacterState, List<InputOptionData>> m_basicMoves = new Dictionary<CharacterControllerBase.CharacterState, List<InputOptionData>>();

    private IEnumerator Start()
    {
        yield return new WaitForSeconds(1f);

        m_playerReference = ServiceLocator.Get<PlayerService>().PlayerController;

        foreach (KeyValuePair<PlayerInputActions, List<CharacterControllerBase.BasicMove>> basicMove in
            m_playerReference.BasicMoveInputDict)
        {
            foreach (CharacterControllerBase.BasicMove move in basicMove.Value)
            {

                if (!m_basicMoves.ContainsKey(move.State))
                {
                    m_basicMoves[move.State] = new List<InputOptionData>();
                }

                m_basicMoves[move.State].Add(new InputOptionData
                {
                    InputAction = basicMove.Key,
                    InputType = PlayerInputTypes.Started,
                    ActionName = move.ActionData.DisplayName,
                });
            }
        }

        m_playerReference.OnMoveStarted.AddListener(UpdateInputDisplays);
        m_playerReference.OnMoveEnded.AddListener(UpdateInputDisplaysBasicMoves);
        m_playerReference.OnDamageTaken.AddListener(UpdateInputDisplaysBasicMoves);
        //m_playerReference.OnMoveCancelled.AddListener(UpdateInputDisplaysBasicMoves);
        //TODO: make current move accessible?

        ServiceLocator.Get<MonoService>().OnUpdate.AddListener(UpdateInputDisplays);

        UpdateInputDisplaysBasicMoves();
    }

    private void UpdateInputDisplaysBasicMoves(float damage)
    {
        UpdateInputDisplaysBasicMoves();
    }

    private void UpdateInputDisplays(float delta)
    {
        if (m_moveExecuting != null)
        {
            UpdateInputDisplays(m_moveExecuting, m_executingAction);
        }
        else
        {
            UpdateInputDisplays(m_basicMoves[m_playerReference.CurrentState]);
        }
    }

    private void UpdateInputDisplaysBasicMoves(ActionData move = null)
    {
        m_moveExecuting = null;
        m_executingAction = PlayerInputActions.Default;
        //UpdateInputDisplays(m_basicMoves[m_playerReference.CurrentState]);
    }

    private void UpdateInputDisplays(ActionData move, PlayerInputActions initiatingAction)
    {
        m_moveExecuting = move;
        m_executingAction = initiatingAction;
        m_executingAction = initiatingAction;
        List<InputOptionData> inputOptions = new List<InputOptionData>();
        foreach (ActionData.InputFollowingOption followingOption in move.FollowingActions)
        {
            inputOptions.Add(new InputOptionData
            {
                InputAction = followingOption.InputAction,
                InputType = followingOption.InputType,
                ActionName = followingOption.Action.DisplayName,
            });
        }

        UpdateInputDisplays(inputOptions);
    }

    private void UpdateInputDisplays(List<InputOptionData> inputOptions)
    {
        for (int i = 0; i < m_inputOptionDisplays.Count; i++)
        {
            m_inputOptionDisplays[i].InjectData(
                inputOptions.Count > i ? inputOptions[i] : InputOptionData.Empty);
        }
    }

    public struct InputOptionData
    {
        public PlayerInputActions InputAction;
        public PlayerInputTypes InputType;
        public string ActionName;

        public static InputOptionData Empty => new InputOptionData
        {
            InputAction = PlayerInputActions.Default,
            InputType = PlayerInputTypes.Held,
            ActionName = String.Empty,
        };

        public static bool Equals(InputOptionData a, InputOptionData b)
        {
            return a.InputAction == b.InputAction && a.InputType == b.InputType && a.ActionName.Equals(b.ActionName);
        }

        public bool IsEmpty => Equals(this, Empty);
    }
}
