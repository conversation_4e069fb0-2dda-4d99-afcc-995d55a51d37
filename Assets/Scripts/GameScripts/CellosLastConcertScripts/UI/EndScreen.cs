using System;
using RibCageGames.Base;
using RibCageGames.Input;
using RibCageGames.UI;
using UnityEngine;
using TMPro;
using UnityEngine.InputSystem;

public class EndScreen : MonoBehaviour
{
    [SerializeField] private string m_steamLink = "https://store.steampowered.com/app/2529960/Alpha_Nomos/";
    [SerializeField] private TextMeshProUGUI m_thankyouText;
    [SerializeField] private TextMeshProUGUI m_timeText;
    [SerializeField] private string m_timePrefix = "Time: ";

    private LevelManagementService m_levelManager;
    private InputService m_inputService;

    private void OnEnable()
    {
        m_levelManager = ServiceLocator.Get<LevelManagementService>();
        m_inputService = ServiceLocator.Get<InputService>();

        //m_deathsText.text = $" X {m_levelManager.Deaths}";
        m_inputService.EnableMouse();

        int secondsInGame = (int) m_levelManager.TimeInGame;
        int minutesInGame = secondsInGame / 60;
        
        secondsInGame %= 60;

        string minutes = minutesInGame < 10 ? ("0" + minutesInGame) : (minutesInGame.ToString());
        string seconds = secondsInGame < 10 ? ("0" + secondsInGame) : (secondsInGame.ToString());
        
        m_timeText.text = String.Format(m_timePrefix, minutes, seconds);
        
        m_thankyouText.text = String.Format(m_thankyouText.text, Application.version);

        if (ServiceLocator.Get<PopupService>().TryGetElement(out TrailerVideoPlayerTrigger videoPlayer))
        {
            videoPlayer.StartDelayedTrigger();
        }
        //ServiceLocator.Get<PopupService>().SetTimerEnabled(false);
    }

    public void OpenSteamLink()
    {
        Application.OpenURL(m_steamLink);
    }
}
