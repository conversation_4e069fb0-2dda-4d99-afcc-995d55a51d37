using System;
using TMPro;
using UnityEngine;

public class ItemCountDisplay : MonoBehaviour
{
    [SerializeField] private RunItemType m_runItemType;
    [SerializeField] private TextMeshProUGUI m_displayText;
    [SerializeField] private string m_displayFormat = "Item: {0}";
    
    
    private ServiceReference<InventoryService> m_inventoryService = new();
    private void Start()
    {
        Debug.LogError($"Start and OnRunItemsChanged.AddListener");
        m_inventoryService.Value.OnRunItemsChanged.AddListener(UpdateItemCount);
    }

    private void UpdateItemCount(RunItemType type, int amount)
    {
        Debug.LogError($"UpdateItemCount with {type} and {amount}");
        if(type == m_runItemType)
        {
            m_displayText.text = String.Format(m_displayFormat, amount);
        }
    }
}
