using System;
using System.Collections.Generic;
using System.Threading;
using Cysharp.Threading.Tasks;
using RibCageGames.Base;
using RibCageGames.Combat;
using RibCageGames.Editor.EditorUtillities.SerializeInterface;
using RibCageGames.Input;
using RibCageGames.Music;
using UnityEngine;

public class ComboSheetMusicDisplay : MonoBehaviour
{
    [SerializeField] private List<ComboSheetNoteVisual> m_noteVisuals;
    
    [SerializeField] private RectTransform m_noteHolder;
    
    [SerializeField] private GameObject m_exampleTimeText;
    [SerializeField] private GameObject m_yourTurnText;
    [SerializeField] private int m_anticipationBeats;
    [SerializeField] private InterfaceReference<ISoundSource> m_readyBeatSound;
    [SerializeField] private float m_completionWaitTime;
    [SerializeField] private float m_unlockTiming;
    [SerializeField] private int m_attemptsBetweenExamples;

    [SerializeField] private InputFeedbackTextController m_feedbackTexts;
    [SerializeField] private float m_perfectTolerence = 0.2f;
    
    [SerializeField] private ComboTutorialMoveList m_testList;
    
    private int m_overallComboBeats;
    private int m_currentWaitingInput;
    private bool m_complete;
    private bool m_listeningToInputs;
    private BeatSystem m_beatSystem;
    private MonoProcess m_comboProcess;
    private CancellationTokenSource m_comboCancellationSource;
    private CancellationTokenSource m_instanceCancellationSource;
    private CancellationTokenSource m_waitCancelSource;
    private List<BeatOverrideActionData> m_currentList;
    private List<BeatOverrideActionData> m_currentAlternativeList;
    private PlayerService m_playerService;
    private Action m_onComplete;

    private void Start()
    {
        m_beatSystem = ServiceLocator.Get<BeatSystem>();
        gameObject.SetActive(false);
        m_exampleTimeText.SetActive(false);
        m_yourTurnText.SetActive(false);
    }

    public async void SetTargetCombo(ComboTutorialMoveList list, Action OnComplete)
    {
        m_onComplete = OnComplete;
        m_comboCancellationSource?.Cancel();
        m_comboCancellationSource = new CancellationTokenSource();
        m_currentList = list.Moves;
        m_currentAlternativeList = list.AlternativeMoves;
        m_currentWaitingInput = 0;
        m_attempts = 0;
        m_playerService = ServiceLocator.Get<PlayerService>(); 
        
        m_complete = false;
        m_playerService.PlayerController.OnMoveStarted.RemoveListener(OnPlayerInput);
        m_playerService.PlayerController.OnMoveStarted.AddListener(OnPlayerInput);
        gameObject.SetActive(true);

        await UniTask.DelayFrame(1);
        
        for (int i = 0; i < m_noteVisuals.Count; i++)
        {
            m_noteVisuals[i].SetNoteGraphic(i < m_currentList.Count && m_currentList[i] != null, i < m_currentList.Count,
                i < list.HoldVisualsActive.Count && list.HoldVisualsActive[i]);
            m_noteVisuals[i].ResetParameter.ApplyParameter();
        }
        
        m_noteHolder.anchoredPosition = Vector2.zero;

        await UniTask.WaitForSeconds(2f);
        m_listeningToInputs = false;
        m_playerService.PlayerController.OnMoveStarted.AddExclusiveListener(OnPlayerInput);
        
        m_playerService.PlayerController.DeregisterNonMovementControls();

        m_attempts = 99;
        m_beatSystem.VisualOnHalfBeat.AddSingleUseListener(StartCombo);
    }

    [SerializeField] private bool m_activate;
    [SerializeField] private RectTransform m_startIndicator;
    [SerializeField] private RectTransform m_endIndicator;
    
#if UNITY_EDITOR
    private void OnValidate()
    {
        if (m_activate)
        {
            m_activate = false;

            var beatSystem = ServiceLocator.EditorGet<BeatSystem>();
            
            Vector2 noteDelta = (m_noteVisuals[0].RectTransform.anchoredPosition - m_noteVisuals[1].RectTransform.anchoredPosition);

            Vector2 inputwindow = noteDelta * ((beatSystem.InputWindow / 2f) /  beatSystem.HalfBeatLength);
            
            Vector2 gracewindow = noteDelta * (beatSystem.InputWindowGrace /  beatSystem.HalfBeatLength);
            
            m_startIndicator.anchoredPosition = - inputwindow - gracewindow + m_anticipationBeats * (noteDelta);
            m_endIndicator.anchoredPosition = inputwindow + m_anticipationBeats * (noteDelta);
        }
        
        if (!Application.isPlaying || ServiceLocator.EditorGet<PlayerService>().PlayerController == null)
        {
            return;
        }
        if (m_testList != null)
        {
            ServiceLocator.Get<PlayerService>().PlayerController.OnMoveStarted.AddExclusiveListener(PlayExample);
        }
        else
        {
            ServiceLocator.Get<PlayerService>().PlayerController.OnMoveStarted.RemoveListener(PlayExample);
        }
    }
#endif

    private void PlayExample(ActionData move, PlayerInputActions input)
    {
        if (m_testList != null && input == PlayerInputActions.Jump)
        {
            for (int i = 0; i < m_noteVisuals.Count; i++)
            {
                m_noteVisuals[i].SetNoteGraphic(i < m_testList.Moves.Count && m_testList.Moves[i] != null, i < m_testList.Moves.Count,
                    i < m_testList.HoldVisualsActive.Count && m_testList.HoldVisualsActive[i]);
                m_noteVisuals[i].ResetParameter.ApplyParameter();
            }
            m_beatSystem.VisualOnHalfBeat.AddSingleUseListener(() => _ = ShowExample(m_testList.Moves));
        }
    }

    private async void OnPlayerInput(ActionData move, PlayerInputActions input)
    {
        if(!m_listeningToInputs) { return; }
        if((move == m_currentList[m_currentWaitingInput] || move == m_currentAlternativeList[m_currentWaitingInput]) && m_playerService.PlayerController.CurrentMoveJust)
        {
            //Is good
            if(m_playerService.PlayerController.GracePeriodInEffect)
            {
                m_feedbackTexts.ActivateGood();
            }
            else
            {
                m_feedbackTexts.ActivatePerfect();
            }
                
            m_noteVisuals[m_currentWaitingInput].OnBeatHitParameter.ApplyParameter();
            m_currentWaitingInput++;
            if (m_currentWaitingInput == m_currentList.Count)
            {
                m_listeningToInputs = false;
                m_playerService.PlayerController.OnMoveStarted.RemoveListener(OnPlayerInput);
                
                m_comboCancellationSource?.Cancel();
                
                await UniTask.WaitForSeconds(m_completionWaitTime);
                
                gameObject.SetActive(false);
                m_onComplete?.Invoke();
            }
            else
            {
                int notesToWait = AdvanceEmptyNotes() + 1;
                StartWaitCancel(notesToWait, m_instanceCancellationSource.Token);
            }
        }
        else //Mistake
        {
            m_noteVisuals[m_currentWaitingInput].OffBeatHitParameter.ApplyParameter();
            ComboFailed();
        }
    }

    private int AdvanceEmptyNotes()
    {
        int beats = 1;
        while (m_currentWaitingInput < m_currentList.Count - 1 && m_currentList[m_currentWaitingInput] == null)
        {
            m_currentWaitingInput++;
            beats++;
        }

        return beats;
    }

    private void StartWaitCancel(int beats, CancellationToken token)
    {
        m_waitCancelSource?.Cancel();
        m_waitCancelSource = CancellationTokenSource.CreateLinkedTokenSource(token);
        if (m_waitCancelSource.IsCancellationRequested) { return; }
        MonoProcess.New(cancellationToken: m_waitCancelSource.Token)
            .WaitForSeconds(m_beatSystem.HalfBeatLength * beats)
            .Do(ComboFailed);
    }

    private async void CancelComboInstance()
    {
        m_instanceCancellationSource?.Cancel();

        for (int i = m_currentWaitingInput; i < m_noteVisuals.Count; i++)
        {
            m_noteVisuals[i].OffBeatHitParameter.ApplyParameter();
        }
        m_currentWaitingInput = 0;
        await UniTask.WaitForSeconds(m_completionWaitTime);
        foreach (ComboSheetNoteVisual visual in m_noteVisuals)
        {
            visual.ResetParameter.ApplyParameter();
        }
    }

    private void ComboFailed()
    {
        m_feedbackTexts.ActivateMiss();
        CancelComboInstance();
        m_listeningToInputs = false;
        m_playerService.PlayerController.DeregisterNonMovementControls();
        m_beatSystem.VisualOnHalfBeat.AddSingleUseListener(StartCombo);
    }

    private int m_attempts;
    private async void StartCombo()
    {
        m_instanceCancellationSource?.Cancel();
        m_instanceCancellationSource = CancellationTokenSource.CreateLinkedTokenSource(m_comboCancellationSource.Token);

        m_attempts++;
        if (m_attempts > m_attemptsBetweenExamples - 1)
        {
            m_attempts = 0;
            m_exampleTimeText.SetActive(true);
            m_yourTurnText.SetActive(false);
            await ShowExample(m_currentList);
        }

        m_exampleTimeText.SetActive(false);
        m_yourTurnText.SetActive(true);
        
        m_beatSystem.VisualOnHalfBeat.AddSingleUseListener(() => StartComboProcess(m_instanceCancellationSource.Token));
    }

    private async UniTask ShowExample(List<BeatOverrideActionData> list)
    {
        bool stoppedForGrace = false;
        bool stoppedForBeat = false;
        bool stoppedForNonBeat = false;
        
        Vector2 startPosition = - m_anticipationBeats * (m_noteVisuals[0].RectTransform.anchoredPosition -
                                     m_noteVisuals[1].RectTransform.anchoredPosition);
        
        Vector2 endPosition = (m_noteVisuals[0].RectTransform.anchoredPosition -
            m_noteVisuals[1].RectTransform.anchoredPosition) * (list.Count - 1);

        float elapsedTime = 0f;
        float duration = m_beatSystem.HalfBeatLength * (list.Count - 1 + m_anticipationBeats);

        int lastBeatRegistered = 0;
        
        while (elapsedTime < duration)
        {
            elapsedTime += Time.deltaTime;
            float normalizedDelta = elapsedTime / duration;
            
            m_noteHolder.anchoredPosition = Vector3.Lerp(startPosition, endPosition, normalizedDelta);

            float correctedTime = elapsedTime - m_beatSystem.HalfBeatLength * (m_anticipationBeats);
            
            if (correctedTime > 0f)
            {
                if (correctedTime > m_beatSystem.HalfBeatLength * (lastBeatRegistered))
                {
                    lastBeatRegistered++;
                    if (list[lastBeatRegistered - 1] != null)
                    {
                        foreach (ActionData.ActionSoundEffect soundEffect in list[lastBeatRegistered - 1].SoundEffects)
                        {
                            m_noteVisuals[lastBeatRegistered - 1].OnBeatHitParameter.ApplyParameter();
                            m_noteVisuals[lastBeatRegistered - 1].ResetParameter.ResetParameterControl(null);
                            if (lastBeatRegistered > 1 && !m_noteVisuals[lastBeatRegistered - 2].ShowingNoteVisual)
                            {
                                m_noteVisuals[lastBeatRegistered - 2].OnBeatHitParameter.ApplyParameter();
                                m_noteVisuals[lastBeatRegistered - 2].ResetParameter.ResetParameterControl(null);
                            }
                            if (soundEffect.SoundStartTime < 0.1f)
                            {
                                soundEffect.SoundSource.Value.Play();
                            }
                        }
                    }
                }
            }
            
            await UniTask.DelayFrame(1);
        }

        await UniTask.WaitForSeconds(m_completionWaitTime);
        
        foreach (ComboSheetNoteVisual visual in m_noteVisuals)
        {
            visual.ResetParameter.ApplyParameter();
        }
    }
    
    

    private void StartComboProcess(CancellationToken token)
    {
        m_currentWaitingInput = 0;
        int beatsElapsed = 0;
        int readySoundsPlayed = 0;
        
        m_beatSystem.VisualOnBeat.AddCancellableListener(IncrementBeat, token);
        m_beatSystem.OnBeat.AddCancellableListener(PlayBeatSound, token);
        
        StartWaitCancel(m_anticipationBeats + 1, token);

        Vector2 noteDelta = (m_noteVisuals[0].RectTransform.anchoredPosition - m_noteVisuals[1].RectTransform.anchoredPosition);
        Vector2 startPosition = - m_anticipationBeats * (noteDelta);
        Vector2 endPosition = noteDelta * (m_currentList.Count - 1);

        m_noteHolder.anchoredPosition = startPosition;

        float elapsedTime = 0f;
        
        float duration = m_beatSystem.HalfBeatLength * (m_currentList.Count - 1 + m_anticipationBeats);
        
        m_comboProcess = MonoProcess.NewManual(cancellationToken: token).WaitWhile(() =>
        {
            elapsedTime += Time.deltaTime;
            float normalizedDelta = elapsedTime / duration;

            m_noteHolder.anchoredPosition = Vector3.Lerp(startPosition, endPosition, normalizedDelta);

            if (elapsedTime > m_beatSystem.HalfBeatLength * m_anticipationBeats - m_unlockTiming)
            {
                if (!m_listeningToInputs)
                {
                    m_listeningToInputs = true;
                    m_playerService.PlayerController.RegisterNonMovementControls();
                }
            }

            return elapsedTime < duration;
        }).WaitForSeconds(2f).Do(CancelComboInstance);
        
        m_comboProcess.Run();

        void PlayBeatSound(bool mainBeat)
        {
            readySoundsPlayed++;
            if (readySoundsPlayed < m_anticipationBeats)
            {
                m_readyBeatSound.Value.Play();
            }
        }

        void IncrementBeat(bool mainBeat)
        {
            beatsElapsed++;
        }
    }
}
