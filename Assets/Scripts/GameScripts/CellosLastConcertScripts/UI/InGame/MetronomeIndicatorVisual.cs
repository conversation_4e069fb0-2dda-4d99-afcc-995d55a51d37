using RibCageGames.Animation;
using RibCageGames.MonoUtils;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.UI;

public class MetronomeIndicatorVisual : MonoBehaviour, IPoolable
{
    [SerializeField] private RectTransform m_rectTransform;
    [SerializeField] private CanvasGroup m_canvasGroup;
    [SerializeField] private RectTransform m_warningImageRect;
    [SerializeField] private Image m_image;
    [SerializeField] private Animator m_animator;

    [SerializeField] [AnimatorParameterControl("m_animator")]
    private AnimatorParameterControl m_activeOnAnimatorParameter;

    [SerializeField] [AnimatorParameterControl("m_animator")]
    private AnimatorParameterControl m_activeOffAnimatorParameter;

    [SerializeField] [AnimatorParameterControl("m_animator")]
    private AnimatorParameterControl m_inRangeOnAnimatorParameter;

    [SerializeField] [AnimatorParameterControl("m_animator")]
    private AnimatorParameterControl m_inRangeOffAnimatorParameter;

    [SerializeField] [AnimatorParameterControl("m_animator")]
    private AnimatorParameterControl m_transitionAnimatorParameter;

    [SerializeField] [AnimatorParameterControl("m_animator")]
    private AnimatorParameterControl m_attackIndicatorOnParameter;

    [SerializeField] [AnimatorParameterControl("m_animator")]
    private AnimatorParameterControl m_attackIndicatorOffParameter;

    [SerializeField] [AnimatorParameterControl("m_animator")]
    private AnimatorParameterControl m_shownOnParameter;

    [SerializeField] [AnimatorParameterControl("m_animator")]
    private AnimatorParameterControl m_shownOffParameter;


    [SerializeField] internal Vector2 m_origin;
    [SerializeField] internal Vector2 m_target;
    [SerializeField] internal float m_originAngle;
    [SerializeField] internal float m_targetAngle;
    [SerializeField] internal Vector2 m_originScale;
    [SerializeField] internal Vector2 m_targetScale;
    [SerializeField] internal AnimationCurve m_scaleCurve;

    public Vector2 Origin => m_origin;
    public Vector2 Target => m_target;
    public float OriginAngle => m_originAngle;
    public float TargetAngle => m_targetAngle;
    
    public Vector2 OriginScale => m_originScale;
    public Vector2 TargetScale => m_targetScale;
    public AnimationCurve ScaleCurve => m_scaleCurve;
    
    public RectTransform RectTransform => m_rectTransform;
    //public RectTransform WarningImageRect => m_warningImageRect;
    public Image Image => m_image;
    public Animator Animator => m_animator;
    public CanvasGroup CanvasGroup => m_canvasGroup;
    public AnimatorParameterControl ActiveOnAnimatorParameter => m_activeOnAnimatorParameter;
    public AnimatorParameterControl ActiveOffAnimatorParameter => m_activeOffAnimatorParameter;
    public AnimatorParameterControl InRangeOnAnimatorParameter => m_inRangeOnAnimatorParameter;
    public AnimatorParameterControl InRangeOffAnimatorParameter => m_inRangeOffAnimatorParameter;
    public AnimatorParameterControl TransitionAnimatorParameter => m_transitionAnimatorParameter;
    public AnimatorParameterControl AttackIndicatorOnParameter => m_attackIndicatorOnParameter;
    public AnimatorParameterControl AttackIndicatorOffParameter => m_attackIndicatorOffParameter;
    public AnimatorParameterControl ShownOnParameter => m_shownOnParameter;
    public AnimatorParameterControl ShownOffParameter => m_shownOffParameter;

    public UnityAction ResetAttackIndicatorUnregister;

    public void ActivatePoolable()
    {
        gameObject.SetActive(true);
    }

    public void DeactivatePoolable()
    {
        gameObject.SetActive(false);
    }

    public void ResetPoolable(bool resetMovement = true)
    {
        gameObject.SetActive(false);
    }

    public void Initialize()
    {
        gameObject.SetActive(false);
        m_activeOnAnimatorParameter.Initialize(m_animator);
        m_activeOffAnimatorParameter.Initialize(m_animator);
        m_inRangeOnAnimatorParameter.Initialize(m_animator);
        m_inRangeOffAnimatorParameter.Initialize(m_animator);
        m_transitionAnimatorParameter.Initialize(m_animator);
        m_attackIndicatorOnParameter.Initialize(m_animator);
        m_attackIndicatorOffParameter.Initialize(m_animator);
        m_shownOnParameter.Initialize(m_animator);
        m_shownOffParameter.Initialize(m_animator);
    }

    public UnityEvent OnDisable { get; private set; } = new UnityEvent();
}
