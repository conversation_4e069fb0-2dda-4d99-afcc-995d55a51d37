using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Cysharp.Threading.Tasks;
using DG.Tweening;
using RibCageGames.Animation;
using RibCageGames.Base;
using RibCageGames.Editor;
using RibCageGames.MonoUtils;
using RibCageGames.Music;
using UnityEngine;

public class BeatMetronome : MonoBehaviour
{
    [SerializeField] private AnimationCurve m_curve;
    [SerializeField] private AnimationCurve m_colorCurve;
    [SerializeField] private int m_indicatorsOnScreen;
    [SerializeField] private float m_indicatorActionDuration;
    [SerializeField] private float m_indicatorFadeDuration;
    [SerializeField] private float m_prefadeActiveDuration;
    [SerializeField] private int m_indicatorCopiesAmount;
    [SerializeField] private Sprite m_fullBeatSprite;
    [SerializeField] private Sprite m_halfBeatSprite;

    [SerializeField] private List<PrefabPool<MetronomeIndicatorVisual>> m_indicatorCopies;

    [SerializeField] private InputFeedbackTextController m_feedbackTexts;
    [SerializeField] private float m_perfectTolerence;
    [SerializeField] private GameObject m_demoDangerIndicator;
    [SerializeField] private bool m_stopOnTarget;

    [SerializeField] private bool m_useTargetAnimator;
    
    [ConditionalHide("m_useTargetAnimator", true)]
    [SerializeField] private Animator m_targetAnimator;
    
    [ConditionalHide("m_useTargetAnimator", true)] [SerializeField] [AnimatorParameterControl("m_targetAnimator")]
    private AnimatorParameterControl m_OnBeatHitParameter;
    
    [ConditionalHide("m_useTargetAnimator", true)] [SerializeField] [AnimatorParameterControl("m_targetAnimator")]
    private AnimatorParameterControl m_OffBeatHitParameter;

    private Queue<BeatIndicator> m_availableIndicators = new();
    private List<BeatIndicator> m_queuedIndicators = new();
        
    private BeatSystem m_beatSystem;
    private bool m_targetOnBeat;

    private void Awake()
    {
        foreach (PrefabPool<MetronomeIndicatorVisual> pool in m_indicatorCopies)
        {
            pool.Init();
        }
    }

    private void Start()
    {
        if (m_useTargetAnimator)
        {
            m_OnBeatHitParameter.Initialize(m_targetAnimator);
            m_OffBeatHitParameter.Initialize(m_targetAnimator);
        }

        ServiceLocator.Get<MonoService>().OnUpdate.AddListener(ControlledUpdate);
        ServiceLocator.Get<PlayerService>().OnPlayerInput.AddListener(OnPlayerInput);
        m_beatSystem = ServiceLocator.Get<BeatSystem>();

        if (m_demoDangerIndicator != null)
        {
            m_demoDangerIndicator.gameObject.SetActive(false);
        }
        
        foreach (PrefabPool<MetronomeIndicatorVisual> pool in m_indicatorCopies)
        {
            pool.Start();
        }
        
        m_beatSystem.VisualOnBeat.AddListener(OnBeat);
        for (int i = 0; i < m_indicatorCopiesAmount; i++)
        {
            List<MetronomeIndicatorVisual> visuals = new List<MetronomeIndicatorVisual>();
            foreach (PrefabPool<MetronomeIndicatorVisual> pool in m_indicatorCopies)
            {
                //Debug.LogError($"Making an instance of {pool.Prefab} for {gameObject.name}");
                MetronomeIndicatorVisual visual = pool.GetInstance();
                visual.transform.SetParent(pool.Prefab.transform.parent);
                visual.transform.localScale = pool.Prefab.transform.localScale;
                visual.transform.localPosition = pool.Prefab.transform.localPosition;
                visual.transform.localRotation = pool.Prefab.transform.localRotation;
                visuals.Add(visual);
            }

            BeatIndicator newIndicator = new BeatIndicator(visuals, m_curve, m_colorCurve, m_stopOnTarget);
            m_availableIndicators.Enqueue(newIndicator);
        }
    }

    private void OnPlayerInput(bool onBeat)
    {
        if (m_queuedIndicators.Count > 0)
        {
            m_queuedIndicators[0].SetInputState(onBeat);
        }

        if (onBeat)
        {
            if (m_useTargetAnimator)
            {
                m_OnBeatHitParameter.ApplyParameter();
            }

            if (m_beatSystem.BeatPercentage < m_perfectTolerence ||
                m_beatSystem.BeatPercentage > 1f - m_perfectTolerence)
            {
                m_feedbackTexts?.ActivatePerfect();   
            }
            else
            {
                m_feedbackTexts?.ActivateGood();
            }
        }
        else
        {
            if (m_useTargetAnimator)
            {
                m_OnBeatHitParameter.ApplyParameter();
            }

            m_feedbackTexts?.ActivateMiss();
        }
    }

    public void ShowFakeDangerIndicator(bool show)
    {
        if (m_demoDangerIndicator != null)
        {
            m_demoDangerIndicator.gameObject.SetActive(show);
        }
    }

    public Action AddDangerIndicator(int attackBeatDelay)
    {
        if (!gameObject.activeInHierarchy)
        {
            return null;
        }
        
        CancellationTokenSource tokenSource = new CancellationTokenSource();
        
        if (attackBeatDelay < m_queuedIndicators.Count)
        {
            BeatIndicator indicator = m_queuedIndicators[attackBeatDelay];
            indicator.ShowDamageIndicator(true);

            return () =>
            {
                indicator.ShowDamageIndicator(false);
            };
        }
        else
        {
            int delay = attackBeatDelay;
            m_beatSystem.VisualOnBeat.AddListenerUntil((_) =>
            {
                delay--;
                if (delay < m_queuedIndicators.Count)
                {
                    BeatIndicator indicator = m_queuedIndicators[delay];
                    indicator.ShowDamageIndicator(true);
                }
            }, _ => delay < m_queuedIndicators.Count, tokenSource.Token);
            
            return () =>
            {
                tokenSource?.Cancel();
                if (delay < m_queuedIndicators.Count)
                {
                    m_queuedIndicators[delay].ShowDamageIndicator(false);
                }
            }; 
        }
    }

    private void ControlledUpdate(float deltaTime)
    {
        //bool inBeatRange = m_beatSystem.IsOnBeat || m_beatSystem.IsInGracePeriod;

        bool inBeatRange = m_beatSystem.VisualIsOnBeat || m_beatSystem.VisualIsInGracePeriod;
        
        //if (m_useTargetAnimator && m_targetOnBeat != inBeatRange)
        //{
            //m_targetOnBeat = inBeatRange;
            //(m_targetOnBeat ? m_activeTargetAnimatorParameter : m_inactiveTargetAnimatorParameter).ApplyParameter();
        //}
        
        foreach (BeatIndicator indicator in m_queuedIndicators)
        {
            indicator.UpdateIndicator(deltaTime / (m_beatSystem.HalfBeatLength * m_indicatorsOnScreen));
        }

        if (m_queuedIndicators.Count > 0) //Needed for initialization before first queue
        {
            m_queuedIndicators[0].SetInBeatRange(inBeatRange);
        }
    }

    private void OnBeat(bool mainBeat)
    {
        QueueBeat(mainBeat);
    }

    private async void QueueBeat(bool mainBeat)
    {
        if (m_availableIndicators.Count > 0)
        {
            BeatIndicator indicator = m_availableIndicators.Dequeue();
            indicator.StartIndicator(mainBeat);
            int beats = 0;
            m_queuedIndicators.Add(indicator);

            for (int i = 0; i < m_indicatorsOnScreen + 1; i++)
            {
                await UniTask.WaitUntil(() => m_beatSystem.VisualIsOnBeat);
                await UniTask.WaitUntil(() => !m_beatSystem.VisualIsOnBeat);
            }

            await UniTask.WaitUntil(() => !m_beatSystem.IsOnBeat);

            m_queuedIndicators.Remove(indicator);

            if (m_prefadeActiveDuration > 0.1f)
            {
                if (indicator.BeatHit)
                {
                    await ContinueIndicatorMovement(indicator);
                    indicator.EndIndicator();
                }
                else
                {
                    indicator.EndIndicator();
                }
            }
            else
            {
                indicator.FadeIndicator(m_indicatorFadeDuration);

                await UniTask.WaitForSeconds(m_indicatorFadeDuration);

                indicator.EndIndicator();    
            }
            
            m_availableIndicators.Enqueue(indicator);
        }
        else
        {
            //Debug.Log($"No beat to queue");
        }
    }

    private async Task ContinueIndicatorMovement(BeatIndicator indicator)
    {
        float time = 0f;
        while (time < m_prefadeActiveDuration)
        {
            await UniTask.NextFrame();
            time += Time.deltaTime;
            indicator.UpdateIndicator(Time.deltaTime / (m_beatSystem.HalfBeatLength * m_indicatorsOnScreen));
        }
    }

    //Indicates one specific beat throughout it's lifetime
    private class BeatIndicator// : IDisposable
    {
        private AnimationCurve m_curve;
        private AnimationCurve m_colorCurve;
        private List<MetronomeIndicatorVisual> m_visuals;
        private float m_currentValue;
        private int m_attacksQueued = 0;
        private bool m_active = false;
        private bool m_stopOnTarget = false;

        public bool BeatHit { get; private set; }

        public float CurrentValue => m_currentValue;

        internal BeatIndicator(List<MetronomeIndicatorVisual> visuals, AnimationCurve curve, AnimationCurve colorCurve, bool stopOnTarget)
        {
            m_visuals = visuals;
            m_curve = curve;
            m_colorCurve = colorCurve;
            m_stopOnTarget = stopOnTarget;

            foreach (MetronomeIndicatorVisual visual in m_visuals)
            {
                visual.gameObject.SetActive(true);
            }
        }

        public void StartIndicator(bool mainBeat)
        {
            foreach (MetronomeIndicatorVisual visual in m_visuals)
            {
                visual.InRangeOffAnimatorParameter.ApplyParameter();
                visual.AttackIndicatorOffParameter.ApplyParameter();
                visual.ShownOnParameter.ApplyParameter();
                
                //TODO: Scale for main and offbeat
            }
            m_currentValue = 0f;
            m_attacksQueued = 0;
            m_active = true;
            BeatHit = false;
        }

        public void FadeIndicator(float duration)
        {
            foreach (MetronomeIndicatorVisual visual in m_visuals)
            {
                visual.CanvasGroup.DOFade(0f, duration);
            }
        }

        public void EndIndicator()
        {
            foreach (MetronomeIndicatorVisual visual in m_visuals)
            {
                visual.ActiveOffAnimatorParameter.ApplyParameter();
                visual.ShownOffParameter.ApplyParameter();
                visual.InRangeOffAnimatorParameter.ApplyParameter();
                visual.AttackIndicatorOffParameter.ApplyParameter();
                visual.CanvasGroup.alpha = 0f;
            }

            m_active = false;
            m_attacksQueued = 0;
            m_currentValue = 0f;
            BeatHit = false;
        }

        public void SetInputState(bool inBeatRange)
        {
            SetInBeatRange(inBeatRange);
            foreach (MetronomeIndicatorVisual visual in m_visuals)
            {
                visual.ActiveOnAnimatorParameter.ApplyParameter();
                visual.TransitionAnimatorParameter.ApplyParameter();
            }

            m_active = false;
            BeatHit = true;
        }

        public void UpdateIndicator(float delta)
        {
            if (!m_active && m_stopOnTarget)
            {
                return;
            }
            m_currentValue += delta;
            if (m_stopOnTarget)
            {
                m_currentValue = Mathf.Clamp01(m_currentValue);
            }
            foreach (MetronomeIndicatorVisual visual in m_visuals)
            {
                visual.RectTransform.anchoredPosition =
                    Vector2.LerpUnclamped(visual.Origin, visual.Target, m_currentValue);//m_curve.Evaluate(currentValue));

                visual.RectTransform.localRotation =
                    Quaternion.Slerp(
                        Quaternion.Euler(0f, 0f, visual.OriginAngle),
                        Quaternion.Euler(0f, 0f, visual.TargetAngle),
                        m_curve.Evaluate(m_currentValue));
                
                visual.Image.rectTransform.localScale =
                    Vector2.Lerp(visual.OriginScale, visual.TargetScale,
                        visual.ScaleCurve.Evaluate(m_currentValue));
                
                visual.CanvasGroup.alpha = m_colorCurve.Evaluate(m_currentValue);
            }
        }

        public void SetInBeatRange(bool inBeatRange)
        {
            if (m_active)
            {
                foreach (MetronomeIndicatorVisual visual in m_visuals)
                {
                    (inBeatRange ? visual.InRangeOnAnimatorParameter : visual.InRangeOffAnimatorParameter)
                        .ApplyParameter();
                }
            }
        }
        
        public void ShowDamageIndicator(bool show)
        {
            if (show)
            {
                m_attacksQueued++;
            }
            else
            {
                m_attacksQueued--;
            }
            foreach (MetronomeIndicatorVisual visual in m_visuals)
            {
                (m_attacksQueued > 0 ? visual.AttackIndicatorOnParameter : visual.AttackIndicatorOffParameter).ApplyParameter();
            }
        }
    }
}
