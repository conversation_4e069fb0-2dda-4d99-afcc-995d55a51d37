using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using RibCageGames.Base;
using UnityEngine;
using UnityEngine.UI;

public class InventoryDisplay : MonoBehaviour
{
    [SerializeField] private List<Image> m_powerUpImages;
    [SerializeField] private List<Image> m_powerUpFillImages;
    [SerializeField] private List<Image> m_powerUpBackgroundImages;
    [SerializeField] private List<PowerUpIcon> m_iconSettings;
    
    private PlayerService m_playerService;
    private MonoService m_monoService;
    private Dictionary<PowerUpType, PowerUpIcon> m_iconSettingsDict = new Dictionary<PowerUpType, PowerUpIcon>();

    private void Start()
    {
        m_playerService = ServiceLocator.Get<PlayerService>();
        m_monoService = ServiceLocator.Get<MonoService>();
        
        m_playerService.OnInventoryChanged.AddListener(UpdateInventoryDisplay);
        m_monoService.OnUpdate.AddListener(UpdateIconFills);

        foreach (PowerUpIcon powerUpIcon in m_iconSettings)
        {
            m_iconSettingsDict.Add(powerUpIcon.PowerUpType ,powerUpIcon);
        }
        
        for (int i = 0; i < m_powerUpFillImages.Count; i++)
        {
            m_powerUpImages[i].gameObject.SetActive(false);
            m_powerUpFillImages[i].gameObject.SetActive(false);
        }
        
    }

    private void UpdateInventoryDisplay(Dictionary<PowerUpType, (int, BonusEffect, Powerup)> powerupData)
    {
        m_displayedBonuses.Clear();

        for (int i = 0; i < m_powerUpFillImages.Count; i++)
        {
            m_powerUpImages[i].gameObject.SetActive(false);
            m_powerUpFillImages[i].gameObject.SetActive(false);
        }

        int slotsTaken = 0;

        for (int i = 0; i < powerupData.Count; i++)
        {
            (int, BonusEffect, Powerup) value = powerupData.Values.ElementAt(i);
            PowerUpType type = powerupData.Keys.ElementAt(i);

            if (value.Item3.IsNamedPowerup)
            {
                Debug.LogError($"Display showing: {value.Item3.DisplayName} of type {type} at level {value.Item1}");
                for (int j = 0; j < value.Item1; j++)
                {
                    m_powerUpImages[slotsTaken + j].sprite = m_iconSettingsDict[type].IconSprite;
                    m_powerUpImages[slotsTaken + j].color = m_iconSettingsDict[type].Color;
                    m_powerUpFillImages[slotsTaken + j].sprite = m_iconSettingsDict[type].FillIconSprite;
                    m_powerUpFillImages[slotsTaken + j].color = m_iconSettingsDict[type].FillColor;

                    m_powerUpBackgroundImages[slotsTaken + j].color = m_iconSettingsDict[type].BackgroundColor;

                    m_powerUpImages[slotsTaken + j].gameObject.SetActive(true);
                    m_powerUpFillImages[slotsTaken + j].gameObject.SetActive(true);
                    m_powerUpImages[slotsTaken + j].transform.parent.gameObject.SetActive(true);
                    m_displayedBonuses.Add(value.Item2);
                }

                slotsTaken += value.Item1;
            }
        }
    }

    private List<BonusEffect> m_displayedBonuses = new List<BonusEffect>();
    
    private void UpdateIconFills(float delta)
    {
        for (int i = 0; i < m_displayedBonuses.Count; i++)
        {
            m_powerUpFillImages[i].fillAmount = m_displayedBonuses[i].FillAmount;
        }
    }

    [Serializable]
    public class PowerUpIcon
    {
        [SerializeField] private PowerUpType m_powerUpType;
        [SerializeField] private Sprite m_iconSprite;
        [SerializeField] private Color m_color;
        [SerializeField] private Sprite m_fillIconSprite;
        [SerializeField] private Color m_fillColor;
        [SerializeField] private Color m_backgroundColor;
        
        public Sprite IconSprite => m_iconSprite;
        public Color Color => m_color;
        public Sprite FillIconSprite => m_fillIconSprite;
        public Color FillColor => m_fillColor;
        public Color BackgroundColor => m_backgroundColor;
        public PowerUpType PowerUpType => m_powerUpType;
    }
}
