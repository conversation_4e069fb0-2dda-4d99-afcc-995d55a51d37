using RibCageGames.Animation;
using RibCageGames.Base;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class EnemyHealthBar : MonoBehaviour
{
    [SerializeField] private Image m_hpFillImage;
    [SerializeField] private AnimationCurve m_fillCurve;
    [SerializeField] private Image m_hpDamageFillImage;
    [SerializeField] private AnimationCurve m_lingerCurve;
    [SerializeField] private TextMeshProUGUI m_healthText;
    [SerializeField] private string m_healthFormat = "{0} / {1}";
    [SerializeField] private float m_changeDuration;
    [SerializeField] private Animator m_healthBarAnimator;
    [SerializeField][AnimatorParameterControl("m_healthBarAnimator")] private AnimatorParameterControl m_showHalthBar;

    private CameraService m_cameraService;
    private Transform mainCameraTransform => m_cameraService.ConcreteCamera.transform;
    private MonoProcess m_fillProcess;
    private bool m_initialized = false;
    
    private void Awake()
    {
        m_cameraService = ServiceLocator.Get<CameraService>();
        m_showHalthBar.Initialize(m_healthBarAnimator);
    }
    
    private void OnEnable()
    {
        //TODO: fix this needing to be editor get when object is on in the loaded scene
        ServiceLocator.EditorGet<MonoService>().OnLateUpdate.AddListener(ControlledLateUpdate);
    }
    
    private void OnDisable()
    {
        ServiceLocator.Get<MonoService>().OnLateUpdate.RemoveListener(ControlledLateUpdate);
    }

    private void ControlledLateUpdate(float arg0)
    {
        transform.LookAt(mainCameraTransform.position, Vector3.up);
    }

    public void ResetHealthBar()
    {
        if (m_fillProcess != null)
        {
            m_fillProcess?.Stop();
        }
        m_hpFillImage.fillAmount = 1f;
        m_hpDamageFillImage.fillAmount = 1f;
    }

    public void OnHealthChanged(float prevHp, float newHp, float maxHP)
    {
        m_showHalthBar.ApplyParameter();
        if(m_healthText){
            m_healthText.text = string.Format(m_healthFormat, (int)(newHp),
            maxHP);
        }

        if (m_fillProcess != null)
        {
            m_fillProcess?.Stop();
        }

        m_fillProcess = MonoProcess.New();
        m_fillProcess.WaitFor(
                () =>
                {
                    m_hpFillImage.fillAmount =
                        Mathf.Lerp(prevHp / maxHP, newHp / maxHP, m_fillCurve.Evaluate(m_fillProcess.ElapsedTime / m_changeDuration));
                    m_hpDamageFillImage.fillAmount =
                        Mathf.Lerp(prevHp / maxHP, newHp / maxHP, m_lingerCurve.Evaluate(m_fillProcess.ElapsedTime / m_changeDuration));
                    return false;
                }, m_changeDuration)
            .Do(() =>
            {
                m_hpFillImage.fillAmount = newHp / maxHP;
                m_hpDamageFillImage.fillAmount = newHp / maxHP;
            });
    }
}
