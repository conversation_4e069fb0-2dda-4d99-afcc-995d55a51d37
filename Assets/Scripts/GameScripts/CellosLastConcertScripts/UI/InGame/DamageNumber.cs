using System.Collections;
using System.Collections.Generic;
using RibCageGames.Base;
using TMPro;
using UnityEngine;

public class DamageNumber : MonoBehaviour
{
    [SerializeField] private TextMeshPro m_text;

    public TextMeshPro Text => m_text;

    private MonoService m_monoService;
    private Camera m_cameraReference;

    
    private void Awake()
    {
        m_monoService = ServiceLocator.Get<MonoService>();
        m_cameraReference = Camera.main;

    }

    private void OnEnable()
    {
        m_monoService.OnUpdate.AddListener(Billboard);
    }

    private void Billboard(float delta)
    {
        if (m_cameraReference == null)
        {
            m_cameraReference = Camera.main;
        }

        transform.LookAt(transform.position + (transform.position - m_cameraReference.transform.position));
    }

    private void OnDisable()
    {
        m_monoService.OnUpdate.RemoveListener(Billboard);
    }
}
