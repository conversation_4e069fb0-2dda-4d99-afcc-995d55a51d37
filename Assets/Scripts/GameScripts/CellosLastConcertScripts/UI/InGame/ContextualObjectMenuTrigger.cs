using RibCageGames.Base;
using RibCageGames.Editor;
using RibCageGames.UI;
using UnityEngine;
using UnityEngine.Events;

public class ContextualObjectMenuTrigger : MonoBehaviour
{
    [SerializeField] private Transform m_menuAnchor;
    [SerializeField][TextArea] private string m_mainText;
    [SerializeField] private string m_titleText;
    [SerializeField] private string m_continuePromptText;
    
    [SerializeField] private bool m_jumpEnabled;
    [SerializeField][ConditionalField("m_jumpEnabled", true)] private string m_jumpPromptText;
    [SerializeField][ConditionalField("m_jumpEnabled", true)] protected UnityEvent m_jumpPressed;
    
    [SerializeField] private bool m_attackEnabled;
    [SerializeField][ConditionalField("m_attackEnabled", true)] private string m_attackPromptText;
    [SerializeField][ConditionalField("m_attackEnabled", true)] protected UnityEvent m_attackPressed;
    
    [SerializeField] private bool m_dodgeEnabled;
    [SerializeField][ConditionalField("m_dodgeEnabled", true)] private string m_dodgePromptText;
    [SerializeField][ConditionalField("m_dodgeEnabled", true)] protected UnityEvent m_dodgePressed;
    
    [SerializeField] private bool m_specialEnabled;
    [SerializeField][ConditionalField("m_specialEnabled", true)] private string m_specialPromptText;
    [SerializeField][ConditionalField("m_specialEnabled", true)] protected UnityEvent m_specialPressed;
    
    public Transform MenuAnchor => m_menuAnchor;
    public string MainText => m_mainText;
    public string TitleText => m_titleText;
    public string ContinuePromptText => m_continuePromptText;
    public virtual bool JumpEnabled => m_jumpEnabled;
    public virtual string JumpPromptText => m_jumpPromptText;
    public virtual UnityEvent JumpPressed => m_jumpPressed;
    public virtual bool AttackEnabled => m_attackEnabled;
    public virtual string AttackPromptText => m_attackPromptText;
    public virtual UnityEvent AttackPressed => m_attackPressed;
    public virtual bool DodgeEnabled => m_dodgeEnabled;
    public virtual string DodgePromptText => m_dodgePromptText;
    public virtual UnityEvent DodgePressed => m_dodgePressed;
    public virtual bool SpecialEnabled => m_specialEnabled;
    public virtual string SpecialPromptText => m_specialPromptText;
    public virtual UnityEvent SpecialPressed => m_specialPressed;

    private readonly ServiceReference<PopupService> m_popupService = new();

    protected ContextualObjectMenu m_menu;

    protected virtual void Awake()
    {
        m_popupService.Value?.TryGetElement(out m_menu); 
    }

    public virtual void InitializeData()
    {
    }
    
    public virtual void Activate()
    {
        InitializeData();
        m_menu.ActivateContext(this);
    }

    public void Deactivate()
    {
        m_menu.DeactivateContext(this);
    }
    
    public void Hide()
    {
        m_menu.HideContext(this);
    }
}
