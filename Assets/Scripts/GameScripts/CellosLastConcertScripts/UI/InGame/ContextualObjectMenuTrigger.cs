using System;
using RibCageGames.Base;
using RibCageGames.Editor;
using RibCageGames.UI;
using UnityEngine;
using UnityEngine.Events;

public class ContextualObjectMenuTrigger : MonoBehaviour
{
    [SerializeField] private Transform m_menuAnchor;
    [SerializeField][TextArea] private string m_mainText;
    [SerializeField] private string m_titleText;
    [SerializeField] private string m_continuePromptText;
    
    [SerializeField] private bool m_jumpEnabled;
    [SerializeField][ConditionalField("m_jumpEnabled", true)] private string m_jumpPromptText;
    [SerializeField][ConditionalField("m_jumpEnabled", true)] protected UnityEvent m_jumpPressed;
    
    [SerializeField] private bool m_attackEnabled;
    [SerializeField][ConditionalField("m_attackEnabled", true)] private string m_attackPromptText;
    [SerializeField][ConditionalField("m_attackEnabled", true)] protected UnityEvent m_attackPressed;
    
    [SerializeField] private bool m_dodgeEnabled;
    [SerializeField][ConditionalField("m_dodgeEnabled", true)] private string m_dodgePromptText;
    [SerializeField][ConditionalField("m_dodgeEnabled", true)] protected UnityEvent m_dodgePressed;
    
    [SerializeField] private bool m_specialEnabled;
    [SerializeField][ConditionalField("m_specialEnabled", true)] private string m_specialPromptText;
    [SerializeField][ConditionalField("m_specialEnabled", true)] protected UnityEvent m_specialPressed;
    
    public Transform MenuAnchor => m_menuAnchor;
    public string MainText => m_mainText;
    public string TitleText => m_titleText;
    public string ContinuePromptText => m_continuePromptText;
    public virtual bool JumpEnabled => m_jumpEnabled;
    public virtual string JumpPromptText => m_jumpPromptText;
    public virtual Action JumpPressed => () =>
    {
        if (JumpInteractable.Invoke())
        {
            m_jumpPressed?.Invoke();
        }
    };
    public virtual Func<bool> JumpInteractable => m_jumpInteractable;
    public virtual bool AttackEnabled => m_attackEnabled;
    public virtual string AttackPromptText => m_attackPromptText;
    public virtual Action AttackPressed => m_attackPressedAction;
    public virtual Func<bool> AttackInteractable => m_attackInteractable;
    public virtual bool DodgeEnabled => m_dodgeEnabled;
    public virtual string DodgePromptText => m_dodgePromptText;
    public virtual Action DodgePressed => m_dodgePressedAction;
    public virtual Func<bool> DodgeInteractable => m_dodgeInteractable;
    public virtual bool SpecialEnabled => m_specialEnabled;
    public virtual string SpecialPromptText => m_specialPromptText;
    public virtual Action SpecialPressed => m_specialPressedAction;
    public virtual Func<bool> SpecialInteractable => m_specialInteractable;
    public virtual int JunkCost => m_junkCost;

    private readonly ServiceReference<PopupService> m_popupService = new();
    private readonly ServiceReference<InventoryService> m_inventoryService = new();
    
    private int m_junkCost = 0;
    
    protected Func<bool> m_specialInteractable = () => true;
    protected Func<bool> m_dodgeInteractable = () => true;
    protected Func<bool> m_attackInteractable = () => true;
    protected Func<bool> m_jumpInteractable = () => true;
    protected Action m_specialPressedAction;
    protected Action m_dodgePressedAction;
    protected Action m_attackPressedAction = () =>  { };
    protected Action m_jumpPressedAction = () =>  { };

    protected ContextualObjectMenu m_menu;

    protected virtual void Awake()
    {
        m_specialPressedAction = () =>
        {
            if (SpecialInteractable.Invoke())
            {
                m_specialPressed?.Invoke();
            }
        };

        m_dodgePressedAction = () =>
        {
            if (DodgeInteractable.Invoke())
            {
                m_dodgePressed?.Invoke();
            }
        };

        m_attackPressedAction = () =>
        {
            if (AttackInteractable.Invoke())
            {
                m_attackPressed?.Invoke();
            }
        };

        m_jumpPressedAction = () =>
        {
            if (JumpInteractable.Invoke())
            {
                m_jumpPressed?.Invoke();
            }
        };


        m_popupService.Value?.TryGetElement(out m_menu);
    }

    public virtual void InitializeData(int junkCost)
    {
        m_junkCost = junkCost;
        if (m_junkCost > 0)
        {
            m_jumpEnabled = false;
            m_attackEnabled = true;
            m_attackInteractable = () => m_inventoryService.Value.HasRunItemAmount(RunItemType.RunCurrency, m_junkCost);
            m_attackPressedAction = () =>
            {
                if (AttackInteractable.Invoke())
                {
                    m_inventoryService.Value.RemoveRunItem(RunItemType.RunCurrency, m_junkCost);
                    m_attackPressed?.Invoke();
                }
            };
            m_specialEnabled = true;
            m_specialInteractable = () => m_inventoryService.Value.HasRunItemAmount(RunItemType.SpecialToken);
            m_specialPressedAction = () =>
            {
                if (m_specialInteractable.Invoke())
                {
                    m_inventoryService.Value.RemoveRunItem(RunItemType.SpecialToken);
                    m_specialPressed?.Invoke();
                }
            };
        }
        else
        {
            m_jumpEnabled = true; 
            m_jumpInteractable = () => true;
            m_jumpPressedAction = () =>
            {
                if (JumpInteractable.Invoke())
                {
                    m_jumpPressed?.Invoke();
                }
            };
            m_attackEnabled = false;
            m_specialEnabled = false;
        }
    }
    
    public virtual void Activate()
    {
        InitializeData(0);
        m_menu.ActivateContext(this);
    }

    public void Deactivate()
    {
        m_menu.DeactivateContext(this);
    }
    
    public void Hide()
    {
        m_menu.HideContext(this);
    }
}
