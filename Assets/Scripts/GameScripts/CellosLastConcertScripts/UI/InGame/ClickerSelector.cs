using System;
using System.Collections.Generic;
using RibCageGames.Input;
using RibCageGames.Music;
using RibCageGames.UI;
using UnityEngine;

public class ClickerSelector : MonoBehaviour
{

    private readonly ServiceReference<InputService> m_inputService = new();
    private readonly ServiceReference<BeatSystem> m_beatSystem = new();
    private readonly ServiceReference<PopupService> m_popupService = new();

    // Track if user has interacted with the clicker selector
    private bool m_hasInteracted = false;
    
    
    private int CurrentMetronomeIndex
    {
        get => m_beatSystem.Value.MetronomeSelection;
        set => m_beatSystem.Value.SetMetronomeSelection(value, true);
    }

    private void Start()
    {
        CurrentMetronomeIndex = 0;
    }

    public void ToggleMetronomeMute()
    {
        m_beatSystem.Value.SetMetronomeActive(!m_beatSystem.Value.MetronomeActive);
    }

    private Action m_settingUnregister;
    public void ActivateClickerSelection()
    {
        ResetInteractionState();
        Action unregisterPrevious = m_inputService.Value.RegisterButtonListener(PlayerInputActions.LightAttack,
            () => {
                CurrentMetronomeIndex++;
                m_hasInteracted = true;
            });
        Action unregisterNext = m_inputService.Value.RegisterButtonListener(PlayerInputActions.Dodge,
            () => {
                ToggleMetronomeMute();
                m_hasInteracted = true;
            });
        
        m_popupService.Value.CurrentPopupSettings.SetClosingPredicate(() => m_hasInteracted);

        m_settingUnregister = () =>
        {
            unregisterPrevious();
            unregisterNext();
            m_popupService.Value.CurrentPopupSettings.ClearClosingPredicate();
        };
    }
    
    public void DeactivateClickerSelection()
    {
        m_settingUnregister();
    }

    /// <summary>
    /// Returns true if the user has interacted with the clicker selector
    /// </summary>
    public bool HasInteracted => m_hasInteracted;

    /// <summary>
    /// Resets the interaction state
    /// </summary>
    public void ResetInteractionState()
    {
        m_hasInteracted = false;
    }
}
