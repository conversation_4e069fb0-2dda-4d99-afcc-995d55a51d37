using System;
using System.Collections.Generic;
using RibCageGames.Animation;
using RibCageGames.Base;
using RibCageGames.Combat;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class StyleMeter : MonoBehaviour
{
    [SerializeField] private Animator m_animator;
    
    [SerializeField] private Image m_backgroundImage;
    [SerializeField] private Image m_fillImage;
    [SerializeField] private Image m_foregroundImage;
    [SerializeField] private TMP_Text m_dpsText;
    [SerializeField] private string m_dpsTextFormat;
    [SerializeField] private float m_dpsValueDecayTime;
    
    [SerializeField] private float m_rankUpSwitchDelay;
    [SerializeField] private float m_rankDownSwitchDelay;

    [SerializeField][AnimatorParameterControl("m_animator")] 
    private AnimatorParameterControl m_rankUp;
    [SerializeField][AnimatorParameterControl("m_animator")] 
    private AnimatorParameterControl m_rankDown;
    [SerializeField][AnimatorParameterControl("m_animator")] 
    private AnimatorParameterControl m_show;
    [SerializeField][AnimatorParameterControl("m_animator")] 
    private AnimatorParameterControl m_hide;
    [SerializeField][AnimatorParameterControl("m_animator")] 
    private AnimatorParameterControl m_level;

    private PlayerService m_playerService;
    private MonoService m_monoService;
    private float m_dpsDamage;
    private float m_dpsDuration;
    private float m_dpsElapsedTime;
    private Color m_dpsTextColor;

    private int m_cachedLevel = 0;

    private void Start()
    {
        m_playerService = ServiceLocator.Get<PlayerService>();
        m_monoService = ServiceLocator.Get<MonoService>();

        m_monoService.OnUpdate.AddListener(ControlledUpdate);

        m_cachedLevel = m_playerService.StyleLevel;
        m_rankUp.Initialize(m_animator);
        m_rankDown.Initialize(m_animator);
        m_show.Initialize(m_animator);
        m_hide.Initialize(m_animator);
        m_level.Initialize(m_animator);
        
        m_playerService.OnEncounterStarted.AddListener(() => m_show.ApplyParameter());
        m_playerService.OnEncounterComplete.AddListener(() => m_hide.ApplyParameter());
        m_playerService.OnAttackLanded.AddListener(PlayerAttackLanded);
        
        //m_show.ApplyParameter();

        SwitchSprites(m_playerService.StyleTierData);
        m_dpsTextColor = m_dpsText.color;
        m_dpsText.color = Color.clear;
    }

    private void PlayerAttackLanded(ActionData move, List<(DamageableRegistry.IDamageable, float)> damageables)
    {
        float addedDamage = 0f;
        foreach ((DamageableRegistry.IDamageable damageable, float damage) hit in damageables)
        {
            addedDamage += hit.damage;
        }
        m_dpsDamage += addedDamage;
        
        //Refresh DPS count
        if (addedDamage > 0f)
        {
            m_dpsDuration = m_dpsValueDecayTime;
        }
    }

    private void SwitchSprites(StyleTier data)
    {
        m_fillImage.sprite = data.FillSprite;
        m_backgroundImage.sprite = data.BackgroundSprite;
        m_foregroundImage.sprite = data.ForegroundSprite;
    }

    private void ControlledUpdate(float delta)
    {
        if (m_playerService.StyleLevel != m_cachedLevel)
        {
            if (m_playerService.StyleLevel > m_cachedLevel)
            {
                m_rankUp.ApplyParameter();
                MonoProcess.WaitForSecondsProcess(m_rankUpSwitchDelay)
                    .Do(() => SwitchSprites(m_playerService.StyleTierData));
            }
            else
            {
                m_rankDown.ApplyParameter();
                MonoProcess.WaitForSecondsProcess(m_rankDownSwitchDelay)
                    .Do(() => SwitchSprites(m_playerService.StyleTierData));
            }
            
            m_cachedLevel = m_playerService.StyleLevel;
        }

        m_level.SetValue(m_playerService.StyleGauge);
        m_level.ApplyParameter();
        m_fillImage.fillAmount = m_playerService.StyleGauge;

        if (m_dpsDuration > 0f)
        {
            m_dpsDuration -= delta;
            m_dpsElapsedTime += delta;
            
            m_dpsText.text = String.Format(m_dpsTextFormat, (m_dpsDamage / Mathf.Max(m_dpsElapsedTime, 1f)).ToString("F0"));
            m_dpsText.color = m_dpsTextColor;
        }
        else //DPS decay duration exceeded
        {
            m_dpsDamage = 0f;
            m_dpsElapsedTime = 0f;
            m_dpsText.color = Color.clear;
        }
    }
}
