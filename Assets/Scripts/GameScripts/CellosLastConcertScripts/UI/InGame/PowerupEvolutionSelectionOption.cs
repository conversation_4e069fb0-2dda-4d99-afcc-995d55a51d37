using System;
using System.Collections.Generic;
using RibCageGames.Base;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class PowerupEvolutionSelectionOption : MonoBehaviour
{
    [SerializeField] private TMP_Text m_powerupNameText;
    [SerializeField] private TMP_Text m_powerupDescriptionText;
    [SerializeField] private Image m_powerupImage;
    [SerializeField] private RectTransform m_selectWidget;
    [SerializeField] private RectTransform m_highlight;
    [SerializeField] private List<RectTransform> m_upgradeNotches;

    private Powerup m_powerup;
    public Powerup Powerup => m_powerup;

    private Action m_onSelectAction;
    public Action OnSelectAction => m_onSelectAction;

    private readonly ServiceReference<PlayerService> m_playerService = new();
    
    public void SetPowerup((Powerup powerup, Action onSelect) data)
    {
        m_onSelectAction = data.onSelect;
        m_powerup = data.powerup;
        m_powerupNameText.text = data.powerup.DisplayName; //Change to display name
        m_powerupDescriptionText.text = data.powerup.Description; //Change to display name

        int currentLevel = m_playerService.Value
            .GetPowerUpLevel(data.powerup.Type switch
            {
                PowerUpType.Delay => PowerUpType.DelayUpgrade,
                PowerUpType.Reverb => PowerUpType.ReverbUpgrade,
                PowerUpType.Flanger => PowerUpType.FlangerUpgrade,
            });
        
        for (var i = 0; i < m_upgradeNotches.Count; i++)
        {
            m_upgradeNotches[i].gameObject.SetActive(i < currentLevel);
        }
    }
    
    public void SetCustomOption((string titleText, string descriptionText, Action onSelect) data)
    {
        m_onSelectAction = data.onSelect;
        m_powerup = null;
        m_powerupNameText.text = data.titleText;
        m_powerupDescriptionText.text = data.descriptionText;

        for (var i = 0; i < m_upgradeNotches.Count; i++)
        {
            m_upgradeNotches[i].gameObject.SetActive(false);
        }
    }
    
    public void Select()
    {
        m_highlight.gameObject.SetActive(true);
        m_selectWidget.gameObject.SetActive(true);
    }
    
    public void Deselect()
    {
        m_highlight.gameObject.SetActive(false);
        m_selectWidget.gameObject.SetActive(false);
    }
}
