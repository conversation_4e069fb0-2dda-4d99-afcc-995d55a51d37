using RibCageGames.Base;
using RibCageGames.UI;
using UnityEngine;
using UnityEngine.Events;

[CreateAssetMenu(fileName = "ComboTutorialTrigger", menuName = "AlphaNomos/Tutorial/ComboTutorialTrigger")]
public class ComboTutorialTrigger : MonoScriptableObject
{
    [SerializeField] private ComboTutorialMoveList m_moveList;
    [SerializeField] private UnityEvent m_onComplete;
    
    public void Activate()
    {
        ServiceLocator.Get<PopupService>()
            .TryGetElement(out ComboSheetMusicDisplay display);
        display.SetTargetCombo(m_moveList, m_onComplete.Invoke);
    }

    public override void Initialize() {}
}
