using System;
using RibCageGames.Animation;
using RibCageGames.Base;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class HealthBar : MonoBehaviour
{
    [SerializeField] private Image[] m_characterFaceImages;
    [SerializeField] private Image m_hpFillImage;
    [SerializeField] private AnimationCurve m_fillCurve;
    [SerializeField] private Image m_hpDamageFillImage;
    [SerializeField] private AnimationCurve m_lingerCurve;
    [SerializeField] private TextMeshProUGUI m_healthText;
    [SerializeField] private string m_healthFormat = "{0} / {1}";
    [SerializeField] private float m_changeDuration;
    
    [SerializeField] private Animator m_healthBarAnimator;
    [SerializeField][AnimatorParameterControl("m_healthBarAnimator")]
    private AnimatorParameterControl m_showDamageParamterControl;
    
    private ServiceReference<PlayerService> m_playerService = new();
    
    private void Start()
    {
        m_playerService.Value.OnPlayerHealthChanged.AddListener(PlayerHealthChanged);
        
        m_showDamageParamterControl.Initialize(m_healthBarAnimator);

        m_playerService.Value.OnPlayerRegistered.AddSingleUseListener((player) =>
        {
            PlayerHealthChanged(0f, 1f);
        });
    }

    private void PlayerHealthChanged(float prevHp, float newHp)
    {
        if (m_characterFaceImages != null && m_characterFaceImages.Length > 0)
        {
            foreach (Image image in m_characterFaceImages)
            {
                image.gameObject.SetActive(false);
            }


            m_characterFaceImages[
                    Mathf.Clamp(Mathf.FloorToInt(newHp * m_characterFaceImages.Length), 0,
                        m_characterFaceImages.Length - 1)]
                .gameObject.SetActive(true);
        }

        m_showDamageParamterControl.ApplyParameter();
        m_healthText.text = String.Format(m_healthFormat, (int) (newHp * m_playerService.Value.MaxHealth), (int) 1f * m_playerService.Value.MaxHealth);

        MonoProcess fillProcess = MonoProcess.New();
        fillProcess.WaitFor(
                () =>
                {
                    //Debug.Log($"Tick");
                    m_hpFillImage.fillAmount =
                        Mathf.Lerp(prevHp, newHp, m_fillCurve.Evaluate(fillProcess.ElapsedTime / m_changeDuration));
                    m_hpDamageFillImage.fillAmount =
                        Mathf.Lerp(prevHp, newHp, m_lingerCurve.Evaluate(fillProcess.ElapsedTime / m_changeDuration));
                    return false;
                }, m_changeDuration)
            .Do(() =>
            {
                //Debug.Log($"Final");
                m_hpFillImage.fillAmount = newHp;
                m_hpDamageFillImage.fillAmount = newHp;
            });
    }
}
