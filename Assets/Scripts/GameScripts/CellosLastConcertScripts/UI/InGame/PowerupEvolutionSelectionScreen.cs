using System;
using System.Collections.Generic;
using System.Threading;
using Cysharp.Threading.Tasks;
using RibCageGames.Base;
using RibCageGames.Input;
using UnityEngine;

public class PowerupEvolutionSelectionScreen : MonoBehaviour
{
    [SerializeField] private List<PowerupEvolutionSelectionOption> m_powerupChoices;
    [SerializeField] private float m_directionRetriggerDelay = 0f;

    private int m_choiceCount = 0;
    private int m_currentChoice = 0;
    private float m_currentDirection = 0f;
    private CancellationTokenSource m_movementCancellationSource;

    private int CurrentChoice
    {
        get => m_currentChoice;
        set
        {
            m_powerupChoices[m_currentChoice].Deselect();

            m_currentChoice = (value + m_choiceCount) % m_choiceCount;
            
            m_powerupChoices[m_currentChoice].Select();
        }
    }

    private readonly ServiceReference<InputService> m_inputService = new();
    
    public void SetPowerupOptions(List<(Powerup powerUp, Action selectAction)> powerupEvolutions)
    {
        m_choiceCount = powerupEvolutions.Count;
        for (int i = 0; i < m_powerupChoices.Count; i++)
        {
            m_powerupChoices[i].Deselect();
            if (i < powerupEvolutions.Count)
            {
                m_powerupChoices[i].SetPowerup(powerupEvolutions[i]);
                m_powerupChoices[i].gameObject.SetActive(true);
            }
            else
            {
                m_powerupChoices[i].gameObject.SetActive(false);
            }
        }

        CurrentChoice = 0;
        
        RegisterInputs();
        
        gameObject.SetActive(true);
    }
    
    private void Select()
    {
        if(m_choiceCount < 1) { return; }
        
        Debug.LogError($"Select {m_powerupChoices[m_currentChoice].Powerup.DisplayName}");
        m_powerupChoices[m_currentChoice].OnSelectAction();
        m_choiceCount = 0;
        DeregisterInputs();
        //trigger select and close animation
        gameObject.SetActive(false);
    }

    private float m_lastDirectionMoved = 0f;

    private void OnMovement(Vector2 direction)
    {
        m_movementCancellationSource?.Cancel();
        m_movementCancellationSource = new CancellationTokenSource();
        
        if (Mathf.Abs(direction.y) > 0.5f &&
            m_lastDirectionMoved * direction.y < 0.25f)
        {
            m_lastDirectionMoved = direction.y;
            MovementProcess(Mathf.Sign(direction.y), m_movementCancellationSource.Token);
        }
        else if (Mathf.Abs(direction.y) < 0.25f)
        {
            m_lastDirectionMoved = 0f;
        }
    }

    private async void MovementProcess(float direction, CancellationToken token)
    {
        while (!token.IsCancellationRequested)
        {
            if (direction > 0f)
            {
                CurrentChoice++;
            }
            else if (direction < 0f)
            {
                CurrentChoice--;
            }

            await UniTask.WaitForSeconds(m_directionRetriggerDelay);
        }
    }
    
    private void RegisterInputs()
    {
        ServiceLocator.Get<PlayerService>().PlayerController.DeregisterNonMovementControls();
        ServiceLocator.Get<PlayerService>().PlayerController.DeregisterMovementControls();
        Dictionary<PlayerInputActions, InputEvent> inputActions = m_inputService.Value.InGameInputs;

        foreach (KeyValuePair<PlayerInputActions, InputEvent> action in inputActions)
        {
            if (action.Key == PlayerInputActions.Movement)
            {
                ((InputVector2Event)action.Value).Performed.AddExclusiveListener(OnMovement);
                ((InputVector2Event)action.Value).Canceled.AddExclusiveListener(OnMovement);
            }
            if (action.Key == PlayerInputActions.Jump)
            {
                ((InputVoidEvent) action.Value).Performed.AddExclusiveListener(Select);
            }
        }
    }

    private void DeregisterInputs()
    {
        Debug.LogError($"Deregister");
        Dictionary<PlayerInputActions, InputEvent> inputActions = m_inputService.Value.InGameInputs;

        foreach (KeyValuePair<PlayerInputActions, InputEvent> action in inputActions)
        {
            if (action.Key == PlayerInputActions.Movement)
            {
                ((InputVector2Event)action.Value).Performed.RemoveListener(OnMovement);
                ((InputVector2Event)action.Value).Canceled.RemoveListener(OnMovement);
                break;
            }
            if (action.Key == PlayerInputActions.Jump)
            {
                ((InputVoidEvent) action.Value).Performed.RemoveListener(Select);
            }
        }
        
        ServiceLocator.Get<PlayerService>().PlayerController.RegisterNonMovementControls();
        ServiceLocator.Get<PlayerService>().PlayerController.RegisterMovementControls();
    }
}
