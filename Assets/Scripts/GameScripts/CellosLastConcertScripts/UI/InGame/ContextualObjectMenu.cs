using System;
using System.Collections.Generic;
using System.Threading;
using Cysharp.Threading.Tasks;
using JetBrains.Annotations;
using RibCageGames.Base;
using RibCageGames.Input;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class ContextualObjectMenu : MonoBehaviour
{
    [SerializeField] private Image m_menuHolder;
    [SerializeField]private TextMeshProUGUI m_mainText;
    [SerializeField] private TextMeshProUGUI m_titleText;
    [SerializeField] private TextMeshProUGUI m_continuePromptText;
    [SerializeField] private List<Transform> m_promptAnchors;
    
    [SerializeField] private TextMeshProUGUI m_jumpPromptText;
    [SerializeField] private GameObject m_continueSymbolJump;
    [SerializeField] private TextMeshProUGUI m_attackPromptText;
    [SerializeField] private GameObject m_continueSymbolAttack;
    [SerializeField] private TextMeshProUGUI m_dodgePromptText;
    [SerializeField] private GameObject m_continueSymbolDodge;
    [SerializeField] private TextMesh<PERSON><PERSON>U<PERSON><PERSON> m_specialPromptText;
    [SerializeField] private GameObject m_continueSymbolSpecial;

    [NonSerialized] private CancellationTokenSource m_followSource;
    
    [CanBeNull] private ContextualObjectMenuTrigger m_currentTrigger = null;
    
    private readonly ServiceReference<PlayerService> m_playerService = new();
    private readonly ServiceReference<InputService> m_inputService = new();
    
    private void Awake()
    {
        m_menuHolder.gameObject.SetActive(false);
        m_continueSymbolJump.gameObject.SetActive(false);
        m_continueSymbolAttack.gameObject.SetActive(false);
        m_continueSymbolDodge.gameObject.SetActive(false);
        m_continueSymbolSpecial.gameObject.SetActive(false);
    }
    
    public void ActivateContext(ContextualObjectMenuTrigger trigger, int junkCost = 0)
    {
        if(trigger == m_currentTrigger) { return; }

        if (m_currentTrigger != null)
        {
            DeactivateContext(m_currentTrigger);
        }

        m_currentTrigger = trigger;
        trigger.InitializeData(junkCost);
        
        m_followSource?.Cancel();
        m_mainText.text = trigger.MainText;
        m_titleText.text = trigger.TitleText;
        if (m_continuePromptText != null)
        {
            m_continuePromptText.text = trigger.ContinuePromptText;
        }

        int promptIndex = 0;
        if (trigger.JumpEnabled)
        {
            m_continueSymbolJump.gameObject.SetActive(true);
            m_jumpPromptText.text = trigger.JumpPromptText;
            m_jumpPromptText.color = trigger.JumpInteractable() ? Color.white : Color.red;
            CopyTransforms(m_promptAnchors[promptIndex++], m_continueSymbolJump.transform);
        }
        
        if (trigger.AttackEnabled)
        {
            m_continueSymbolAttack.gameObject.SetActive(true);
            m_attackPromptText.text = trigger.AttackPromptText;
            m_attackPromptText.color = trigger.AttackInteractable() ? Color.white : Color.red;
            CopyTransforms(m_promptAnchors[promptIndex++], m_continueSymbolAttack.transform);
        }
        
        if (trigger.DodgeEnabled)
        {
            m_continueSymbolDodge.gameObject.SetActive(true);
            m_dodgePromptText.text = trigger.DodgePromptText;
            m_dodgePromptText.color = trigger.DodgeInteractable() ? Color.white : Color.red;
            CopyTransforms(m_promptAnchors[promptIndex++], m_continueSymbolDodge.transform);
        }
        
        if (trigger.SpecialEnabled)
        {
            m_continueSymbolSpecial.gameObject.SetActive(true);
            m_specialPromptText.text = trigger.SpecialPromptText;
            m_specialPromptText.color = trigger.SpecialInteractable() ? Color.white : Color.red;
            CopyTransforms(m_promptAnchors[promptIndex++], m_continueSymbolSpecial.transform);
        }

        m_followSource = new CancellationTokenSource();
        RegisterInputs(trigger, m_followSource.Token);
        FollowAnchor(trigger.MenuAnchor, m_followSource.Token).Forget();
        m_menuHolder.gameObject.SetActive(true);
    }

    private async UniTaskVoid FollowAnchor(Transform anchor, CancellationToken token)
    {
        while (!token.IsCancellationRequested)
        {
            Vector3 screenPosition = Camera.main.WorldToScreenPoint(anchor.position);
            m_menuHolder.transform.position = screenPosition;
            await UniTask.DelayFrame(1);
        }
        DeregisterInputs(m_currentTrigger);
    }

    public void DeactivateContext(ContextualObjectMenuTrigger trigger)
    {
        if (m_currentTrigger != trigger)
        {
            return;
        }

        m_currentTrigger = null;
        m_followSource?.Cancel();
        m_menuHolder.gameObject.SetActive(false);
        m_continueSymbolJump.gameObject.SetActive(false);
        m_continueSymbolAttack.gameObject.SetActive(false);
        m_continueSymbolDodge.gameObject.SetActive(false);
        m_continueSymbolSpecial.gameObject.SetActive(false);
        DeregisterInputs(trigger);
    }
    
    public void HideContext(ContextualObjectMenuTrigger trigger)
    {
        if (m_currentTrigger != trigger)
        {
            return;
        }

        m_currentTrigger = null;
        m_followSource?.Cancel();
        m_menuHolder.gameObject.SetActive(false);
        m_continueSymbolJump.gameObject.SetActive(false);
        m_continueSymbolAttack.gameObject.SetActive(false);
        m_continueSymbolDodge.gameObject.SetActive(false);
        m_continueSymbolSpecial.gameObject.SetActive(false);
    }

    private void RegisterInputs(ContextualObjectMenuTrigger trigger, CancellationToken token)
    {
        m_playerService.Value.PlayerController.DeregisterNonMovementControls();
        Dictionary<PlayerInputActions, InputEvent> inputActions = m_inputService.Value.InGameInputs;

        foreach (KeyValuePair<PlayerInputActions, InputEvent> action in inputActions)
        {
            switch (action.Key)
            {
                case PlayerInputActions.Jump:
                    ((InputVoidEvent) action.Value).Performed.AddExclusiveListener(trigger.JumpPressed.Invoke, cancellationToken: token);
                    break;
                case PlayerInputActions.LightAttack:
                    ((InputVoidEvent) action.Value).Performed.AddExclusiveListener(trigger.AttackPressed.Invoke, cancellationToken: token);
                    break;
                case PlayerInputActions.Special:
                    ((InputVoidEvent) action.Value).Performed.AddExclusiveListener(trigger.SpecialPressed.Invoke, cancellationToken: token);
                    break;
                case PlayerInputActions.Dodge:
                    ((InputVoidEvent) action.Value).Performed.AddExclusiveListener(trigger.DodgePressed.Invoke, cancellationToken: token);
                    break;
            }
        }
    }
    
    private void DeregisterInputs(ContextualObjectMenuTrigger trigger)
    {
        if (trigger == null)
        {
            return;
        }

        Dictionary<PlayerInputActions, InputEvent> inputActions = m_inputService.Value.InGameInputs;

        foreach (KeyValuePair<PlayerInputActions, InputEvent> action in inputActions)
        {
            switch (action.Key)
            {
                case PlayerInputActions.Jump:
                    ((InputVoidEvent) action.Value).Performed.RemoveListener(trigger.JumpPressed.Invoke);
                    break;
                case PlayerInputActions.LightAttack:
                    ((InputVoidEvent) action.Value).Performed.RemoveListener(trigger.AttackPressed.Invoke);
                    break;
                case PlayerInputActions.Special:
                    ((InputVoidEvent) action.Value).Performed.RemoveListener(trigger.SpecialPressed.Invoke);
                    break;
                case PlayerInputActions.Dodge:
                    ((InputVoidEvent) action.Value).Performed.RemoveListener(trigger.DodgePressed.Invoke);
                    break;
            }
        }
        m_playerService.Value.PlayerController.RegisterNonMovementControls();
    }

    private void CopyTransforms(Transform sourceTransform, Transform targetTransform)
    {
        // Copy the position, rotation, and scale of the source transform to the target transform
        targetTransform.position = sourceTransform.position;
        targetTransform.rotation = sourceTransform.rotation;
        targetTransform.localScale = sourceTransform.localScale;
    }
}
