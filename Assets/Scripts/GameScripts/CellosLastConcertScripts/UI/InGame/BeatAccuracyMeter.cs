using System.Collections.Generic;
using Coffee.UIExtensions;
using Cysharp.Threading.Tasks;
using RibCageGames.Base;
using RibCageGames.Input;
using RibCageGames.Music;
using UnityEngine;
using UnityEngine.Events;

public class BeatAccuracyMeter : MonoBehaviour
{
    [SerializeField] private Transform m_startLocator;
    [SerializeField] private Transform m_endLocator;
    
    [SerializeField] private UIParticle m_UIParticle;
    
    private UnityAction m_onButtonPress;
    private BeatSystem m_beatSystem;
    
    private async void OnEnable()
    {
        await UniTask.WaitForSeconds(1f);
        RegisterInputs();
        ServiceLocator.Get<MonoService>().OnUpdate.AddListener(ControlledUpdate);
        if (m_beatSystem == null)
        {
            m_beatSystem = ServiceLocator.Get<BeatSystem>();
        }
    }
    
    private void OnDisable()
    {
        DeregisterInputs();
        ServiceLocator.Get<MonoService>().OnUpdate.RemoveListener(ControlledUpdate);
    }

    private void ControlledUpdate(float delta)
    {
        float value = m_beatSystem.BeatPercentage;
        value += 0.5f;
        if (value > 1f)
        {
            value -= 1f;
        }
        m_UIParticle.transform.position = Vector3.Lerp(m_startLocator.position, m_endLocator.position, value);
    }


    private void RegisterInputs()
    {
        Dictionary<PlayerInputActions, InputEvent> inputActions = ServiceLocator.Get<InputService>().InGameInputs;

        foreach (KeyValuePair<PlayerInputActions, InputEvent> action in inputActions)
        {
            switch (action.Key)
            {
                case PlayerInputActions.Jump:
                case PlayerInputActions.LightAttack:
                case PlayerInputActions.Dodge:
                case PlayerInputActions.Special:
                    m_onButtonPress = () => OnButtonPress((InputVoidEvent)action.Value, action.Key);
                    ((InputVoidEvent) action.Value).Performed.AddListener(m_onButtonPress);
                    break;
            }
        }
    }
    
    private void DeregisterInputs()
    {
        Dictionary<PlayerInputActions, InputEvent> inputActions = ServiceLocator.Get<InputService>().InGameInputs;

        foreach (KeyValuePair<PlayerInputActions, InputEvent> action in inputActions)
        {
            switch (action.Key)
            {
                case PlayerInputActions.Jump:
                case PlayerInputActions.LightAttack:
                case PlayerInputActions.Dodge:
                case PlayerInputActions.Special:
                    var x = ((InputVoidEvent)action.Value).Performed;
                    if (x != null && m_onButtonPress != null)
                    {
                        ((InputVoidEvent) action.Value).Performed.RemoveListener(m_onButtonPress);
                    }
                    break;
            }
        }
    }

    private void OnButtonPress(InputVoidEvent input, PlayerInputActions action)
    {
        m_UIParticle.Play();
    }
}
