using RibCageGames.Base;
using UnityEngine;
using UnityEngine.Events;

public class PowerupUpgradeContextTrigger : ContextualObjectMenuTrigger
{

    private const int m_maxUpgradeLevel = 5;
    public override bool JumpEnabled => m_reverbUpgradeLevel < m_maxUpgradeLevel;
    public override string JumpPromptText => "Upgrade reverb";
    public override UnityEvent JumpPressed => m_jumpPressed;
    public override bool AttackEnabled => m_delayUpgradeLevel < m_maxUpgradeLevel;
    public override string AttackPromptText => "Upgrade delay";
    public override UnityEvent AttackPressed => m_attackPressed;
    public override bool DodgeEnabled => m_flangerUpgradeLevel < m_maxUpgradeLevel;
    public override string DodgePromptText => "Upgrade flanger";
    public override UnityEvent DodgePressed => m_dodgePressed;

    [SerializeField] private PowerUpPickup m_pickup;
    
    private PlayerService m_playerService;
    private int m_reverbUpgradeLevel;
    private int m_delayUpgradeLevel;
    private int m_flangerUpgradeLevel;
    
    protected override void Awake()
    {
        m_playerService = ServiceLocator.Get<PlayerService>();
    }
    
    public override void InitializeData()
    {
        m_reverbUpgradeLevel =
            m_playerService.PowerUpLevelDict.TryGetValue(PowerUpType.ReverbUpgrade, out (int level, BonusEffect, Powerup) reverbbonus)
                ? reverbbonus.level
                : 0;
        m_delayUpgradeLevel =
            m_playerService.PowerUpLevelDict.TryGetValue(PowerUpType.DelayUpgrade, out (int level, BonusEffect, Powerup) delaybonus)
                ? delaybonus.level
                : 0;
        m_flangerUpgradeLevel =
            m_playerService.PowerUpLevelDict.TryGetValue(PowerUpType.DelayUpgrade, out (int level, BonusEffect, Powerup) flangerbonus)
                ? flangerbonus.level
                : 0;

        m_jumpPressed = new UnityEvent();
        m_jumpPressed.AddListener(() =>
        {
            m_pickup.Pickup();
            m_playerService.AddBonusEffect(
                new StatUpgradeBonus(PowerUpType.ReverbUpgrade),
                m_pickup.PowerUp, m_playerService.GetPowerUpLevel(PowerUpType.ReverbUpgrade) + 1);
        });
        
        m_attackPressed = new UnityEvent();
        m_attackPressed.AddListener(() =>
        {
            m_pickup.Pickup();
            m_playerService.AddBonusEffect(
                new StatUpgradeBonus(PowerUpType.DelayUpgrade),
                m_pickup.PowerUp, m_playerService.GetPowerUpLevel(PowerUpType.DelayUpgrade) + 1);
        });
        
        m_dodgePressed = new UnityEvent();
        m_dodgePressed.AddListener(() =>
        {
            m_pickup.Pickup();
            m_playerService.AddBonusEffect(
                new StatUpgradeBonus(PowerUpType.FlangerUpgrade),
                m_pickup.PowerUp, m_playerService.GetPowerUpLevel(PowerUpType.FlangerUpgrade) + 1);
        });
    }
}