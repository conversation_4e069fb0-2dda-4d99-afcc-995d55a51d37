using System.Collections;
using System.Collections.Generic;
using RibCageGames.Base;
using UnityEngine;

public class TutorialPromptsController : MonoBehaviour
{
    [SerializeField] private SerializableDictionary<TutorialPrompts, TutorialPrompt> m_promptVisuals;

    private void Start()
    {
        TutorialService tutorialService = ServiceLocator.Get<TutorialService>();
        tutorialService.OnTutorialShow.AddListener(TutorialShow);
        tutorialService.OnTutorialHide.AddListener(TutorialHide);
    }

    private void TutorialShow(TutorialPrompts prompt)
    {
        if (m_promptVisuals.TryGetValue(prompt, out TutorialPrompt visual))
        {
            visual.Activate();
        }
    }
    
    private void TutorialHide(TutorialPrompts prompt)
    {
        if (m_promptVisuals.TryGetValue(prompt, out TutorialPrompt visual))
        {
            visual.Deactivate();
        }
    }
}
