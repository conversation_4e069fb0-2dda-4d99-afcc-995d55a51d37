using RibCageGames.Base;
using RibCageGames.Music;
using UnityEngine;
using UnityEngine.UI;

public class VolumeControlSlider : MonoBehaviour
{
    [SerializeField] private VolumeGroup m_group;
    [SerializeField] private Slider m_slider;
    
    private BeatSystem m_beatSystem;
    
    private void Start()
    {
        m_beatSystem = ServiceLocator.Get<BeatSystem>();
        MonoProcess.NextFrame.Do(() =>
        {
            m_slider.value = ((m_slider.maxValue - m_slider.minValue) * m_beatSystem.GetDirectVolume(m_group)) + m_slider.minValue;
            m_slider.onValueChanged.AddListener(OnValueChanged);
        });
    }

    private void OnValueChanged(float value)
    {
        m_beatSystem.SetVolume(m_group, (value - m_slider.minValue) / (m_slider.maxValue - m_slider.minValue));
    }
}
