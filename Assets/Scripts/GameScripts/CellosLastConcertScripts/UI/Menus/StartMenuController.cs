using System.Collections.Generic;
using System.Threading;
using Cysharp.Threading.Tasks;
using DG.Tweening;
using RibCageGames.Base;
using RibCageGames.Input;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.UI;

public class StartMenuController : MonoBehaviour
{
    [SerializeField] private CanvasGroup m_startButtonFade;
    [SerializeField] private float m_startButtonFadeInTime;
    [SerializeField] private CanvasGroup m_logoFade;
    [SerializeField] private float m_logoFadeInTime;
    [SerializeField] private CanvasGroup m_menuFade;
    [SerializeField] private float m_menuFadeInTime;
    [SerializeField] private float m_directionRetriggerDelay = 0f;
    
    [SerializeField] private UnityEvent m_startMenuPressed;
    [SerializeField] private UnityEvent m_selectionChanged;
    [SerializeField] private UnityEvent m_startGamePressed;

    [SerializeField] private List<Image> m_optionHighlights;
    [SerializeField] private List<Button> m_optionButtons;
    
    private readonly ServiceReference<InputService> m_inputService = new();
    private bool m_menuOn = false;
    private int m_choiceCount = 0;
    private int m_currentChoice = 0;
    private float m_currentDirection = 0f;
    private CancellationTokenSource m_movementCancellationSource;
    private float m_lastDirectionMoved = 0f;
    
    private int CurrentChoice
    {
        get => m_currentChoice;
        set
        {
            m_optionHighlights[m_currentChoice].gameObject.SetActive(false);

            m_currentChoice = (value + m_choiceCount) % m_choiceCount;
            
            m_optionHighlights[m_currentChoice].gameObject.SetActive(true);
        }
    }

    public void SetCurrentChoice(int value)
    {
        CurrentChoice = value;
    }

    public void FadeInStartButton()
    {
        m_choiceCount = m_optionButtons.Count;
        m_startButtonFade.DOFade(1f, m_startButtonFadeInTime);
        RegisterInputs();
        CurrentChoice = 0;
    }
    
    private void RegisterInputs()
    {
        //ServiceLocator.Get<PlayerService>().PlayerController.DeregisterNonMovementControls();
        //ServiceLocator.Get<PlayerService>().PlayerController.DeregisterMovementControls();
        Dictionary<PlayerInputActions, InputEvent> inputActions = m_inputService.Value.InGameInputs;

        foreach (KeyValuePair<PlayerInputActions, InputEvent> action in inputActions)
        {
            if (action.Key == PlayerInputActions.Movement)
            {
                ((InputVector2Event)action.Value).Performed.AddExclusiveListener(OnMovement);
                ((InputVector2Event)action.Value).Canceled.AddExclusiveListener(OnMovement);
            }
            if (action.Key == PlayerInputActions.Jump)
            {
                ((InputVoidEvent) action.Value).Performed.AddExclusiveListener(Select);
            }
        }
    }

    private async void Select()
    {
        if (!m_menuOn)
        {
            m_menuOn = true;
            
            m_startButtonFade.DOFade(0f, m_logoFadeInTime);

            await UniTask.WaitForSeconds(m_logoFadeInTime);
            
            m_logoFade.DOFade(1f, m_logoFadeInTime);
            m_menuFade.DOFade(1f, m_menuFadeInTime);
            m_startMenuPressed?.Invoke();
        }
        else
        {
            m_optionButtons[m_currentChoice].onClick?.Invoke();
        }
    }
    
    private void OnMovement(Vector2 direction)
    {
        m_movementCancellationSource?.Cancel();
        m_movementCancellationSource = new CancellationTokenSource();
        
        if (Mathf.Abs(direction.y) > 0.5f &&
            m_lastDirectionMoved * direction.y < 0.25f)
        {
            m_lastDirectionMoved = direction.y;
            MovementProcess(- Mathf.Sign(direction.y), m_movementCancellationSource.Token);
        }
        else if (Mathf.Abs(direction.y) < 0.25f)
        {
            m_lastDirectionMoved = 0f;
        }
    }

    private async void MovementProcess(float direction, CancellationToken token)
    {
        while (!token.IsCancellationRequested)
        {
            if (direction > 0f)
            {
                CurrentChoice++;
                m_selectionChanged?.Invoke();
            }
            else if (direction < 0f)
            {
                CurrentChoice--;
                m_selectionChanged?.Invoke();
            }

            await UniTask.WaitForSeconds(m_directionRetriggerDelay);
        }
    }

    private void DeregisterInputs()
    {
        Debug.LogError($"Deregister");
        Dictionary<PlayerInputActions, InputEvent> inputActions = m_inputService.Value.InGameInputs;

        foreach (KeyValuePair<PlayerInputActions, InputEvent> action in inputActions)
        {
            if (action.Key == PlayerInputActions.Movement)
            {
                ((InputVector2Event)action.Value).Performed.RemoveListener(OnMovement);
                ((InputVector2Event)action.Value).Canceled.RemoveListener(OnMovement);
                break;
            }
            if (action.Key == PlayerInputActions.Jump)
            {
                ((InputVoidEvent) action.Value).Performed.RemoveListener(Select);
            }
        }
        
        //ServiceLocator.Get<PlayerService>().PlayerController.RegisterNonMovementControls();
        //ServiceLocator.Get<PlayerService>().PlayerController.RegisterMovementControls();
    }
}
