using System;
using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using DG.Tweening;
using RibCageGames.Base;
using RibCageGames.Editor.EditorUtillities.SerializeInterface;
using RibCageGames.Input;
using RibCageGames.Music;
using RibCageGames.Sound;
using TMPro;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.UI;

public class BeatOffsetSettingDisplay : MonoBehaviour
{
    [Header("Configuration")]
    [SerializeField] private int m_setupInputs;
    [SerializeField] private int m_succesfulInputsToComplete = 4;
    [SerializeField] private List<string> m_successTexts;
    
    [Header("References")]
    [SerializeField] private CanvasGroup m_canvasGroup;
    [SerializeField] private float m_fadeInDuration = 2f;
    [SerializeField] private RectTransform m_leftPlaceholder;
    [SerializeField] private RectTransform m_rightPlaceholder;
    [SerializeField] private RectTransform m_centerPlaceholder;
    [SerializeField] private RectTransform m_leftIndicator;
    [SerializeField] private RectTransform m_rightIndicator;
    [SerializeField] private AnimationCurve m_indicatorCurve;
    
    [SerializeField] private BeatVisualSlider m_metronomeSlider;
    
    [SerializeField] private TMP_Text m_endSetupText;
    [SerializeField] private TMP_Text m_successText;
    [SerializeField] private Slider m_offsetSlider;
    
    [SerializeField] private InterfaceReference<ISoundSource> m_setupEffect;
    [SerializeField] private InterfaceReference<ISoundSource> m_missEffect;
    [SerializeField] private InterfaceReference<ISoundSource> m_hitEffect;
    
    [SerializeField] private TMP_Text m_manualHint;
    [SerializeField] private TMP_Text m_manualSetting;
    [SerializeField] private TMP_Text m_manualContinue;
    //[SerializeField] private CanvasGroup m_metronomeGroup;
    
    [SerializeField] private UnityEvent m_onMetronomeVisualBeat;
    [SerializeField] private float m_animationEventOffset;

    [SerializeField] private UnityEvent m_setupComplete;
    
    private int m_currentInputs = 0;
    private ServiceReference<BeatSystem> m_beatSystem = new();
    private ServiceReference<SoundService> m_soundService = new();
    private ServiceReference<MonoService> m_monoService = new();
    private ServiceReference<InputService> m_inputService = new();
    
    private int m_inputDirection;
    
    //private int m_onBeatInputs = 0;
    private bool m_complete;
    private bool m_destroyed = false;
    //private bool m_settingMetronome = false;
    //private bool m_settingInput = false;
    private bool m_hittingInputs = false;

    private float m_beatIndicatorValue = 0f;

    private async void Start()
    {
        m_monoService.Value.OnUpdate.AddListener(ControlledUpdate);
        m_currentInputs = 0;
        m_offsetSlider.value = 0f;
        m_endSetupText.gameObject.SetActive(false);
        m_manualHint.gameObject.SetActive(false);
        m_manualSetting.gameObject.SetActive(false);
        m_manualContinue.gameObject.SetActive(false);
        m_successText.text = string.Empty;
        m_complete = false;

        m_canvasGroup.alpha = 0f;
        
        m_beatSystem.Value.OnBeat.AddListener(OnBeat);
        m_beatSystem.Value.VisualOnMainBeat.AddListener(VisualOnMainBeat);

        await UniTask.DelayFrame(3);
        m_offsetSlider.value = 0f;
    }

    private void VisualOnMainBeat()
    {
        m_beatIndicatorValue = 0f;
    }

    private async void OnBeat(bool mainBeat)
    {
        float offset = m_beatSystem.Value.VisualOffset + m_animationEventOffset;

        if (Mathf.Approximately(offset, 0f) && mainBeat)
        {
            m_onMetronomeVisualBeat?.Invoke();
        }
        else if (offset > 0f && mainBeat)
        {
            await UniTask.WaitForSeconds(offset);
            m_onMetronomeVisualBeat?.Invoke();
        }else if (offset < 0f && !mainBeat)
        {
            await UniTask.WaitForSeconds(m_beatSystem.Value.HalfBeatLength + offset);
            m_onMetronomeVisualBeat?.Invoke();
        }
    }

    private void OnDestroy()
    {
        m_destroyed = true;
        DeregisterInputs();
        if (m_holdProcess != null)
        {
            m_holdProcess.Stop();
            m_holdProcess = null;
        }
    }

    private void RegisterInputs()
    {
        m_manualHint.gameObject.SetActive(true);
        Dictionary<PlayerInputActions, InputEvent> inputActions = ServiceLocator.Get<InputService>().InGameInputs;

        foreach (KeyValuePair<PlayerInputActions, InputEvent> action in inputActions)
        {
            switch (action.Key)
            {
                case PlayerInputActions.Jump:
                case PlayerInputActions.LightAttack:
                case PlayerInputActions.Dodge:
                case PlayerInputActions.Special:
                    m_onButtonPress = () => OnButtonPress((InputVoidEvent)action.Value, action.Key);
                    ((InputVoidEvent) action.Value).Performed.AddListenerUntil(m_onButtonPress, () => m_destroyed || m_complete);
                    break;
            }
        }
    }
    
    private void DeregisterInputs()
    {
        Dictionary<PlayerInputActions, InputEvent> inputActions = ServiceLocator.Get<InputService>().InGameInputs;

        foreach (KeyValuePair<PlayerInputActions, InputEvent> action in inputActions)
        {
            switch (action.Key)
            {
                case PlayerInputActions.Jump:
                case PlayerInputActions.LightAttack:
                case PlayerInputActions.Dodge:
                case PlayerInputActions.Special:
                    var x = ((InputVoidEvent)action.Value).Performed;
                    if (x != null && m_onButtonPress != null)
                    {
                        ((InputVoidEvent) action.Value).Performed.RemoveListener(m_onButtonPress);
                    }
                    break;
            }
        }

        if (m_manualHint != null)
        {
            m_manualHint.gameObject.SetActive(false);
        }
    }

    private MonoProcess m_holdProcess;
    private void StartHold(InputVoidEvent input)
    {
        if (m_holdProcess != null)
        {
            m_holdProcess.Stop();
            m_holdProcess = null;
        }
        m_holdProcess = MonoProcess.New();
        m_holdProcess.HoldInput(input, OnHoldComplete);
    }

    private void OnHoldComplete()
    {
        m_manualSetting.gameObject.SetActive(true);
        m_manualContinue.gameObject.SetActive(true);
        m_manualHint.gameObject.SetActive(false);
        OnComplete();
    }

    private UnityAction m_onButtonPress;
    
    private void OnButtonPress(InputVoidEvent input, PlayerInputActions action)
    {
        //Why does deregister not work?!
        if (m_endSetupText == null)
        {
            DeregisterInputs();
            return;
        }
        
        if (m_currentInputs < m_succesfulInputsToComplete)
        {
            m_currentInputs++;
            m_beatSystem.Value.AddInputSample();
            StartHold(input);
        }
        else
        {
            return;
        }

        if (m_beatSystem.Value.IsOnMainBeat)
        {
            if (!m_complete && m_currentInputs <= m_successTexts.Count)
            {
                m_successText.text = m_successTexts[m_currentInputs - 1];   
            }
            m_soundService.Value.PlayClipWithCallback(m_hitEffect.Value);
            if (m_currentInputs == m_succesfulInputsToComplete)
            {
                OnComplete();
            }
        }
        else
        {
            m_currentInputs = 0;
            m_soundService.Value.PlayClipWithCallback(m_missEffect.Value);
            m_successText.text = string.Empty;
        }
        m_offsetSlider.value = ((float) m_currentInputs) / m_setupInputs;
    }
    
    private void OnComplete()
    {
        if (m_complete) { return; }

        m_complete = true;
        DeregisterInputs();
        if (m_holdProcess != null)
        {
            m_holdProcess.Stop();
            m_holdProcess = null;
        }

        m_beatSystem.Value.SaveVisualSettingData();
        m_beatSystem.Value.StopCalibrationMetronome();
        
        m_canvasGroup.DOFade(0f, m_fadeInDuration).onComplete += m_setupComplete.Invoke;
        
        //m_setupComplete?.Invoke();
        //MonoProcess.WaitForSecondsProcess(1f).Do(() =>
        //{
            //ServiceLocator.Get<LevelManagementService>().LoadNextLevel();
        //});
    }

    [SerializeField] private GameObject m_authenticationInProgress;
    [SerializeField] private GameObject m_authenticationFailed;
    [SerializeField] private TMP_InputField m_pinInputField;
    
    public async void FadeIn()
    {
        //If already set offsets

        if (false)
        {
            m_canvasGroup.alpha = 0f;
            OnComplete();
            return;
        }
        
        /*var gameTester = ServiceLocator.Get<GameTesterService>();
        if (gameTester != null)
        {
            m_authenticationInProgress.gameObject.SetActive(true);
            await UniTask.DelayFrame(1);
            m_pinInputField.Select();
            m_pinInputField.onSubmit.AddListener(async (string pin) =>
            {
                gameTester.Auth(pin);
                Debug.LogError($"Waiting for authentication");
                await UniTask.WaitUntil(() => gameTester.Authenticated);
                Debug.LogError($"Authentication done");
                if (gameTester.AuthenticationSuccessful)
                {
                    m_authenticationInProgress.gameObject.SetActive(false);
                    m_canvasGroup.DOFade(2f, m_fadeInDuration).onComplete += RegisterInputs;
                }
                else
                {
                    m_authenticationInProgress.gameObject.SetActive(false);
                    m_authenticationFailed.gameObject.SetActive(true);
                    await UniTask.WaitForSeconds(3f);
                    Application.Quit();
                }
            });
        }
        else
        {*/
            m_canvasGroup.DOFade(2f, m_fadeInDuration).onComplete +=
                () =>
                {
                    m_beatSystem.Value.StartCalibrationMetronome();
                    RegisterInputs();
                };
        //}
    }
    
    private void ControlledUpdate(float delta)
    {
        m_beatIndicatorValue += delta / m_beatSystem.Value.BeatLength;
        
        m_leftIndicator.anchoredPosition =
            Vector2.Lerp(m_leftPlaceholder.anchoredPosition,
                m_centerPlaceholder.anchoredPosition,
                m_beatIndicatorValue);
        
        m_leftIndicator.localScale =
            Vector3.Lerp(m_leftPlaceholder.localScale,
                m_centerPlaceholder.localScale,
                m_beatIndicatorValue);
        
        m_rightIndicator.anchoredPosition =
            Vector2.Lerp(m_rightPlaceholder.anchoredPosition,
                m_centerPlaceholder.anchoredPosition,
                m_beatIndicatorValue);
        
        m_rightIndicator.localScale =
            Vector3.Lerp(m_rightPlaceholder.localScale,
                m_centerPlaceholder.localScale,
                m_beatIndicatorValue);
    }
}
