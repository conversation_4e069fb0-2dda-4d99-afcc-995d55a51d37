using System;
using UnityEngine;

public class ProceduralLevelPopulator : MonoBehaviour
{
    [SerializeField] private MeshFilter m_mesh;
    public GameObject prefabToSpawn;
    public float spawnOffset = 0.1f;

    private void LoadMeshData()
    {
        Mesh mesh = m_mesh.mesh;
        Color[] colors = mesh.colors;
        Vector3[] vertices = mesh.vertices;

        if (colors.Length != vertices.Length)
        {
            Debug.LogError("Vertex colors array length does not match vertex array length.");
            return;
        }

        for (int i = 0; i < vertices.Length; i++)
        {
            if (colors[i].r > 0.5f && colors[i].g < 0.5f && colors[i].b < 0.5f) // Check if the red component is dominant
            {
                // Convert local vertex position to world position
                Vector3 worldPosition = transform.TransformPoint(vertices[i]);

                // Instantiate the prefab at the vertex position with an optional offset
                Instantiate(prefabToSpawn, worldPosition + transform.up * spawnOffset, Quaternion.identity);
            }
        }
    }

    private void OnDrawGizmosSelected()
    {
        Mesh mesh = m_mesh.mesh;
        Color[] colors = mesh.colors;
        Vector3[] vertices = mesh.vertices;

        if (colors.Length != vertices.Length)
        {
            Debug.LogError($"Vertex colors array length ({colors.Length}) does not match vertex array length ({vertices.Length})");
            return;
        }

        for (int i = 0; i < vertices.Length; i++)
        {
            Vector3 worldPosition = transform.TransformPoint(vertices[i]);
            
            if (colors[i].r > 0.5f || colors[i].g > 0.5f || colors[i].b > 0.5f)
            {
                Gizmos.color = colors[i];    
            }
            else
            {
                Gizmos.color = Color.magenta;
            }
            
            Gizmos.DrawSphere(worldPosition, 0.1f);
        }
    }
}
