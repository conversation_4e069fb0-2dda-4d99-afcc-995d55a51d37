using System;
using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using RibCageGames.Base;
using UnityEngine;
using UnityEngine.Events;

/// <summary>
/// Manages a collection of PickupSpawnBase objects using pooling system.
/// Handles spawning different types of pickups and tracks pickup events.
/// </summary>
public class RewardHandler : MonoBehaviour
{
    [SerializeField] private Transform m_spawnLocator;
    
    [Header("Pickup Pool Configuration")]
    [SerializeField] private List<PickupPoolMapping> m_pickupPoolMappings = new();

    [Header("Events")]
    [SerializeField] private UnityEvent<PickupType, PickupSpawnBase> m_onPickupSpawned;
    [SerializeField] private UnityEvent<PickupType, PickupSpawnBase> m_onPickupCollected;

    public Transform SpawnLocator => m_spawnLocator;
    
    // Runtime dictionary for fast lookup
    private Dictionary<PickupType, PickupPrefabPool> m_pickupPoolDict;
    private List<PickupSpawnBase> m_activePickups = new List<PickupSpawnBase>();

    /// <summary>
    /// Private class mapping PickupType to PickupPrefabPool assets
    /// </summary>
    [Serializable]
    private class PickupPoolMapping
    {
        [SerializeField] private PickupType m_pickupType;
        [SerializeField] private PickupPrefabPool m_pickupPool;

        public PickupType PickupType => m_pickupType;
        public PickupPrefabPool PickupPool => m_pickupPool;
    }

    #region Properties

    /// <summary>
    /// Event triggered when a pickup is spawned
    /// </summary>
    public UnityEvent<PickupType, PickupSpawnBase> OnPickupSpawned => m_onPickupSpawned;

    /// <summary>
    /// Event triggered when a pickup is collected
    /// </summary>
    public UnityEvent<PickupType, PickupSpawnBase> OnPickupCollected => m_onPickupCollected;

    /// <summary>
    /// Gets the list of currently active pickups
    /// </summary>
    public IReadOnlyList<PickupSpawnBase> ActivePickups => m_activePickups.AsReadOnly();

    /// <summary>
    /// Gets the number of active pickups
    /// </summary>
    public int ActivePickupCount => m_activePickups.Count;

    #endregion

    #region Unity Lifecycle

    private void Awake()
    {
        InitializePickupPools();
    }

    #endregion

    #region Initialization

    /// <summary>
    /// Initializes the pickup pool dictionary from the mappings
    /// </summary>
    private void InitializePickupPools()
    {
        m_pickupPoolDict = new Dictionary<PickupType, PickupPrefabPool>();

        foreach (PickupPoolMapping mapping in m_pickupPoolMappings)
        {
            if (mapping.PickupPool != null)
            {
                if (m_pickupPoolDict.ContainsKey(mapping.PickupType))
                {
                    Debug.LogWarning($"RewardHandler: Duplicate PickupType {mapping.PickupType} found in mappings. Using first occurrence.");
                    continue;
                }

                m_pickupPoolDict[mapping.PickupType] = mapping.PickupPool;
            }
            else
            {
                Debug.LogWarning($"RewardHandler: PickupPool is null for PickupType {mapping.PickupType}");
            }
        }

        Debug.Log($"RewardHandler: Initialized {m_pickupPoolDict.Count} pickup pool mappings");
    }

    #endregion

    #region Pickup Spawning

    /// <summary>
    /// Spawns a pickup of the specified type at the given position
    /// </summary>
    /// <param name="pickupType">Type of pickup to spawn</param>
    /// <returns>The spawned pickup instance, or null if spawning failed</returns>
    public PickupSpawnBase SpawnPickup(PickupType pickupType, Action onPickupCollected = null)
    {
        if (!m_pickupPoolDict.TryGetValue(pickupType, out PickupPrefabPool pool))
        {
            Debug.LogWarning($"RewardHandler: No pool found for PickupType {pickupType}");
            return null;
        }

        if (pool == null)
        {
            Debug.LogError($"RewardHandler: Pool for PickupType {pickupType} is null");
            return null;
        }

        // Get instance from pool
        PickupSpawnBase pickup = pool.GetInstance();
        if (pickup == null)
        {
            Debug.LogError($"RewardHandler: Failed to get instance from pool for PickupType {pickupType}");
            return null;
        }

        // Set position and rotation
        pickup.transform.position = m_spawnLocator.position;
        pickup.transform.rotation = m_spawnLocator.rotation;

        // Subscribe to pickup event
        pickup.OnPickup.AddSingleUseListener(() => OnPickupCollectedInternal(pickupType, pickup, onPickupCollected));

        // Activate the pickup
        pickup.ActivatePoolable();

        // Track active pickup
        m_activePickups.Add(pickup);

        // Trigger spawn event
        m_onPickupSpawned?.Invoke(pickupType, pickup);

        return pickup;
    }

    #endregion

    #region Pickup Management

    /// <summary>
    /// Internal method called when a pickup is collected
    /// </summary>
    /// <param name="pickupType">Type of pickup that was collected</param>
    /// <param name="pickup">The pickup instance that was collected</param>
    private async void OnPickupCollectedInternal(PickupType pickupType, PickupSpawnBase pickup, Action onPickupCollected = null)
    {
        // Remove from active pickups
        m_activePickups.Remove(pickup);

        // Trigger collection event
        m_onPickupCollected?.Invoke(pickupType, pickup);
        onPickupCollected?.Invoke();
        
        await UniTask.WaitForSeconds(pickup.PickupDisableDelay);
        ClearAllPickups();
    }

    /// <summary>
    /// Deactivates and returns all active pickups to their pools
    /// </summary>
    public void ClearAllPickups()
    {
        List<PickupSpawnBase> pickupsToRemove = new List<PickupSpawnBase>(m_activePickups);

        foreach (PickupSpawnBase pickup in pickupsToRemove)
        {
            if (pickup != null)
            {
                pickup.DeactivatePoolable();
            }
        }

        m_activePickups.Clear();
    }

    /// <summary>
    /// Gets the pool for a specific pickup type
    /// </summary>
    /// <param name="pickupType">The pickup type to get the pool for</param>
    /// <returns>The pickup pool, or null if not found</returns>
    public PickupPrefabPool GetPoolForType(PickupType pickupType)
    {
        m_pickupPoolDict.TryGetValue(pickupType, out PickupPrefabPool pool);
        return pool;
    }

    /// <summary>
    /// Checks if a pickup type is supported by this handler
    /// </summary>
    /// <param name="pickupType">The pickup type to check</param>
    /// <returns>True if the pickup type is supported, false otherwise</returns>
    public bool SupportsPickupType(PickupType pickupType)
    {
        return m_pickupPoolDict.ContainsKey(pickupType);
    }

    #endregion

    #region Debug Methods

    [ContextMenu("Debug: Log Active Pickups")]
    private void DebugLogActivePickups()
    {
        Debug.Log($"RewardHandler: {m_activePickups.Count} active pickups:");
        for (int i = 0; i < m_activePickups.Count; i++)
        {
            PickupSpawnBase pickup = m_activePickups[i];
            Debug.Log($"  {i}: {pickup.name} at {pickup.transform.position}");
        }
    }

    [ContextMenu("Debug: Clear All Pickups")]
    private void DebugClearAllPickups()
    {
        if (Application.isPlaying)
        {
            ClearAllPickups();
        }
    }

    #endregion
}
