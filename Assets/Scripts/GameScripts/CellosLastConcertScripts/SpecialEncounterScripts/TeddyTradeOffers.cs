using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

/// <summary>
/// Scriptable object that holds possible trade offers for <PERSON>'s trading encounter
/// </summary>
[CreateAssetMenu(fileName = "TeddyTradeOffers", menuName = "RibCageGames/SpecialEncounters/TeddyTradeOffers")]
public class TeddyTradeOffers : MonoScriptableObject
{
    [Header("Trade Offers Configuration")]
    [SerializeField] private List<TradeOffer> m_availableTradeOffers = new();
    
    /// <summary>
    /// Gets all available trade offers
    /// </summary>
    public IReadOnlyList<TradeOffer> AvailableTradeOffers => m_availableTradeOffers.AsReadOnly();
    
    
    /// <summary>
    /// Gets a random selection of trade offers
    /// </summary>
    /// <param name="count">Number of offers to select</param>
    /// <param name="playerService">Player service to check availability</param>
    /// <returns>List of available trade offers</returns>
    public List<TradeOffer> GetRandomTradeOffers(int count, PlayerService playerService)
    {
        // Filter offers based on what the player actually has
        List<TradeOffer> availableOffers = m_availableTradeOffers
            .Where(offer => offer.IsAvailable(playerService))
            .ToList();
        
        if (availableOffers.Count == 0)
        {
            Debug.LogWarning("TeddyTradeOffers: No available trade offers for current player state");
            return new List<TradeOffer>();
        }
        
        // Shuffle and take the requested count
        List<TradeOffer> shuffledOffers = availableOffers.OrderBy(x => UnityEngine.Random.value).ToList();
        return shuffledOffers.Take(count).ToList();
    }

    public override void Initialize() { }
}

/// <summary>
/// Represents a single trade offer with sacrifice and reward
/// </summary>
[Serializable]
public class TradeOffer
{
    [Header("Trade Description")]
    [SerializeField] private string m_tradeName = "Trade Offer";
    [SerializeField] [TextArea(2, 4)] private string m_tradeDescription = "Sacrifice something to gain something else";
    
    [Header("Sacrifice (What Player Loses)")]
    [SerializeField] private TradeItem m_sacrifice;
    
    [Header("Reward (What Player Gains)")]
    [SerializeField] private TradeItem m_reward;
    
    /// <summary>
    /// Gets the trade name
    /// </summary>
    public string TradeName => m_tradeName;
    
    /// <summary>
    /// Gets the trade description
    /// </summary>
    public string TradeDescription => m_tradeDescription;
    
    /// <summary>
    /// Gets the sacrifice item
    /// </summary>
    public TradeItem Sacrifice => m_sacrifice;
    
    /// <summary>
    /// Gets the reward item
    /// </summary>
    public TradeItem Reward => m_reward;
    
    /// <summary>
    /// Checks if this trade offer is available based on player's current state
    /// </summary>
    /// <param name="playerService">Player service to check against</param>
    /// <returns>True if the trade is available, false otherwise</returns>
    public bool IsAvailable(PlayerService playerService)
    {
        return m_sacrifice.CanSacrifice(playerService);
    }
    
    /// <summary>
    /// Executes the trade (removes sacrifice and adds reward)
    /// </summary>
    /// <param name="playerService">Player service to modify</param>
    public void ExecuteTrade(PlayerService playerService)
    {
        if (!IsAvailable(playerService))
        {
            Debug.LogWarning($"TeddyTradeOffer: Cannot execute trade '{m_tradeName}' - sacrifice not available");
            return;
        }
        
        // Execute sacrifice
        m_sacrifice.ExecuteSacrifice(playerService);
        
        // Apply reward
        m_reward.ApplyReward();
        
        Debug.Log($"TeddyTradeOffer: Executed trade '{m_tradeName}' - Sacrificed {m_sacrifice.GetDisplayText()} for {m_reward.GetDisplayText()}");
    }
}

/// <summary>
/// Represents an item that can be traded (sacrificed or rewarded)
/// </summary>
[Serializable]
public class TradeItem
{
    [Header("Item Configuration")]
    [SerializeField] private TradeItemType m_itemType;
    [SerializeField] private PowerUpType m_powerUpType; // Used for powerup-related trades
    [SerializeField] private Powerup m_sourcePowerup;
    [SerializeField] private int m_amount = 1; // Amount to sacrifice/reward
    [SerializeField] private string m_customDisplayText; // Optional custom text
    
    /// <summary>
    /// Gets the item type
    /// </summary>
    public TradeItemType ItemType => m_itemType;
    
    /// <summary>
    /// Gets the powerup type (if applicable)
    /// </summary>
    public PowerUpType PowerUpType => m_powerUpType;
    
    /// <summary>
    /// Gets the amount
    /// </summary>
    public int Amount => m_amount;
    
    /// <summary>
    /// Checks if this item can be sacrificed by the player
    /// </summary>
    /// <param name="playerService">Player service to check</param>
    /// <returns>True if can be sacrificed, false otherwise</returns>
    public bool CanSacrifice(PlayerService playerService)
    {
        switch (m_itemType)
        {
            case TradeItemType.PowerUpLevel:
                return playerService.GetPowerUpLevel(m_powerUpType) >= m_amount;
            
            case TradeItemType.MaxHPUpgrade:
                // Check if player has enough max HP upgrades to sacrifice
                return playerService.GetPowerUpLevel(PowerUpType.MaxHealth) > 0;
            
            case TradeItemType.StatUpgrade:
                return playerService.GetPowerUpLevel(m_powerUpType) >= m_amount;
            
            default:
                return false;
        }
    }
    
    /// <summary>
    /// Executes the sacrifice (removes from player)
    /// </summary>
    /// <param name="playerService">Player service to modify</param>
    public void ExecuteSacrifice(PlayerService playerService)
    {
        switch (m_itemType)
        {
            case TradeItemType.PowerUpLevel:
                playerService.ReducePowerupLevel(m_powerUpType);
                break;
            
            case TradeItemType.MaxHPUpgrade:
                playerService.ReducePowerupLevel(PowerUpType.MaxHealth);
                break;
        }
    }
    
    /// <summary>
    /// Applies the reward (adds to player)
    /// </summary>
    /// <param name="playerService">Player service to modify</param>
    public void ApplyReward()
    {
        switch (m_itemType)
        {
            case TradeItemType.PowerUpLevel:
                m_sourcePowerup.UpgradeBonusLevel();
                break;
            
            case TradeItemType.MaxHPUpgrade:
                // Increase max HP
                m_sourcePowerup.UpgradeBonusLevel();
                break;
            
            case TradeItemType.StatUpgrade:
                //TODO: maybe support more than 1 level for this
                m_sourcePowerup.UpgradeBonusLevel();
                // Add stat upgrade
                //playerService.AddBonusEffect(
                //    new StatUpgradeBonus(m_powerUpType), 
                //    null, 
                //    playerService.GetPowerUpLevel(m_powerUpType) + m_amount);
                break;
        }
    }
    
    /// <summary>
    /// Gets display text for this trade item
    /// </summary>
    /// <returns>Human-readable description</returns>
    public string GetDisplayText()
    {
        if (!string.IsNullOrEmpty(m_customDisplayText))
        {
            return m_customDisplayText;
        }
        
        string amountText = m_amount > 1 ? $"{m_amount}x " : "";
        
        switch (m_itemType)
        {
            case TradeItemType.PowerUpLevel:
                return $"{amountText}{m_powerUpType} Level{(m_amount > 1 ? "s" : "")}";
            
            case TradeItemType.MaxHPUpgrade:
                return $"{amountText}Max HP Upgrade{(m_amount > 1 ? "s" : "")}";
            
            case TradeItemType.StatUpgrade:
                return $"{amountText}{m_powerUpType} Stat Upgrade{(m_amount > 1 ? "s" : "")}";
            
            default:
                return "Unknown Item";
        }
    }
}

/// <summary>
/// Types of items that can be traded
/// </summary>
public enum TradeItemType
{
    PowerUpLevel,    // Levels of specific powerups
    MaxHPUpgrade,    // Max health upgrades
    StatUpgrade      // Stat upgrade bonuses
}
