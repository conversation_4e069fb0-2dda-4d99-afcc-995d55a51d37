using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

/// <summary>
/// Scriptable object that holds possible shop offers for shop encounters
/// </summary>
[CreateAssetMenu(fileName = "ShopOffers", menuName = "RibCageGames/SpecialEncounters/ShopOffers")]
public class ShopOffers : ScriptableObject
{
    [Header("Shop Offers Configuration")]
    [SerializeField] private List<ShopOffer> m_availableShopOffers = new List<ShopOffer>();
    
    /// <summary>
    /// Gets all available shop offers
    /// </summary>
    public IReadOnlyList<ShopOffer> AvailableShopOffers => m_availableShopOffers.AsReadOnly();
    
    /// <summary>
    /// Gets a random selection of shop offers
    /// </summary>
    /// <param name="count">Number of offers to select</param>
    /// <param name="inventoryService">Inventory service to check currency availability</param>
    /// <returns>List of shop offers</returns>
    public List<ShopOffer> GetRandomShopOffers(int count, InventoryService inventoryService)
    {
        // Get all available offers (we don't filter by affordability here, let player see what's available)
        List<ShopOffer> availableOffers = new List<ShopOffer>(m_availableShopOffers);
        
        if (availableOffers.Count == 0)
        {
            Debug.LogWarning("ShopOffers: No shop offers configured");
            return new List<ShopOffer>();
        }
        
        // Shuffle and take the requested count
        List<ShopOffer> shuffledOffers = availableOffers.OrderBy(x => UnityEngine.Random.value).ToList();
        return shuffledOffers.Take(count).ToList();
    }
    
    /// <summary>
    /// Gets offers that the player can afford
    /// </summary>
    /// <param name="inventoryService">Inventory service to check currency</param>
    /// <returns>List of affordable offers</returns>
    public List<ShopOffer> GetAffordableOffers(InventoryService inventoryService)
    {
        return m_availableShopOffers.Where(offer => offer.CanAfford(inventoryService)).ToList();
    }
}

/// <summary>
/// Represents a single shop offer with item and price
/// </summary>
[Serializable]
public class ShopOffer
{
    [Header("Offer Description")]
    [SerializeField] private string m_offerName = "Shop Item";
    [SerializeField] [TextArea(2, 4)] private string m_offerDescription = "A useful item for your journey";
    
    [Header("Item")]
    [SerializeField] private InventoryItemType m_itemType;
    [SerializeField] private int m_itemAmount = 1;
    [SerializeField] private bool m_isPermanentItem = false;
    
    [Header("Price")]
    [SerializeField] private CurrencyType m_currencyType;
    [SerializeField] private int m_price = 10;
    
    [Header("Stock")]
    [SerializeField] private int m_maxStock = 1; // -1 for unlimited
    [SerializeField] private bool m_restockOnNewRun = true;
    
    // Runtime stock tracking
    private int m_currentStock;
    private bool m_stockInitialized = false;
    
    /// <summary>
    /// Gets the offer name
    /// </summary>
    public string OfferName => m_offerName;
    
    /// <summary>
    /// Gets the offer description
    /// </summary>
    public string OfferDescription => m_offerDescription;
    
    /// <summary>
    /// Gets the item type
    /// </summary>
    public InventoryItemType ItemType => m_itemType;
    
    /// <summary>
    /// Gets the item amount
    /// </summary>
    public int ItemAmount => m_itemAmount;
    
    /// <summary>
    /// Gets whether this is a permanent item
    /// </summary>
    public bool IsPermanentItem => m_isPermanentItem;
    
    /// <summary>
    /// Gets the currency type required
    /// </summary>
    public CurrencyType CurrencyType => m_currencyType;
    
    /// <summary>
    /// Gets the price
    /// </summary>
    public int Price => m_price;
    
    /// <summary>
    /// Gets the current stock
    /// </summary>
    public int CurrentStock 
    { 
        get 
        {
            if (!m_stockInitialized)
            {
                InitializeStock();
            }
            return m_currentStock;
        }
    }
    
    /// <summary>
    /// Gets whether this offer is in stock
    /// </summary>
    public bool IsInStock => m_maxStock < 0 || CurrentStock > 0;
    
    /// <summary>
    /// Initializes the stock for this offer
    /// </summary>
    private void InitializeStock()
    {
        m_currentStock = m_maxStock;
        m_stockInitialized = true;
    }
    
    /// <summary>
    /// Checks if the player can afford this offer
    /// </summary>
    /// <param name="inventoryService">Inventory service to check currency</param>
    /// <returns>True if the player can afford this offer</returns>
    public bool CanAfford(InventoryService inventoryService)
    {
        return inventoryService.HasCurrency(m_currencyType, m_price) && IsInStock;
    }
    
    /// <summary>
    /// Executes the purchase (removes currency and adds item)
    /// </summary>
    /// <param name="inventoryService">Inventory service to modify</param>
    /// <returns>True if purchase was successful</returns>
    public bool ExecutePurchase(InventoryService inventoryService)
    {
        if (!CanAfford(inventoryService))
        {
            Debug.LogWarning($"ShopOffer: Cannot purchase '{m_offerName}' - insufficient currency or out of stock");
            return false;
        }
        
        // Remove currency
        if (!inventoryService.RemoveCurrency(m_currencyType, m_price))
        {
            Debug.LogError($"ShopOffer: Failed to remove currency for '{m_offerName}'");
            return false;
        }
        
        // Add item
        inventoryService.AddItem(m_itemType, m_itemAmount, m_isPermanentItem);
        
        // Reduce stock
        if (m_maxStock > 0)
        {
            m_currentStock--;
        }
        
        Debug.Log($"ShopOffer: Purchased '{m_offerName}' - {m_itemAmount}x {m_itemType} for {m_price} {m_currencyType}");
        return true;
    }
    
    /// <summary>
    /// Restocks this offer (if applicable)
    /// </summary>
    public void Restock()
    {
        if (m_restockOnNewRun)
        {
            m_currentStock = m_maxStock;
        }
    }
    
    /// <summary>
    /// Gets display text for this shop offer
    /// </summary>
    /// <returns>Human-readable description</returns>
    public string GetDisplayText()
    {
        string itemText = m_itemAmount > 1 ? $"{m_itemAmount}x " : "";
        itemText += InventoryHelper.GetItemDisplayName(m_itemType);
        
        string priceText = $"{m_price} {InventoryHelper.GetCurrencyDisplayName(m_currencyType)}";
        
        string stockText = "";
        if (m_maxStock > 0)
        {
            stockText = $" (Stock: {CurrentStock})";
        }
        
        return $"{itemText} - {priceText}{stockText}";
    }
    
    /// <summary>
    /// Gets the full offer description including price and stock
    /// </summary>
    /// <returns>Complete offer description</returns>
    public string GetFullDescription()
    {
        string description = m_offerDescription;
        
        if (!string.IsNullOrEmpty(description))
        {
            description += "\n\n";
        }
        
        description += GetDisplayText();
        
        if (!IsInStock)
        {
            description += "\n[OUT OF STOCK]";
        }
        
        return description;
    }
}
