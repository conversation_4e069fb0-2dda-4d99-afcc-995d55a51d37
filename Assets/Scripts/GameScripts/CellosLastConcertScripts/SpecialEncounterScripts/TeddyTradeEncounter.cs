using System;
using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using RibCageGames.Base;
using RibCageGames.UI;
using UnityEngine;
using Random = UnityEngine.Random;

public class TeddyTradeEncounter : SpecialSceneController
{
    [SerializeField] private OnTriggerEnterListener m_onTriggerEnterListener;
    [SerializeField] private NPCController m_teddyController;
    [SerializeField] private FocusCameraShot m_teddyCameraShot;

    [SerializeField] private float m_dialogDelay;
    [SerializeField] private List<DialogPopupSettings> m_appearanceDialogOptions;

    [Header("Trade Configuration")]
    [SerializeField] private TeddyTradeOffers m_tradeOffers;
    [SerializeField] private int m_numberOfTradeOptions = 3;
    
    private readonly ServiceReference<PopupService> m_popupService = new();
    private readonly ServiceReference<PlayerService> m_playerService = new();
    
    protected override async UniTask OnStartScene()
    {
        await m_onTriggerEnterListener.TriggerEnter.WaitForEventTrigger();

        m_teddyController.Animator.gameObject.SetActive(true);
        m_teddyCameraShot.ActivateFocusShot(m_teddyController.transform);
        m_teddyController.AnimatorParameterControls[0].ApplyParameter();

        await m_popupService.Value.ShowDialogWithDelayAsync(m_dialogDelay,
            m_appearanceDialogOptions[Random.Range(0, m_appearanceDialogOptions.Count)]);

        // Offer trade deals to the player
        OfferDeal();
    }
    
    public void OfferDeal()
    {
        if (m_tradeOffers == null)
        {
            Debug.LogError("TeddyTradeEncounter: No trade offers configured!");
            return;
        }

        if (m_popupService.Value.TryGetElement(out PowerupEvolutionSelectionScreen selectionScreen))
        {
            // Get random trade offers from the scriptable object
            List<TradeOffer> availableOffers = m_tradeOffers.GetRandomTradeOffers(m_numberOfTradeOptions, m_playerService.Value);

            if (availableOffers.Count == 0)
            {
                Debug.LogWarning("TeddyTradeEncounter: No available trade offers for current player state");
                return;
            }

            // Convert trade offers to powerup selection format
            List<(string, string, Action)> tradeOptions = new List<(string, string, Action)>();

            foreach (TradeOffer offer in availableOffers)
            {
                // Create a dummy powerup for display purposes
                //TradeDisplayPowerup displayPowerup = CreateTradeDisplayPowerup(offer);

                // Create the action that executes the trade
                Action tradeAction = () =>
                {
                    ExecuteTrade(offer);
                };

                tradeOptions.Add((offer.TradeName, offer.TradeDescription, tradeAction));
            }

            // Set the trade options in the selection screen
            selectionScreen.SetCustomOptions(tradeOptions);
        }
    }

    /// <summary>
    /// Executes a trade offer
    /// </summary>
    /// <param name="offer">The trade offer to execute</param>
    private void ExecuteTrade(TradeOffer offer)
    {
        if (offer.IsAvailable(m_playerService.Value))
        {
            offer.ExecuteTrade(m_playerService.Value);
            Debug.Log($"TeddyTradeEncounter: Trade completed - {offer.TradeName}");

            // Close the selection screen after trade
            if (m_popupService.Value.TryGetElement(out PowerupEvolutionSelectionScreen selectionScreen))
            {
                selectionScreen.gameObject.SetActive(false);
            }
        }
        else
        {
            Debug.LogWarning($"TeddyTradeEncounter: Cannot execute trade - {offer.TradeName}");
        }
        
        OnSpecialSceneCompleted?.Invoke();
    }
}
