using System;
using System.Collections.Generic;
using System.Linq;
using Cysharp.Threading.Tasks;
using RibCageGames.Base;
using RibCageGames.UI;
using UnityEngine;
using Random = UnityEngine.Random;

public class TeddyTradeEncounter : SpecialSceneController
{
    [SerializeField] private OnTriggerEnterListener m_onTriggerEnterListener;
    [SerializeField] private NPCController m_teddyController;
    [SerializeField] private FocusCameraShot m_teddyCameraShot;
    
    [SerializeField] private float m_dialogDelay;
    [SerializeField] private List<DialogPopupSettings> m_appearanceDialogOptions;
    
    private readonly ServiceReference<PopupService> m_popupService = new();
    private readonly ServiceReference<PlayerService> m_playerService = new();
    
    protected override async UniTask OnStartScene()
    {
        await m_onTriggerEnterListener.TriggerEnter.WaitForEventTrigger();
        
        m_teddyCameraShot.ActivateFocusShot(m_teddyController.transform);
        m_teddyController.AnimatorParameterControls[0].ApplyParameter();
        
        await m_popupService.Value.ShowDialogWithDelayAsync(m_dialogDelay,
            m_appearanceDialogOptions[Random.Range(0, m_appearanceDialogOptions.Count)]);
        
        
    }
    
    public void OfferDeal()
    {
        if (m_popupService.Value.TryGetElement(out PowerupEvolutionSelectionScreen selectionScreen))
        {
            List<Powerup> powerups = m_playerService.Value.PowerUpLevelDict.Values
                .Select(x => x.Item3)
                .Where(p => p.IsNamedPowerup)
                .ToList();
            
            //selectionScreen.SetPowerupOptions(
            //    powerups
            //        .Select<Powerup, (Powerup, Action)>(
            //            x => (x, () =>
            //            {
            //                switch (x.Type)
            //                {
            //                    //Add option to selection menu for custom texts use that to describe deal
            //                    //Have a list of deals, filter then take random two/three
            //                    case PowerUpType.Delay:
            //                        m_playerService.Value.AddBonusEffect(
            //                            new StatUpgradeBonus(PowerUpType.DelayUpgrade),
            //                            null, m_playerService.Value.GetPowerUpLevel(PowerUpType.DelayUpgrade) + 1);
            //                        break;
            //                    case PowerUpType.Flanger:
            //                        m_playerService.Value.AddBonusEffect(
            //                            new StatUpgradeBonus(PowerUpType.FlangerUpgrade),
            //                            null, m_playerService.Value.GetPowerUpLevel(PowerUpType.FlangerUpgrade) + 1);
            //                        break;
            //                    case PowerUpType.Reverb:
            //                        m_playerService.Value.AddBonusEffect(
            //                            new StatUpgradeBonus(PowerUpType.ReverbUpgrade),
            //                            null, m_playerService.Value.GetPowerUpLevel(PowerUpType.ReverbUpgrade) + 1);
            //                        break;
            //                }
            //                
            //            }))
            //        .ToList());
        }
    }
}
