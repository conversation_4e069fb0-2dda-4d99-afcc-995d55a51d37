using UnityEditor;
using UnityEngine;
using UnityEngine.SceneManagement;

namespace RibCageGames.Base
{
    public static class RuntimeBootStrap
    {
        private const string PlayCinematicsTogglePath = "RibCageGames/PlayCinematic";
        private const string AutoLoadCoreTogglePath = "RibCageGames/AutoLoadCore";
        
#if UNITY_EDITOR
        [MenuItem(PlayCinematicsTogglePath, priority = 1)]
        private static void CinematicSetting()
        {
            LevelManagementService levelManagementService = ServiceLocator.EditorGet<LevelManagementService>();
            levelManagementService.SetPlayCinematic(!levelManagementService.PlayCinematics);
        }

        [MenuItem(PlayCinematicsTogglePath, true)]
        private static bool CinematicSettingValidate() {
            LevelManagementService levelManagementService = ServiceLocator.EditorGet<LevelManagementService>();
            Menu.SetChecked(PlayCinematicsTogglePath, levelManagementService.PlayCinematics);
            return true;
        }
        
        [MenuItem(AutoLoadCoreTogglePath, priority = 2)]
        private static void AutoLoadCoreSetting()
        {
            LevelManagementService levelManagementService = ServiceLocator.EditorGet<LevelManagementService>();
            levelManagementService.SetAutoLoadCore(!levelManagementService.AutoLoadCore);
        }

        [MenuItem(AutoLoadCoreTogglePath, true)]
        private static bool AutoLoadCoreSettingValidate() {
            LevelManagementService levelManagementService = ServiceLocator.EditorGet<LevelManagementService>();
            Menu.SetChecked(AutoLoadCoreTogglePath, levelManagementService.AutoLoadCore);
            return true;
        }
#endif
        
        [RuntimeInitializeOnLoadMethod]
        public static void Init()
        {
            //ServiceLocator.InitializeServiceLocator();
#if UNITY_EDITOR

            if (!ServiceLocator.EditorGet<LevelManagementService>().AutoLoadCore)
            {
                return;
            }
            
            ServiceLocator.InitializeServiceLocator();

            Scene currentlyLoadedEditorScene = SceneManager.GetActiveScene();
            Debug.Log($"currentlyLoadedEditorScene {currentlyLoadedEditorScene.name}");
            
            if (currentlyLoadedEditorScene.buildIndex != 0)
            {
                SceneManager.LoadScene(0);
                ServiceLocator.Get<LevelManagementService>().SetInitialScene(currentlyLoadedEditorScene);
            }
#endif
        }
    }
}
