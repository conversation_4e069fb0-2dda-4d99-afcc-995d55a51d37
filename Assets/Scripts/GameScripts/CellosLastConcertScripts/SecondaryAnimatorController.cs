using System;
using System.Collections.Generic;
using RibCageGames.Animation;
using RibCageGames.Combat;
using RibCageGames.Input;
using UnityEngine;
using UnityEngine.Events;

public class SecondaryAnimatorController : MonoBehaviour
{
    [SerializeField] private CombatEntity m_entity;
    
    [SerializeField] private Animator m_animator;
    
    [SerializeField]
    private List<MoveSpecificParameterControlGroup> m_controlGroups;

    [SerializeField]
    private VisualEffectHolder holder;

    private Dictionary<ActionData, (List<AnimatorParameterControl>, UnityEvent)> m_moveAnimatorControlsDict;

    private void Start()
    {
        
        m_moveAnimatorControlsDict = new Dictionary<ActionData, (List<AnimatorParameterControl>, UnityEvent)>();

        foreach (MoveSpecificParameterControlGroup controlGroup in m_controlGroups)
        {
            foreach (ActionData appliedMove in controlGroup.AppliedMoves)
            {
                if (m_moveAnimatorControlsDict.ContainsKey(appliedMove))
                {
                    m_moveAnimatorControlsDict[appliedMove].Item1.AddRange(controlGroup.AnimatorParameters);
                }
                else
                {
                    m_moveAnimatorControlsDict.Add(appliedMove, (controlGroup.AnimatorParameters, controlGroup.SwitchEvent));   
                }
            }
            
            foreach (AnimatorParameterControl parameterControl in controlGroup.AnimatorParameters)
            {
                parameterControl.Initialize(m_animator);
            }
        }
        
        m_entity.OnMoveStarted.AddListener(EntityMoveStarted);
        m_entity.OnMoveEnded.AddListener(EntityMoveEnded);
        m_entity.OnMoveCancelled.AddListener(EntityMoveEnded);
    }

    private void EntityMoveStarted(ActionData action, PlayerInputActions initiatingAction)
    {
        if (m_moveAnimatorControlsDict.TryGetValue(action, out (List<AnimatorParameterControl>, UnityEvent) parameterControls))
        {
            parameterControls.Item2?.Invoke();
            foreach (AnimatorParameterControl control in parameterControls.Item1)
            {
                control.ApplyParameter();
            }
        }
    }
    
    private void EntityMoveEnded(ActionData action)
    {
        if (action == null) { return; }
        
        if (m_moveAnimatorControlsDict.TryGetValue(action, out (List<AnimatorParameterControl>, UnityEvent) parameterControls))
        {
            foreach (AnimatorParameterControl control in parameterControls.Item1)
            {
                if (control.ResetParameter)
                {
                    control.ResetParameterControl(m_animator);
                }
            }
        }
    }

    [Serializable]
    private class MoveSpecificParameterControlGroup
    {
        [SerializeField] private UnityEvent m_switchEvent;
        
        [SerializeField][AnimatorParameterControl("m_animator")]
        private List<AnimatorParameterControl> m_animatorParameters;
        
        [SerializeField] private List<ActionData> m_appliedMoves;
        
        public List<AnimatorParameterControl> AnimatorParameters => m_animatorParameters;
        public List<ActionData> AppliedMoves => m_appliedMoves;
        public UnityEvent SwitchEvent => m_switchEvent;
    }
}
