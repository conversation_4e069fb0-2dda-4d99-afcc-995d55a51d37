using System;
using Cysharp.Threading.Tasks;
using RibCageGames.Base;
using RibCageGames.Behaviour;
using RibCageGames.Combat;
using RibCageGames.MonoUtils;
using RibCageGames.UI;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.Serialization;

public class TutorialBeatGate : MonoBehaviour
{
    [Header("References")]
    [SerializeField] private NPCController m_blockingNPC;
    [SerializeField] private NPCController m_practiceEnemy;
    [SerializeField] private EncounterGate m_gate;

    [Header("Settings")]
    [SerializeField] private float m_runDelay;
    [SerializeField] private int m_onBeatAttacksToProceed;
    [SerializeField] private UnityEvent m_onActivated;
    
    [SerializeField] private float m_startingDialogDelay;
    [SerializeField] private DialogPopupSettings m_startingDialog;
    
    [SerializeField] private UnityEvent m_onBeatAttackLanded;
    [SerializeField] private UnityEvent m_offBeatAttackLanded;
    
    [SerializeField] private UnityEvent m_comboTutorialStarted;
    [SerializeField] private float m_comboDialogDelay;
    [SerializeField] private DialogPopupSettings m_comboDialog;
    [SerializeField] private ComboTutorialMoveList m_halfBeatComboList;
    
    
    [SerializeField] private float m_secondComboDialogDelay;
    [SerializeField] private DialogPopupSettings m_secondComboDialog;
    [SerializeField] private ComboTutorialMoveList m_fullBeatComboList;
    
    [SerializeField] private float m_secondComboCompleteDialogDelay;
    [SerializeField] private DialogPopupSettings m_secondComboCompleteDialog;
    
    [FormerlySerializedAs("m_comboCompleteDialogDelay")] [SerializeField] private float m_completeDialogDelay;
    [SerializeField] private DialogPopupSettings m_completeDialog;
    [SerializeField] private UnityEvent m_teddyLeaves;
    
    [SerializeField] private MonoTweenerSettings m_retaliationTween;
    [SerializeField] private float m_hitListenDeadzone;
    [SerializeField] private float m_encounterDelay;
    [SerializeField] private ParticleSystem m_enemyDeathParticles;
    
    [SerializeField] private UnityEvent m_barrierDeactivated;

    private readonly ServiceReference<PlayerService> m_playerService = new();
    private readonly ServiceReference<PopupService> m_popupService = new();
    private readonly ServiceReference<PointOfInterestService> m_pointOfInterestService = new();
    private int m_onBeatHits;
    private bool m_listeningToHits = true;
    private TutorialActionCount m_countText;
    
    private void Start()
    {
        m_blockingNPC.LogicHitbox.OnHit.AddListener(EnemyHit);
        m_blockingNPC.gameObject.SetActive(false);
    }

    public void Activate()
    {
        m_pointOfInterestService.Value.AddPoint(PointOfInterestTypes.Enemy, m_blockingNPC.transform);
        m_gate.Activate();
        m_blockingNPC.gameObject.SetActive(true);
        m_onActivated?.Invoke();
        m_popupService.Value.ShowDialogWithDelay(m_startingDialogDelay, m_startingDialog, StartOnBeatHits);
    }

    private void StartOnBeatHits()
    {
        m_popupService.Value.TryGetElement(out m_countText);
        m_countText.ActivateTextWithFormat($"Attacks on beat:", $"{{0}} / {{1}}", m_onBeatAttacksToProceed);
    }

    private void EnemyHit()
    {
        if (!m_listeningToHits)
        {
            return;
        }
        
        m_listeningToHits = false;
        MonoProcess listenProcess = MonoProcess.WaitForSecondsProcess(m_hitListenDeadzone).Do(() => { m_listeningToHits = true; });

        if (m_playerService.Value.PlayerController.CurrentMoveJust)
        {
            m_onBeatHits++;
            m_countText?.UpdateCount(m_onBeatHits, m_onBeatAttacksToProceed);
            
            if (m_onBeatHits >= m_onBeatAttacksToProceed)
            {
                m_countText.Deactivate();
                m_blockingNPC.ActivateAnimatorControl(3);
                m_listeningToHits = false;
                m_blockingNPC.LogicHitbox.OnHit.RemoveListener(EnemyHit);
                ActivateComboTutorial();
                listenProcess.Stop();
            }
            else
            {
                m_onBeatAttackLanded?.Invoke();
                m_blockingNPC.ActivateAnimatorControl(3); //Activate hit
            }
        }
    }

    private void ActivateComboTutorial()
    {
        m_pointOfInterestService.Value.RemovePoint(PointOfInterestTypes.Enemy, m_blockingNPC.transform);

        m_popupService.Value.DeactivateMetronome();
        m_playerService.Value.PlayerController.SetGracePeriod(true);

        m_popupService.Value.ShowDialogWithDelay(m_comboDialogDelay, m_comboDialog, async () =>
        {
            m_pointOfInterestService.Value.AddPoint(PointOfInterestTypes.Enemy, m_practiceEnemy.transform);

            m_comboTutorialStarted?.Invoke();

            await UniTask.WaitForSeconds(1f);

            m_practiceEnemy.gameObject.SetActive(true);
            m_practiceEnemy.ActivateAnimatorControl(2);
            m_enemyDeathParticles.Play();

            m_halfBeatComboList.Activate(FirstComboComplete);
        });
    }
    
    private void FirstComboComplete()
    {
        m_popupService.Value.ShowDialogWithDelay(m_secondComboDialogDelay, m_secondComboDialog, () =>
        {
            m_fullBeatComboList.Activate(SecondComboComplete); 
        });
    }
    
    private void SecondComboComplete()
    {
        m_practiceEnemy.ActivateAnimatorControl(1);
        m_practiceEnemy.Animator.applyRootMotion = true;
        m_practiceEnemy.LogicHitbox.Collider.enabled = false;
        MonoProcess.WaitForSecondsProcess(2f)
            .Do(() => m_enemyDeathParticles.Play())
            .WaitForSeconds(1f).Do(() =>
            {
                m_practiceEnemy.gameObject.SetActive(false);
            });
        
        m_popupService.Value.ShowDialogWithDelay(m_secondComboCompleteDialogDelay, m_secondComboCompleteDialog, DeactivateBarrier);
    }

    private void ApplyAttackToPlayer()
    {
        m_playerService.Value.PlayerController.TakeDamage(100f,
            m_playerService.Value.PlayerController,
            ActionData.ActionHitBox.DirectHitbox(HitSeverity.KnockbackHit,
                m_retaliationTween,
                false), out _);
    }

    private void DeactivateBarrier()
    {
        m_gate.Deactivate(false);

        m_blockingNPC.ActivateAnimatorControl(1); //Activate exit anim
        m_pointOfInterestService.Value.RemovePoint(PointOfInterestTypes.Enemy, m_practiceEnemy.transform);
        m_popupService.Value.ActivateMetronome();
        m_playerService.Value.PlayerController.SetGracePeriod(false);
        m_teddyLeaves?.Invoke();

        m_popupService.Value.ShowDialogWithDelay(m_completeDialogDelay, m_completeDialog, () =>
        {
            MonoProcess.WaitForSecondsProcess(m_encounterDelay).Do(() => m_barrierDeactivated?.Invoke());
            m_blockingNPC.gameObject.SetActive(false);
        });
    }
}
