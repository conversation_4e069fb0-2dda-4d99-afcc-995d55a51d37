using System.Collections;
using System.Collections.Generic;
using RibCageGames.Combat;
using UnityEngine;

public class TutorialNutCrackerBeatGate : NutcrackerEnemyController
{
    public override float TakeDamage(float damage, CombatEntity owner, ActionData.ActionHitBox hitData, out bool flinch)
    {
        flinch = false;
        for (int i = 0; i < m_hitParticleDamageThreshold.Count; i++)
        {
            if (damage > m_hitParticleDamageThreshold[i])
            {
                m_hitParticles[i].Play();
            }
        }
        OnDamageTaken?.Invoke(damage);
        return damage;
    }
}
