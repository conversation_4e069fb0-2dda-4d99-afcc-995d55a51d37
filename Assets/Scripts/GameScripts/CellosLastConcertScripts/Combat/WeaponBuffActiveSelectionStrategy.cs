using RibCageGames.Base;
using UnityEngine;

[CreateAssetMenu(fileName = "WeaponBuffActiveSelectionStrategy", menuName = "RibCageGames/AlphaNomos/WeaponBuffActiveSelectionStrategy")]
public class WeaponBuffActiveSelectionStrategy : PlayerSelectableActionDataStrategy
{
    public override int Select()
    {
        base.Select();
        return m_characterController.HasPowerup(PowerUpType.WeaponBuff) ? 1 : 0;
    }
}
