using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class ColliderBoundsVisualizer : MonoBehaviour
{
    [SerializeField] private Collider m_collider;

    private void OnDrawGizmos()
    {
        Bounds bounds = m_collider.bounds;
        
        Gizmos.color = Color.yellow; // Change color as desired
        Gizmos.DrawWireCube(bounds.center, bounds.size);
    }
}
