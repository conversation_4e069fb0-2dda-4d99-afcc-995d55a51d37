using BehaviorDesigner.Runtime;
using DG.Tweening;
using RibCageGames.Base;
using RibCageGames.Combat;
using UnityEngine;

public class NutcrackerEnemyController : BaseEnemyController {

    private static readonly int MouthOpened = Animator.StringToHash("MouthOpened");
    
    [SerializeField] private  GameObject m_nutGraphic;
    [SerializeField] private  GameObject m_nutFailParticleSystem;
    [SerializeField] private float m_nutStaggerDamageThreshold;
    
    //Add to stagger only if nut equipped
    protected override float CurrentFlinchAmount
    {
        set
        {
            if (value < m_currentFlinchAmount || m_nutEquipped)
            {
                m_currentFlinchAmount = value;
            }
        }
        get => m_currentFlinchAmount;
    }
    
    protected override float FlinchThreshold => m_nutEquipped ? m_nutStaggerDamageThreshold : m_currentFlinchThreshold;

    private SharedBool m_nutEquippedBehaviorVariable;
    
    private VisualEffectHolder m_nutFailEffect;
    
    private bool m_nutEquipped = false;

    public override void Initialize()
    {
        base.Initialize();
        m_nutFailEffect = new VisualEffectHolder(m_nutFailParticleSystem);
        if (m_behaviorTree != null)
        {
            m_nutEquippedBehaviorVariable = (SharedBool) m_behaviorTree.GetVariable("NutEquipped");
        }
    }

    public void ToggleNut(bool equip)
    {
        m_nutEquipped = equip;
        m_nutGraphic.SetActive(equip);
        CurrentFlinchAmount = 0f;
        if (m_behaviorTree != null)
        {
            m_nutEquippedBehaviorVariable.SetValue(equip);
        }

        m_animator.SetBool(MouthOpened, equip);
    }

    public void SetDamageTakenDuringCharge(float percentage)
    {
        m_currentFlinchAmount = percentage * m_nutStaggerDamageThreshold;
    }

    protected override void BreakStagger(CombatEntity attackOwner, ActionData.ActionHitBox hitData, bool moveable = true)
    {
        base.BreakStagger(attackOwner, hitData, false);
        ToggleNut(false);
        CurrentFlinchAmount = 0f;
        m_animator.SetInteger(Damage, 3);
        m_busy = true;
        m_nutFailEffect?.Play();
        m_hitProcess?.Stop();
        m_hitProcess = MonoProcess.WaitForSecondsProcess(m_hitSeverityStunDict[HitSeverity.KnockbackHit] - 1f)
            .Do(() =>
            {
                m_animator.SetInteger(Damage, 0);
            }).WaitForSeconds(1f).Do(() =>
            {
                m_busy = false;
                IsStunned = false;
                m_busyBehaviorVariable.Value = false;
                CurrentFlinchAmount = 0f;
                ResetFlinchThreshold();
                m_animator.SetBool(IsStunnedAnimatorParameter, false);
            });
    }

    protected override void Die(CombatEntity attackOwner, ActionData.ActionHitBox hitData)
    {
        base.Die(attackOwner, hitData);
        ToggleNut(false);
    }

    public override void ResetCombatEntity(bool resetHealth = true, bool resetMovement = true)
    {
        base.ResetCombatEntity(resetHealth, resetMovement);
        ToggleNut(false);
        ResetFlinchThreshold();
    }
}
