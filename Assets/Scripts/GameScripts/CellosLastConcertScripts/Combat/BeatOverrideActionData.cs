using System;
using System.Threading;
using RibCageGames.Base;
using RibCageGames.Combat;
using RibCageGames.Input;
using UnityEngine;

[CreateAssetMenu(fileName = "Action", menuName = "RibCageGames/CellosLastConcert/BeatOverrideActionData")]
public class BeatOverrideActionData : BeatBasedActionData
{
    [SerializeField] private BeatBasedActionData m_base;

    public override string DisplayName => 
        OnBeat? 
            (!string.IsNullOrWhiteSpace(m_displayName) ? r_displayName : m_base.DisplayName)
            : m_base.DisplayName;
    
    private bool OnBeat => m_beatSystem.Value.IsOnBeat;
    
    public override float FullSnapRadius => 
        m_base != null && (r_fullSnapRadius < 0.01f || !OnBeat)
            ? m_base.FullSnapRadius
            : r_fullSnapRadius;
    
    public override float LightSnapRadius => 
        m_base != null && (r_lightSnapRadius < 0.01f || !OnBeat)
            ? m_base.LightSnapRadius
            : r_lightSnapRadius;

    public override float LightSnapAngle =>
        m_base != null && (r_lightSnapRadius < 0.01f || !OnBeat)
            ? m_base.LightSnapAngle
            : r_lightSnapAngle;
    
    public override bool IgnoreHeight =>
        m_base != null && !OnBeat
            ? m_base.IgnoreHeight
            : r_ignoreHeight;
    
    public override float MinimalCancelTime =>
        m_base != null && (r_minimalCancelTime < 0.01f || !OnBeat)
            ? m_base.MinimalCancelTime
            : r_minimalCancelTime;
    
    public override float MinimalMoveTime => 
        m_base != null && (r_minimalMoveTime < 0.01f || !OnBeat)
            ? m_base.MinimalMoveTime
            : r_minimalMoveTime;
    
    
    
    public override bool Valid => m_beatSystem.Value.IsOnBeat || (m_base != null);
    
    public override int HitBoxCount => (m_hitboxes.Count + m_beatHitboxes.Count > 0) ? (m_hitboxes.Count + m_beatHitboxes.Count) : m_base.HitBoxCount;

    public override void PerformConsecutive(PerformParams performParams, bool isOnBeat, CancellationToken cancellationToken = default(CancellationToken), CancellationTokenSource cancelSource = null)
    {
        BeatPerformData performData = ConstructBeatPerformData();
        performData.isOnBeat = isOnBeat;
        performData.DirectHit = isOnBeat;
        performParams.initiatingPlayerAction = PlayerInputActions.Default;
        ConstructOverrideData(performData);
        Perform(performParams, performData, cancellationToken, cancelSource);
    }

    public override void Perform(PerformParams performParams, CancellationToken cancellationToken = default(CancellationToken), CancellationTokenSource cancelSource = null)
    {
        BeatPerformData performData = ConstructBeatPerformData();
        performData = ConstructOverrideData(performData);
        Perform(performParams, performData, cancellationToken, cancelSource);
    }

    protected virtual BeatPerformData ConstructOverrideData(BeatPerformData data)
    {
        if (m_base != null)
        {
            //TODO: cache values for on and offbeat maybe using r_values
            //this would allow not checking for fallback values at runtime
            if (!data.isOnBeat || r_moveDuration < 0.01f) { data.moveDuration = m_base.MoveDuration; }
            if (!data.isOnBeat || r_minimalMoveTime < 0.01f) { data.minimalMoveTime = m_base.MinimalMoveTime; }
            if (!data.isOnBeat || r_tweenMotionModifier.sqrMagnitude < 0.1f) { data.tweenMotionModifier = m_base.TweenMotionModifier; }
            if (!data.isOnBeat || r_animationMotionModifier.sqrMagnitude < 0.1f) { data.animationMotionModifier = m_base.AnimationMotionModifier; }
            if (!data.isOnBeat || r_moveTween == null) { data.moveTween = m_base.MoveTween; }
            if (!data.isOnBeat || r_moveDamage < 0.01f) { data.moveDamage = m_base.MoveDamage; }
            if (!data.isOnBeat || r_animatorParameters.Count < 1) { data.animatorParameters = m_base.AnimatorParameters; }
            if (!data.isOnBeat || r_hitboxes.Count < 1) { data.hitboxes = m_base.Hitboxes; }
            if (!data.isOnBeat || r_soundEffects.Count < 1) { data.soundEffects = m_base.SoundEffects; } 
            if (!data.isOnBeat || r_effects.Count < 1) { data.effects = m_base.Effects; }
            if (!data.isOnBeat || r_actionMoveEffects.Count < 1) { data.actionMoveEffects = m_base.ActionMoveEffects; } 
            if (!data.isOnBeat || r_followingActions.Count < 1) { data.followingActions = m_base.FollowingActions; } 
            if (!data.isOnBeat || r_beatHitboxes.Count < 1) { data.beatHitboxes = m_base.BeatHitboxes; }
            if (!data.isOnBeat || r_beatEffects.Count < 1) { data.beatEffects = m_base.BeatEffects; }
            if (!data.isOnBeat || r_beatSounds.Count < 1) { data.beatSounds = m_base.BeatSounds; }
            if (!data.isOnBeat || r_beatFollowingOptions.Count < 1) { data.beatFollowingOptions = m_base.BeatFollowingOptions; }
            if (!data.isOnBeat || r_fullSnapRadius < 0.01f) { data.fullSnapRadius = m_base.FullSnapRadius; }
            if (!data.isOnBeat || r_lightSnapRadius < 0.01f) { data.lightSnapRadius = m_base.LightSnapRadius; }
            if (!data.isOnBeat || r_lightSnapAngle < 0.01f) { data.lightSnapAngle = m_base.LightSnapAngle; }
        }
        return data;
    }


    [ContextMenu("CopyDurationFromBase")]
    public void CopyDurationFromBase()
    {
        m_moveDuration = m_base.MoveDuration;
    }
    
    [ContextMenu("CopyRootMotionModifierFromBase")]
    public void CopyRootMotionModifierFromBase()
    {
        m_rootMotionModifierVector = m_base.AnimationMotionModifier;
        m_tweenMotionModifierVector = m_base.TweenMotionModifier;
    }
    
    [ContextMenu("CopyTweenFromBase")]
    public void CopyTweenFromBase()
    {
        m_moveTween = m_base.MoveTween;
    }
    
    [ContextMenu("CopyAnimatorParametersFromBase")]
    public void CopyAnimatorParametersFromBase()
    {
        m_animatorParameters = m_base.OriginalAnimatorParameters.CopyFromByValue();
    }
    
    [ContextMenu("CopyHitboxesFromBase")]
    public void CopyHitboxesFromBase()
    {
        m_hitboxes = m_base.Hitboxes.CopyFromByValue();
    }

    [ContextMenu("CopyEffectsFromBase")]
    public void CopyEffectsFromBase()
    {
        m_effects = m_base.Effects.CopyFromByValue();
    }
    
    [ContextMenu("CopySoundsFromBase")]
    public void CopySoundEffectsFromBase()
    {
        m_soundEffects = m_base.SoundEffects.CopyFromByValue();
    }
    
    [ContextMenu("CopyFollowingActionsFromBase")]
    public void CopyFollowingActionsFromBase()
    {
        m_followingActions = m_base.OriginalFollowingActions.CopyFromByValue();
    }
    
    [ContextMenu("CopyBeatFollowingActionsFromBase")]
    public void CopyBeatFollowingActionsFromBase()
    {
        m_beatFollowingOptions = m_base.OriginalBeatFollowingOptions.CopyFromByValue();
    }
    
    [ContextMenu("MatchAllWithSource")]
    public void MatchAllVariablesWithBase()
    {
        m_base.SetValuesFrom(this);
    }
    
    protected override void OnValidate()
    {
        base.OnValidate();
        if (name.Contains("OwlClaw") && name.Contains("Just"))
        {
            MatchAllVariablesWithBase();
        }
    }

#if UNITY_EDITOR
    public override GameObject Owner
    {
        set
        {
            SetOwner(value);
            if (m_base != null) { m_base.Owner = value; }
        }
    }
    #endif
}
