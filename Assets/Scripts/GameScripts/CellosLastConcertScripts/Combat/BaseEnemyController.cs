using DG.Tweening;
using RibCageGames.Base;
using RibCageGames.Behaviour;
using RibCageGames.Combat;
using RibCageGames.Editor;
using System;
using System.Collections.Generic;
using System.Threading;
using BehaviorDesigner.Runtime;
using RibCageGames.Input;
using RibCageGames.MonoUtils;
using UnityEngine;
using UnityEngine.Serialization;
using Random = UnityEngine.Random;

public class BaseEnemyController : BeatCombatEntity
{
    [NonSerialized] protected static readonly int IsStunnedAnimatorParameter = Animator.StringToHash("isStunned");
    [NonSerialized] protected static readonly int Dead = Animator.StringToHash("isDead");
    [NonSerialized] protected static readonly int Damage = Animator.StringToHash("damage");
    [NonSerialized] protected static readonly int HitReceived = Animator.StringToHash("hitReceived");
    [NonSerialized] protected static readonly int IsInAir = Animator.StringToHash("isInAir");

    [Header("Behaviour")] [SerializeField] protected BehaviorTree m_behaviorTree;
    [SerializeField] protected EnemyHealthBar m_healthBar;
    [SerializeField] private ActionData m_spawnAction;
    [SerializeField][Range(0.001f, 1f)] protected float m_friendlyFireDamageModifier = 0.5f;
    [SerializeField][Range(0.001f, 1f)] private float m_friendlyFireDamageCap = 0.5f;
    [SerializeField] protected float m_flowStateDamageMultiplier;
    
    [Header("Movement")]
    [SerializeField] private float m_movementSpeed;
    [SerializeField] private float m_rotationSpeed;
    [SerializeField] private float m_dropSpeed = -10f;
    
    [Header("Damage")]
    [SerializeField] protected List<ParticleSystem> m_hitParticles;
    [SerializeField] protected List<int> m_hitParticleDamageThreshold;
    [ArrayElementTitle("m_severity")]
    [SerializeField] private HitSeverityReaction[] m_hitSeverityReactions;
    
    [Header("Death Process")]
    [SerializeField] protected float m_deathLogicDelay;    
    [SerializeField] protected float m_deathParticleDelay;
    [SerializeField] protected ParticleSystem m_deathParticles;
    [SerializeField] protected float m_deathRendererDisableDelay;
    [SerializeField] protected SkinnedMeshRenderer m_renderer;
    [SerializeField] protected float m_fullDisableDelay;

    [NonSerialized] protected PointOfInterestService m_pointOfInterestService;
    [NonSerialized] protected Dictionary<HitSeverity, float> m_hitSeverityStunDict;
    [NonSerialized] protected Action m_currentBehaviourTreeCancel;
    [NonSerialized] private bool m_moveInProgress = false;
    [NonSerialized] protected Vector3 m_movementDirection;
    [NonSerialized] protected float m_verticalSpeed;
    [NonSerialized] private PlayerService m_playerService;
    [NonSerialized] protected float m_currentFlinchThreshold;
    [NonSerialized] protected float m_currentFlinchAmount;
    [NonSerialized] protected MonoProcess m_flinchAmountProcess;
    [NonSerialized] private SharedFloat m_selectionValueBehaviorVariable;
    [NonSerialized] private SharedFloat m_playerDistanceBehaviorVariable;
    [NonSerialized] private SharedFloat m_playerVerticalDistanceBehaviorVariable;
    [NonSerialized] private SharedFloat m_healthPercentageBehaviorVariable;
    [NonSerialized] protected SharedBool m_attackAllowedBehaviorVariable;
    [NonSerialized] protected SharedBool m_busyBehaviorVariable;
    [NonSerialized] private SharedBool m_friendlyFireBehaviorVariable;
    [NonSerialized] protected SharedBool m_flowStateBehaviorVariable;
    [NonSerialized] private float r_movementSpeed;
    [NonSerialized] private Vector3[] m_environmentObstacleNodes = new Vector3[5];
    [NonSerialized] private bool m_attackingAllowed;
    
    public bool AttackingAllowed
    {
        get => m_attackingAllowed;
        set
        {
            m_attackingAllowed = value;
            m_attackAllowedBehaviorVariable.Value = value;
        }
    }

    protected override float CurrentFlinchAmount
    {
        set
        {
            float oldStaggerAmount = m_currentFlinchAmount;
            m_currentFlinchAmount = value;
            m_flinchAmountProcess?.Stop();
            if (value > 0f)
            {
                float delta = 0f;
                m_flinchAmountProcess =
                    MonoProcess
                        .WaitForSecondsProcess(m_flinchRestorationDelay)
                        .WaitFor(elapsed =>
                        {
                            m_currentFlinchAmount -= (elapsed - delta) * m_flinchRestorationRate;
                            delta = elapsed;
                            return m_currentFlinchAmount < 0f;
                        }, 3f)
                        .Do(() =>
                        {
                            m_currentFlinchAmount = 0f;
                        });
            }
            else
            {
                m_currentFlinchAmount = 0f;
            }
        }
        get => m_currentFlinchAmount;
    }
    
    [NonSerialized] protected bool m_stagerBroken = false;
    
    protected override float FlinchThreshold => m_stagerBroken ? 1f : m_currentFlinchThreshold;
    public override bool IsPlayer => false;
    public Vector3 SelfToPlayerVector => m_playerService.PlayerController.transform.position - transform.position;
    public float PlayerDistance => SelfToPlayerVector.magnitudeXZ();
    public float PlayerVerticalDistance => SelfToPlayerVector.y;
    
    public bool IsBusy => m_busy || IsDead;
    public Behaviour This => this;

    private NavMeshDeltaStrategy m_navMeshStrategy;
    
    #region Mono

    public override void Initialize()
    {
        if (m_initialized) { return; }
        
        base.Initialize();

        m_pointOfInterestService = ServiceLocator.Get<PointOfInterestService>();
        ServiceLocator.Get<CameraService>();
        m_playerService = ServiceLocator.Get<PlayerService>();
        m_navMeshStrategy = (NavMeshDeltaStrategy) CompositeMovementStrategy.DeltaStrategy;

        m_hitSeverityStunDict = new Dictionary<HitSeverity, float>();

        foreach (HitSeverityReaction hitSeverity in m_hitSeverityReactions)
        {
            m_hitSeverityStunDict.Add(hitSeverity.Severity, hitSeverity.StunDuration);
        }

        m_monoService.OnFixedUpdate.AddListener(ApplyMovement);
        //OnDead.AddListener((entity) => OnDisable.Invoke());
        
        if (m_behaviorTree != null)
        {
            m_selectionValueBehaviorVariable = (SharedFloat) m_behaviorTree.GetVariable("SelectionValue");
            m_playerDistanceBehaviorVariable = (SharedFloat) m_behaviorTree.GetVariable("PlayerDistance");
            m_playerVerticalDistanceBehaviorVariable = (SharedFloat) m_behaviorTree.GetVariable("PlayerVerticalDistance");
            m_healthPercentageBehaviorVariable = (SharedFloat) m_behaviorTree.GetVariable("HealthPercentage");
            m_busyBehaviorVariable = (SharedBool) m_behaviorTree.GetVariable("Busy");
            m_attackAllowedBehaviorVariable = (SharedBool) m_behaviorTree.GetVariable("AttackAllowed");
            m_friendlyFireBehaviorVariable = (SharedBool) m_behaviorTree.GetVariable("FriendlyHit");
            m_flowStateBehaviorVariable = (SharedBool) m_behaviorTree.GetVariable("FlowState");
        }

        if (m_friendlyFireBehaviorVariable != null)
        {
            OnAttackLanded.AddListener((action, targetsHit) =>
            {
                if (targetsHit != null && targetsHit.Count > 0)
                {
                    bool friendlyFire = true;
                    foreach ((DamageableRegistry.IDamageable damageable, float damage) target in targetsHit)
                    {
                        friendlyFire = friendlyFire && !((CombatEntity)target.damageable).IsPlayer;
                    }

                    m_friendlyFireBehaviorVariable.Value = friendlyFire;
                }
            });
        }

        if (m_healthBar != null)
        {
            OnDamageTaken.AddListener(OnHealthChanged);   
        }
    }

    private float m_previousHealthValue; 
    private void OnHealthChanged(float damage)
    {
        m_healthBar.OnHealthChanged(m_previousHealthValue, CurrentHealth, m_maxHealth);
        m_previousHealthValue = CurrentHealth;
    }

    public override void MoveStarted(ActionData actionData, Action cancelMoveSource,
        PlayerInputActions initiatingAction)
    {
        base.MoveStarted(actionData, cancelMoveSource, initiatingAction);
        if (m_friendlyFireBehaviorVariable != null)
        {
            m_friendlyFireBehaviorVariable.Value = false;
        }
    }

    protected override void Start()
    {
        base.Start();
        Initialize();
    }

    protected override void ControlledLateUpdate(float deltaTime)
    {
        base.ControlledLateUpdate(deltaTime);
        m_animator.SetBool(IsInAir, InAir);
    }

    protected override void ControlledUpdate(float deltaTime)
    {
        base.ControlledUpdate(deltaTime);

        if (gameObject.activeInHierarchy && m_behaviorTree != null)
        {
            m_playerDistanceBehaviorVariable.Value = PlayerDistance;
            m_playerVerticalDistanceBehaviorVariable.Value = PlayerVerticalDistance;
            
            //TODO: is traversing navmeshlinks needed?
            //m_navMeshGuide.autoTraverseOffMeshLink = m_navigating || PlayerVerticalDistance > 0.4f;

            //TODO: put these not in update
            m_healthPercentageBehaviorVariable.Value = HealthPercentage;
            m_busyBehaviorVariable.Value = IsBusy;
            
            //Doing this in regular update makes specifically ranged scrubs be in air every other frame, not sure why but position is set to about 0.2 or move above guide
            /*
            m_animator.SetBool(IsInAir, InAir);
            */
        }
    }

    protected override void OnDestroy()
    {
        m_currentBehaviourTreeCancel?.Invoke();
        m_currentBehaviourTreeCancel = null;
        m_hitTween?.Kill();
        m_hitProcess?.Stop();

        if (m_pointOfInterestService != null)
        {
            m_pointOfInterestService.RemovePoint(PointOfInterestTypes.Enemy, this.transform);
            m_pointOfInterestService.RemovePoint(PointOfInterestTypes.Other, this.transform);
        }

        base.OnDestroy();
    }
    #endregion

    protected override void SetGrounded(bool grounded, Collider collider = null)
    {
        base.SetGrounded(grounded, collider);
        m_animator.SetBool(IsInAir, grounded);
    }

    [SerializeField] private bool resetHP;
#if UNITY_EDITOR
    protected override void OnValidate()
    {
        base.OnValidate();
        if (resetHP)
        {
            resetHP = false;
            CurrentHealth = m_maxHealth;
        }
    }
#endif
    public override float TakeDamage(float damage, CombatEntity owner, ActionData.ActionHitBox hitData, out bool flinch)
    {
        if (owner != null && !owner.IsPlayer)
        {
            damage *= m_friendlyFireDamageModifier;
        }

        CurrentFlinchAmount += damage;
        
        float damageDealt = base.TakeDamage(damage, owner, hitData, out flinch); 
        
        if (damageDealt <= 0f)
        {
            return damageDealt;
        }

        for (int i = 0; i < m_hitParticleDamageThreshold.Count; i++)
        {
            if (damage > m_hitParticleDamageThreshold[i])
            {
                m_hitParticles[i].Play();
            }
        }
        
        //Heal if damage is friendly and exceeds friendly damage cap
        if (owner != null && !owner.IsPlayer && m_friendlyFireDamageCap * m_maxHealth > m_currentHealth)
        {
            CurrentHealth += (int) damage;
        }

        if (!flinch)
        {
            return damage;
        }
        
        HitSeverity hitIntensity = InAir ? HitSeverity.LaunchHit : hitData.HitSeverity;
        
        m_busy = true;
        IsStunned = true;
        m_busyBehaviorVariable.Value = true;
        m_currentBehaviourTreeCancel?.Invoke();
        m_currentBehaviourTreeCancel = null;
        m_currentMoveCancel?.Invoke();
        m_animator.SetInteger(Damage, (int) hitIntensity);
        m_hitProcess?.Stop();
        m_hitProcess = MonoProcess.New();
        m_animationMotionModifier = Vector3.one;
        m_moveTweenMotionModifier = Vector3.one;
        m_verticalSpeed = 0f;
        SetDirection(Vector3.zero);
        SetSpeed(0f);
        
        if (hitData.FaceDamageSource) { FaceDirection(-owner.OwnForward); }
        
        if (m_friendlyFireBehaviorVariable != null)
        {
            m_friendlyFireBehaviorVariable.Value = false;
        }
        
        if (CurrentHealth <= 0 && !m_dead) //Die, only once
        {
            Die(owner, hitData);
        }
        //else
        //{
            BreakStagger(owner, hitData);
        //}
        
        return damageDealt;
    }

    protected virtual void BreakStagger(CombatEntity attackOwner, ActionData.ActionHitBox hitData, bool moveable = true)
    {
        
        //Disruptive hit
        HitSeverity hitIntensity = InAir ? HitSeverity.LaunchHit : hitData.HitSeverity;
        CurrentFlinchAmount = 0f;

        if (!m_stagerBroken)
        {
            m_staggerBrokenProcess?.Stop();
            m_stagerBroken = true;
            m_staggerBrokenProcess = MonoProcess.New();
            
            if (hitIntensity == HitSeverity.LaunchHit)
            {
                m_staggerBrokenProcess.WaitForSeconds(0.5f);
            }
                    
            m_staggerBrokenProcess    
                .WaitUntil(() => !InAir)
                .WaitForSeconds(m_flinchResetDelay)
                .Do(() =>
                {
                    m_stagerBroken = false;
                    CurrentFlinchAmount = 0f;
                });
        }
        
        m_animator.SetBool(IsStunnedAnimatorParameter, true);
        m_animator.SetTrigger(HitReceived);
        
        if (hitIntensity == HitSeverity.LaunchHit && Grounded)
        {
            m_animator.SetBool(IsInAir, true);
            m_hitProcess.WaitForSeconds(1f);
        }
        //m_navMeshGuide.autoTraverseOffMeshLink = false;
        m_navMeshStrategy.Navigating = false;

        m_hitProcess
            .WaitUntil(() => !InAir)
            .Do(() => m_animator.SetBool(IsInAir, InAir))
            .WaitForSeconds(m_hitSeverityStunDict[hitIntensity])
            .Do(() =>
            {
                m_busy = false;
                IsStunned = false;
                m_busyBehaviorVariable.Value = false;
                ResetFlinchThreshold();
                m_animator.SetBool(IsStunnedAnimatorParameter, false);
            });

        if (moveable && hitData.HitMoveEffect != null && (hitData.HitMoveEffect.EffectsGrounded || InAir))
        {
            m_hitTween?.Kill();
            if (attackOwner != null)
            {
                m_hitTween = hitData.HitMoveEffect.RunDirected(m_animator.transform.parent, attackOwner.OwnForward, null,
                    attackOwner.ConnectedTransform);
            }
        }
    }
    
    protected virtual void Die(CombatEntity attackOwner, ActionData.ActionHitBox hitData)
    {
        m_dead = true;
        //Disruptive hit
        HitSeverity hitIntensity = InAir ? HitSeverity.LaunchHit : hitData.HitSeverity;
        CurrentFlinchAmount = 0f;
        
        m_animator.SetBool(IsStunnedAnimatorParameter, true);
        m_animator.SetTrigger(HitReceived);


        if (attackOwner != null)
        {
            FaceDirection(-attackOwner.OwnForward);
        }

        m_animator.SetBool(Dead, true);

        m_animator.SetInteger(Damage,
            (int) (hitIntensity == HitSeverity.SmashHit ? HitSeverity.SmashHit : HitSeverity.KnockbackHit));
            
        m_pointOfInterestService.RemovePoint(PointOfInterestTypes.Enemy, this.transform);
        m_pointOfInterestService.RemovePoint(PointOfInterestTypes.Other, this.transform);

        //visuals
        MonoProcess.New()
            .WaitFor(() => !InAir, 3f)
            .WaitForSeconds(m_deathParticleDelay)
            .Do(m_deathParticles.Play)
            .WaitForSeconds(m_deathRendererDisableDelay)
            .Do(() => { m_renderer.enabled = false; })
            .WaitForSeconds(m_fullDisableDelay)
            .Do(DeactivatePoolable) //Necessary for preloaded enemies
            .Do(OnDisable.Invoke);
        //logic
        MonoProcess.New()
            .WaitFor(() => !InAir, 3f)
            .WaitForSeconds(m_deathLogicDelay)
            .Do(() => OnDead?.Invoke(this));
    }

    public override void ResetCombatEntity(bool resetHealth = true, bool resetMovement = true)
    {
        base.ResetCombatEntity(resetHealth, resetMovement);

        m_currentBehaviourTreeCancel?.Invoke();
        m_currentBehaviourTreeCancel = null;
        m_currentFlinchThreshold = m_flinchThreshold;
        m_stagerBroken = false;
        m_dead = false;
        CurrentFlinchAmount = 0f;
        m_baseDamageMultiplier = 1f;
        SetDirection(Vector3.zero);
        SetSpeed(0f);

        if (resetHealth && m_healthBar != null)
        {
            m_previousHealthValue = m_maxHealth;
            m_healthBar.ResetHealthBar();
        }
        
        if (resetMovement)
        {
            RepositionEntity(transform.position, transform.rotation);
        }
        
        m_hitTween?.Kill();
        m_hitProcess?.Stop();
        m_animator.SetBool(IsStunnedAnimatorParameter, false);
        m_animator.ResetTrigger(HitReceived);
        m_animator.SetBool(Dead, false);
        m_animator.SetInteger(Damage, 0);
        m_deathParticles.Stop();
        m_renderer.enabled = true;
    }

    public override void ActivatePoolable()
    {
        base.ActivatePoolable();
        
        m_floorCheck.CheckOverlap(m_hardAbstraction);
        m_remoteFloorCheck.CheckOverlap(m_hardAbstraction);
        
        m_pointOfInterestService.OnPointRemoved.AddListener(AddDynamicObstacle);
        m_pointOfInterestService.OnPointAdded.AddListener(RemoveDynamicObstacle);
        m_pointOfInterestService.OnBulkChange.AddListener(AddObstaclesBulk);
        
        m_staticObstaclePositions.Clear();
        m_dynamicObstaclePositions.Clear();
        AddObstaclesBulk(PointOfInterestTypes.Enemy,
            m_pointOfInterestService.GetPointsOfType(PointOfInterestTypes.Enemy));
        AddObstaclesBulk(PointOfInterestTypes.Terrain,
            m_pointOfInterestService.GetPointsOfType(PointOfInterestTypes.Terrain));
    }

    #region Movement
    
    private void ApplyMovement(float deltaTime) {
        
        //Wall bug protection
        if (transform.position.y > 100f || transform.position.y < -100f)
        {
            Vector3 correctedPosition = transform.position;
            RepositionEntity(correctedPosition, transform.rotation);
            if (!m_dead)
            {
                TakeDamage(999f,
                    null,
                    ActionData.ActionHitBox.DirectHitbox(HitSeverity.KnockbackHit, null, false), out _);
            }
        }

        if (m_navMeshStrategy.Navigating)
        {
            ConcurrentMovement = new Vector3(0f, m_verticalSpeed, 0f);
            return;
        }
        
        if (InAir && (m_hitTween == null || !m_hitTween.active) && !(NonConcurrentMovement.y > 0f))
        {
            m_verticalSpeed += deltaTime * m_dropSpeed;
        }
        else
        {
            m_verticalSpeed = 0f;
        }
        
        if (m_busy)
        {
            ConcurrentMovement = new Vector3(0f, m_verticalSpeed, 0f);
            return;
        }
        
        Vector3 movement = Vector3.zero;
        
        if (m_movementDirection != Vector3.zero) {
            m_movementDirection.y = 0;
            movement = m_movementDirection.normalized * r_movementSpeed;
        }
        
        movement.y = m_verticalSpeed;

        ConcurrentMovement = movement;
        
        FaceMovement(deltaTime);
    }
    
    private void FaceMovement(float deltaTime)
    {
        FaceDirection(m_movementDirection, deltaTime);
    }

    public override void FaceDirection(Vector3 direction, float delta)
    {
        direction.y = 0f;
        if (direction.sqrMagnitude > 0.1f){
            direction = direction.normalized;
            transform.forward = Vector3.Slerp(transform.forward, direction, m_rotationSpeed * delta);
            m_navMeshStrategy.Forward = direction;
        }
    }

    #endregion

    #region Behaviour
    
    private bool m_engaged = false;

    public void Engage(bool doSpawnAction = true)
    {
        m_engaged = true;
        if (m_pointOfInterestService)
        {
            m_pointOfInterestService.AddPoint(PointOfInterestTypes.Enemy, this.transform);
        }
        
        MonoProcess.New().Do(() =>
        {
            if (doSpawnAction && m_spawnAction != null)
            {
                PerformCombatAction(m_spawnAction, () =>
                {
                    if (m_behaviorTree != null)
                    {
                        m_behaviorTree.enabled = true;
                        m_behaviorTree.EnableBehavior();
                    }
                });
                MonoProcess.New().WaitForSeconds(m_spawnAction.MoveDuration).Do(() =>
                {
                    if (m_behaviorTree != null)
                    {
                        m_behaviorTree.enabled = true;
                        m_behaviorTree.EnableBehavior();
                    }
                });
            }
            else
            {
                if (m_behaviorTree != null)
                {
                    m_behaviorTree.enabled = true;
                    m_behaviorTree.EnableBehavior();
                }
            }
        });
    }
    
    public void Disengage()
    {
        m_engaged = false;
        if(m_pointOfInterestService) m_pointOfInterestService.RemovePoint(PointOfInterestTypes.Enemy, this.transform);

        if (m_behaviorTree != null)
        {
            m_behaviorTree.enabled = false;
        }
    }
    
    public void PerformAction(ActionData actionData)
    {
        PerformAction(actionData, null);
    }

    protected override CancellationTokenSource PerformAction(ActionData actionData, Action onComplete,
        PlayerInputActions initiatingAction = PlayerInputActions.Default)
    {
        return base.PerformAction(actionData, () =>
        {
            onComplete?.Invoke();
        }, initiatingAction);
    }

    public override void DeactivatePoolable()
    {
        m_currentBehaviourTreeCancel?.Invoke();
        m_currentBehaviourTreeCancel = null;
        m_hitTween?.Kill();
        m_hitProcess?.Stop();

        if (m_behaviorTree != null)
        {
            m_behaviorTree.enabled = false;
            m_behaviorTree.DisableBehavior();
        }

        m_pointOfInterestService.OnPointRemoved.RemoveListener(AddDynamicObstacle);
        m_pointOfInterestService.OnPointAdded.RemoveListener(RemoveDynamicObstacle);
        m_pointOfInterestService.OnBulkChange.RemoveListener(AddObstaclesBulk);
        
        base.DeactivatePoolable();
    }

    [SerializeField] private float m_debugSelectionValue; 
    
    public virtual void SetSelectionValue()
    {
        if (m_debugSelectionValue > 0f)
        {
            m_selectionValueBehaviorVariable.Value = m_debugSelectionValue;
        }
        else
        {
            float val = Random.value;
            m_selectionValueBehaviorVariable.Value = val;
        }
    }
    
    public virtual void SetFlowState(bool value)
    {
        if (m_flowStateBehaviorVariable != null)
        {
            m_flowStateBehaviorVariable.Value = value;
            m_baseDamageMultiplier = value ? m_flowStateDamageMultiplier : 1f;   
        }
    }

    public void SetSpeed(float speed)
    {
        r_movementSpeed = speed;
        m_navMeshStrategy.Speed = speed;
    }
    
    public void SetTurnSpeed(float speed)
    {
        m_rotationSpeed = speed;
    }
    
    public void SetDirection(Vector3 direction)
    {
        m_movementDirection = direction.normalized;
    }

    public void NavigateTo(Vector3 position)
    {
        NavigateTo(position, m_movementSpeed);
    }

    public bool IsNavigating => !m_navMeshStrategy.NavMeshGuide.isStopped;
    
    public Vector3 NavigationHeading => m_navMeshStrategy.Forward;
    
    public void StopNavigating()
    {
        m_navMeshStrategy.NavMeshGuide.isStopped = true;
        m_navMeshStrategy.NavMeshGuide.autoTraverseOffMeshLink = false;
        m_navMeshStrategy.Navigating = false;
    }

    public void NavigateTo(Vector3 position, float speed)
    {
        FaceDirection(position - transform.position);

        SetSpeed(speed);

        Vector3 targetPosition = position;

        m_navMeshStrategy.NavMeshGuide.autoTraverseOffMeshLink = true;
        m_navMeshStrategy.Navigating = true;
        m_navMeshStrategy.NavMeshGuide.SetDestination(targetPosition);
    }
    
    public void SetFlinchThreshold(float threshold)
    {
        m_currentFlinchThreshold = Mathf.Max(m_currentFlinchThreshold, threshold);
    }
    
    public void ResetFlinchThreshold()
    {
        m_currentFlinchThreshold = m_flinchThreshold;
    }

    #endregion

    public override Transform GetCombatTarget(ActionData actionData)
    {
        return m_playerService.PlayerController.transform;
    }

    #region debug
    
    private readonly List<Transform> m_staticObstaclePositions = new List<Transform>();
    private readonly List<Transform> m_dynamicObstaclePositions = new List<Transform>();

    [FormerlySerializedAs("targetCurve")] [SerializeField] private AnimationCurve m_targetMovementCurve;
    
    [FormerlySerializedAs("obstacleCurve")] [SerializeField] private AnimationCurve m_staticObstacleAvoidanceCurve;
    
    [SerializeField] private AnimationCurve m_dynamicObstacleAvoidanceCurve;
    
    [FormerlySerializedAs("angleResolution")] [SerializeField] private int m_movementAngleResolution;
    
    public Vector3 CalculateMovementDirection(Vector3 direction, Vector3 heading, float? minimalValidValue, AnimationCurve targetCurve = null) 
    {
        Vector3 position = transform.position;
        
        return CalculateMovementDirection(
            position,
            direction + position,
            heading,
            minimalValidValue ?? 0f,
            targetCurve ?? m_targetMovementCurve,
            m_dynamicObstaclePositions,
            m_dynamicObstacleAvoidanceCurve,
            m_staticObstaclePositions,
            m_staticObstacleAvoidanceCurve,
            m_movementAngleResolution);
    }
    
    private void AddObstaclesBulk(PointOfInterestTypes type, List<Transform> transforms)
    {
        if (type == PointOfInterestTypes.Enemy)
        {
            if (transforms == null) {
                return;
            }

            foreach (Transform pointTransform in transforms)
            {
                if (transform != this.transform)
                {
                    m_dynamicObstaclePositions.Add(pointTransform);
                }
            }
        }
        else
        {
            if (transforms == null) {
                return;
            }
            
            foreach (Transform pointTransform in transforms)
            {
                m_staticObstaclePositions.Add(pointTransform);
            }
        }
    }
    
    private void AddDynamicObstacle(PointOfInterestTypes type, Transform transform)
    {
        if (transform != this.transform)
        {
            m_dynamicObstaclePositions.Add(transform);
        }
    }
    
    private void RemoveDynamicObstacle(PointOfInterestTypes type, Transform transform)
    {
        if (transform != this.transform)
        {
            m_dynamicObstaclePositions.Remove(transform);
        }
    }

    //TODO: add robust debugging for path calculation
    private Vector3 CalculateMovementDirection(
        Vector3 position,
        Vector3 targetPosition,
        Vector3 currentHeading,
        float minimalViableValue,
        AnimationCurve targetCurve,
        List<Transform> dynamicObstaclePositions,
        AnimationCurve dynamicObstacleCurve,
        List<Transform> staticObstaclePositions,
        AnimationCurve staticObstacleCurve,
        int angleResolution)
    {
        float largest = 0f;
        Vector3 selectedVector = Vector3.zero;

        Vector3 targetVector = (targetPosition - position).normalized;
        
        if (r_movementSpeed < 0.1f) { return targetVector; }
        
        CalculateBoundingNodes(position, staticObstaclePositions, ref m_environmentObstacleNodes);
        
        for (int i = 0; i < angleResolution; i++)
        {
            float angle = i * (360f) / angleResolution;

            Vector3 vector = Quaternion.Euler(0f, angle, 0f) * targetVector;

            float dot =
                Vector3.Dot(vector, targetVector)
                    .CastToRange(-1f, 1f,
                        0f, 1f);
            
            //TODO: make heading bias dropoff a parameter(?) behavior variable? entity variable? 
            //Casting to 0f->1.5f then clamping to 0->1 makes angles close to heading be weighed equally
            float headingDot =  Mathf.Clamp01(Vector3.Dot(currentHeading.normalized, vector)
                    .CastToRange(-1f, 1f,
                        0f, 1.25f));

            float directionBias = targetCurve.Evaluate(dot) * headingDot;

            //TODO: consider shaping obstacle avoidance with
            // dot(away, normal) weight = 1.0 - abs(dot - 0.65)

            for (int j = 0; j < m_environmentObstacleNodes.Length; j++)
            {
                directionBias -= EvaluateObstacle(vector, position, m_environmentObstacleNodes[j], staticObstacleCurve);    
            }
            
            foreach (Transform obstacle in dynamicObstaclePositions)
            {
                float obstacleDot = Vector3.Dot(vector, (obstacle.position - position).normalized);
                //No need to normalize dot for obstacle because angle > 90 can be ignored
                //obstacleDot = (obstacleDot + 1f) / 2f;
                float eval = dynamicObstacleCurve.Evaluate(obstacleDot);
                directionBias -= eval;
            }

            if (directionBias > 0f && directionBias > largest + 0.1)//25)
            {
                largest = directionBias;
                
                if (directionBias > minimalViableValue)
                {
                    selectedVector = vector;
                }
            }
        }
        
        return selectedVector;
    }

    private void CalculateBoundingNodes(Vector3 position, List<Transform> boundingNodes, ref Vector3[] obstacleArray)
    {
        if (boundingNodes == null || boundingNodes.Count < 0 || boundingNodes[0] == null)
        {
            return;
        }
        
        float minimalDistance = (boundingNodes[0].position - position).sqrMagnitude;
        int minimalNodeIndex = -1;

        for (int i = 0; i < boundingNodes.Count; i++)
        {
            float dist = (boundingNodes[i].position - position).sqrMagnitude;
            if (dist < minimalDistance)
            {
                minimalDistance = dist;
                minimalNodeIndex = i;
            }
        }

        if (minimalNodeIndex < 0)
        {
            for (int i = 0; i < 5; i++)
            {
                obstacleArray[i] = boundingNodes[0].position;   
            }
            return;
        }

        minimalNodeIndex = (minimalNodeIndex % boundingNodes.Count + boundingNodes.Count) % boundingNodes.Count;

        Vector3 previousPoint =
            boundingNodes[((minimalNodeIndex - 1) % boundingNodes.Count + boundingNodes.Count) % boundingNodes.Count]
                .position;
        
        Vector3 nextPoint =
            boundingNodes[((minimalNodeIndex + 1) % boundingNodes.Count + boundingNodes.Count) % boundingNodes.Count]
                .position;
        
        Vector3 res1 = MathUtils.GetClosestPointOnLine(
            boundingNodes[minimalNodeIndex].position,
            nextPoint, position);
        Vector3 res2 = MathUtils.GetClosestPointOnLine(
            boundingNodes[minimalNodeIndex].position,
            previousPoint, position);

        obstacleArray[0] = boundingNodes[minimalNodeIndex].position;
        obstacleArray[1] = res1;
        obstacleArray[2] = res2;
        obstacleArray[3] = previousPoint;
        obstacleArray[4] = nextPoint;
    }

    private float EvaluateObstacle(Vector3 direction, Vector3 position, Vector3 obstaclePos,
        AnimationCurve avoidanceCurve)
    {
        float eval = 0f;
        float dot = Vector3.Dot(direction, (obstaclePos - position).normalized);

        if ((obstaclePos - position).sqrMagnitude < 1f)
        {
            eval = avoidanceCurve.Evaluate(dot);
        }
        else
        {
            eval =
                Mathf.Clamp01(1f - ((obstaclePos - position).magnitude / r_movementSpeed))
                * avoidanceCurve.Evaluate(dot);
        }

        return eval;
    }

    #endregion

    public void CancelCurrentMove()
    {
        m_currentMoveCancel?.Invoke();
    }
}

