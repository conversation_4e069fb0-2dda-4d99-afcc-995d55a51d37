using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Cysharp.Threading.Tasks;
using RibCageGames.Animation;
using RibCageGames.Base;
using RibCageGames.Behaviour;
using RibCageGames.Combat;
using RibCageGames.Editor;
using RibCageGames.Input;
using RibCageGames.MonoUtils;
using Unity.Cinemachine;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.InputSystem;
using UnityEngine.UI;
using ActionData = RibCageGames.Combat.ActionData;

public class CharacterControllerBase : BeatCombatEntity
{
    [Header("References")] [Space(10)] [Header("CharacterControllerBase")] [SerializeField]
    private CinemachineBrain m_cameraBrain;

    [SerializeField] private Transform m_graphicHolder;
    [SerializeField] private GameObject m_weaponVisual;
    [SerializeField] private WeaponTypes m_weaponType;
    [SerializeField] private List<BasicMove> m_basicMoves;
    [SerializeField] private BasePlayerWeaponMoveSelectionStrategy m_weaponMoveStrategy;

    [SerializeField] private VisualEffectHolder m_boostParticleSystem;

    [SerializeField] private VisualEffectHolder m_reverbVFX;
    public VisualEffectHolder ReverbVFX => m_reverbVFX;

    [SerializeField] private VisualEffectHolder m_echoVFX;
    public VisualEffectHolder FlangerVFX => m_echoVFX;

    [ArrayElementTitle("m_severity")] [SerializeField]
    private HitSeverityReaction[] m_hitSeverityReactions;

    [Header("Movement")] [SerializeField] private float m_maximalHorizontalVelocity;
    [SerializeField] private float m_walkSpeed;
    [SerializeField] private float m_sprintSpeed;
    [SerializeField] private float m_rotationSpeed = 10f;
    [SerializeField] private float m_acceleration = 30f;
    [SerializeField] private float m_busyAcceleration = 30f;
    [SerializeField] private float m_jumpPower = 50;
    [SerializeField] private float m_dropBoost = 8;
    [SerializeField] private float m_inMoveDropBoost = 0;
    [SerializeField] private int m_airStateDelayFrames;
    [SerializeField] private float m_airStateDelayTime;
    [SerializeField] private float m_coyoteTime;

    [Header("Animator parameters")] [SerializeField] [ArrayElementTitle("Type")]
    private List<NonMoveAnimatorParameter> m_nonMoveAnimatorParameters;

    public Vector2 CurrentCameraRelativeMovement => m_currentCameraRelativeMovement;

    [NonSerialized] private int m_framesOffGround;
    [NonSerialized] private float m_timeOffGround;
    [NonSerialized] private Vector3 m_inputDirection;
    [NonSerialized] private Vector2 m_currentCameraRelativeMovement;
    [NonSerialized] private Dictionary<NonMoveAnimatorParameterType, int> m_animatorParameterDict;
    [NonSerialized] private Vector3 m_lastFrameDirection;
    [NonSerialized] private float m_lastFrameLean;
    [NonSerialized] private float m_moveSpeed;
    [NonSerialized] private CharacterState m_currentState;
    [NonSerialized] private InputAction jumpAction;
    [NonSerialized] private InputAction lightAttackAction;
    [NonSerialized] private InputAction heavyAttackAction;
    [NonSerialized] private InputAction dodgeAction;
    [NonSerialized] private InputAction specialAttackAction;
    [NonSerialized] private InputAction lockOnAction;
    [NonSerialized] private InputAction lockOnSwitchAction;
    [NonSerialized] private PointOfInterestService m_pointOfInterestService;
    [NonSerialized] private bool m_dashUsed;
    [NonSerialized] private bool m_jumpUsed;
    [NonSerialized] private bool m_airAttackUsed;
    [NonSerialized] private ActionData m_groundDodge;
    [NonSerialized] private ActionData m_airDodge;
    [NonSerialized] private Dictionary<PlayerInputActions, List<BasicMove>> m_basicMoveInputDict;
    [NonSerialized] private MonoProcess InputBufferProcess;
    [NonSerialized] private bool m_currentMoveJust = false; //TODO: sub-optimal maybe find a better way
    [NonSerialized] private bool m_nonMovementControlsActive = true;
    [NonSerialized] protected Dictionary<HitSeverity, float> m_hitSeverityStunDict;

    private readonly ServiceReference<InputService> m_inputService = new();

    public Dictionary<PlayerInputActions, List<BasicMove>> BasicMoveInputDict => m_basicMoveInputDict;

    public CharacterState CurrentState => m_currentState;

    public bool CurrentMoveJust => m_currentMoveJust;

    [NonSerialized] private float m_timeBeforeMoveCancelable;

    public UnityEvent<float, float> OnHealthChanged { get; } = new();

    public override bool IsPlayer => true;

    public override Vector3 OwnForward => m_graphicHolder.forward;

    public override int CurrentHealth
    {
        get => m_currentHealth;
        set
        {
            OnHealthChanged.Invoke((float)m_currentHealth / (float)m_maxHealth,
                (float)(value) / (float)m_maxHealth);

            m_currentHealth = value;
        }
    }

    public override float BaseDamage
    {
        get => m_styleDamageMultiplier * base.BaseDamage;
        set => m_styleDamageMultiplier = value;
    }

    [NonSerialized] protected float m_styleDamageMultiplier = 1f;

    public int MaxHealth => m_maxHealth;

    private Vector2 CameraRelativeMovement(Vector3 movementDirection)
    {
        if (m_forcedMovement)
        {
            return Vector2.zero;
        }
        
        Vector3 cameraForward = m_cameraBrain.transform.forward;
        Vector3 cameraRight = m_cameraBrain.transform.right;

        cameraForward.y = 0f;
        cameraRight.y = 0f;

        Vector3 sumMovement = cameraForward * movementDirection.z
                              + cameraRight * movementDirection.x;

        return new Vector2(sumMovement.x, sumMovement.z).normalized;
    }

    protected override void ResetMovement()
    {
        base.ResetMovement();
    }

    public override void Initialize()
    {
        base.Initialize();
        m_hitSeverityStunDict = new Dictionary<HitSeverity, float>();

        foreach (HitSeverityReaction hitSeverity in m_hitSeverityReactions)
        {
            m_hitSeverityStunDict.Add(hitSeverity.Severity, hitSeverity.StunDuration);
        }
    }

    protected override void Start()
    {
        Application.targetFrameRate = 60;
        base.Start();

        m_animatorParameterDict = new Dictionary<NonMoveAnimatorParameterType, int>();
        foreach (NonMoveAnimatorParameter parameter in m_nonMoveAnimatorParameters)
        {
            m_animatorParameterDict.Add(parameter.Type, parameter.Hash);
        }

        foreach (BasicMove move in m_basicMoves)
        {
            if (move.InputAction == PlayerInputActions.Dodge)
            {
                if (move.State == CharacterState.InAir)
                {
                    m_airDodge = move.ActionData;
                }
                else
                {
                    m_groundDodge = move.ActionData;
                }
            }
        }

        //Activate();

        m_pointOfInterestService = ServiceLocator.Get<PointOfInterestService>();

        m_moveSpeed = m_walkSpeed;

        CurrentHealth = m_maxHealth;

        m_basicMoveInputDict = new Dictionary<PlayerInputActions, List<BasicMove>>();
        foreach (BasicMove move in m_basicMoves)
        {
            if (!m_basicMoveInputDict.ContainsKey(move.InputAction))
            {
                m_basicMoveInputDict.Add(move.InputAction, new List<BasicMove>());
            }

            m_basicMoveInputDict[move.InputAction].Add(move);
        }

        PlayerService m_playerService = ServiceLocator.Get<PlayerService>();
        m_playerService.RegisterPlayer(this, m_weaponType);

        //TODO: use a general collection for these instead of enumerating each one
        if (m_boostParticleSystem != null)
        {
            m_boostParticleSystem.Initialize();
        }

        if (m_reverbVFX != null)
        {
            m_reverbVFX.Initialize();
        }

        if (m_reverbVFX != null)
        {
            m_reverbVFX.Initialize();
        }
    }

    public override void ActivateCombatEntity(bool resetHealth = true)
    {
        base.ActivateCombatEntity(resetHealth);
        m_monoService.OnUpdate.AddListener(CalculateMovement);

        m_damageableRegistry.RegisterDamageable(m_hurtBox, this);
        ServiceLocator.Get<LevelManagementService>().OnLevelReset.AddListener(ResetMovement);

        m_pointOfInterestService.AddPoint(PointOfInterestTypes.Player, this.transform);

        RegisterMovementControls();
        RegisterNonMovementControls();
    }

    public void DeregisterNonMovementControls(List<PlayerInputActions> inputExceptions = null)
    {
        if (m_inputService == null || m_inputService.Value == null)
        {
            return;
        }
            
        m_nonMovementControlsActive = false;
        Dictionary<PlayerInputActions, InputEvent> inputActions = m_inputService.Value.InGameInputs;

        foreach (KeyValuePair<PlayerInputActions, InputEvent> action in inputActions)
        {
            if (inputExceptions != null && inputExceptions.Contains(action.Key))
            {
                continue;
            }

            switch (action.Key)
            {
                case PlayerInputActions.Jump:
                    ((InputVoidEvent)action.Value).Performed.RemoveListener(OnJump);
                    break;
                case PlayerInputActions.LightAttack:
                    ((InputVoidEvent)action.Value).Performed.RemoveListener(OnLightAttack);
                    break;
                case PlayerInputActions.Special:
                    ((InputVoidEvent)action.Value).Performed.RemoveListener(OnSpecialAttack);
                    break;
                case PlayerInputActions.Dodge:
                    ((InputVoidEvent)action.Value).Performed.RemoveListener(OnDodge);
                    break;
            }
        }
    }

    public void ToggleWeaponVisual(bool weaponVisualActive)
    {
        m_weaponVisual?.SetActive(weaponVisualActive);
    }

    public void RegisterMovementControls()
    {
        Dictionary<PlayerInputActions, InputEvent> inputActions = m_inputService.Value.InGameInputs;

        foreach (KeyValuePair<PlayerInputActions, InputEvent> action in inputActions)
        {
            if (action.Key == PlayerInputActions.Movement)
            {
                ((InputVector2Event)action.Value).Performed.AddExclusiveListener(OnMovement);
                ((InputVector2Event)action.Value).Canceled.AddExclusiveListener(OnMovement);
                break;
            }
        }
    }

    public void RegisterNonMovementControls()
    {
        m_nonMovementControlsActive = true;
        Dictionary<PlayerInputActions, InputEvent> inputActions = m_inputService.Value.InGameInputs;

        foreach (KeyValuePair<PlayerInputActions, InputEvent> action in inputActions)
        {
            switch (action.Key)
            {
                case PlayerInputActions.Jump:
                    ((InputVoidEvent)action.Value).Performed.AddExclusiveListener(OnJump);
                    break;
                case PlayerInputActions.LightAttack:
                    ((InputVoidEvent)action.Value).Performed.AddExclusiveListener(OnLightAttack);
                    break;
                case PlayerInputActions.Special:
                    ((InputVoidEvent)action.Value).Performed.AddExclusiveListener(OnSpecialAttack);
                    break;
                case PlayerInputActions.Dodge:
                    ((InputVoidEvent)action.Value).Performed.AddExclusiveListener(OnDodge);
                    break;
            }
        }
    }

    public void DeregisterMovementControls()
    {
        Dictionary<PlayerInputActions, InputEvent> inputActions = m_inputService.Value.InGameInputs;

        foreach (KeyValuePair<PlayerInputActions, InputEvent> action in inputActions)
        {
            if (action.Key == PlayerInputActions.Movement)
            {
                ((InputVector2Event)action.Value).Performed.RemoveListener(OnMovement);
                ((InputVector2Event)action.Value).Canceled.RemoveListener(OnMovement);
                break;
            }
        }
    }

    public override void DeactivateCombatEntity(bool immediate = false)
    {
        base.DeactivateCombatEntity(immediate);
        m_monoService.OnUpdate.RemoveListener(CalculateMovement);
        if (!immediate)
        {
            MonoProcess.NextFrame.Do(() =>
            {
                m_damageableRegistry.UnRegisterDamageable(m_hurtBox);
                m_pointOfInterestService.RemovePoint(PointOfInterestTypes.Player, this.transform);
            });
        }
        else
        {
            m_damageableRegistry.UnRegisterDamageable(m_hurtBox);
            m_pointOfInterestService.RemovePoint(PointOfInterestTypes.Player, this.transform);
        }

        ServiceLocator.Get<LevelManagementService>().OnLevelReset.RemoveListener(ResetMovement);

        Dictionary<PlayerInputActions, InputEvent> inputActions = m_inputService.Value.InGameInputs;

        foreach (KeyValuePair<PlayerInputActions, InputEvent> action in inputActions)
        {
            switch (action.Key)
            {
                case PlayerInputActions.Movement:
                    ((InputVector2Event)action.Value).Performed.RemoveListener(OnMovement);
                    ((InputVector2Event)action.Value).Canceled.RemoveListener(OnMovement);
                    break;
                case PlayerInputActions.Jump:
                    ((InputVoidEvent)action.Value).Performed.RemoveListener(OnJump);
                    break;
                case PlayerInputActions.LightAttack:
                    ((InputVoidEvent)action.Value).Performed.RemoveListener(OnLightAttack);
                    break;
                //case PlayerInputActions.HeavyAttack:
                //    ((InputVoidEvent) action.Value).Performed.RemoveListener(OnLightAttack);
                //    break;
                case PlayerInputActions.Special:
                    ((InputVoidEvent)action.Value).Performed.RemoveListener(OnSpecialAttack);
                    break;
                case PlayerInputActions.Dodge:
                    ((InputVoidEvent)action.Value).Performed.RemoveListener(OnDodge);
                    break;
                case PlayerInputActions.LockOn:
                    break;
                case PlayerInputActions.LockOnSwitch:
                    ((InputVoidEvent)action.Value).Performed.RemoveListener(OnLockOnSwitch);
                    break;
            }
        }
    }

    Vector3 m_lastPlacedPosition = Vector3.zero;

    public override async void RepositionEntity(Vector3 newPosition, Quaternion rotation)
    {
        m_lastPlacedPosition = newPosition;
        m_graphicHolder.localRotation = Quaternion.identity;
        base.RepositionEntity(newPosition, rotation);
    }

    public void ControlSpawnAnimation(bool value)
    {
        if (m_animator == null || m_animatorParameterDict == null)
        {
            return;
        }
        
        m_animator.SetBool(m_animatorParameterDict[NonMoveAnimatorParameterType.Spawn], value);
    }

    protected override void ControlledUpdate(float deltaTime)
    {
        base.ControlledUpdate(deltaTime);
        FaceMovement(deltaTime);
        ManageAnimatorParameters(deltaTime);

        GroundCheck(deltaTime);

        if (transform.position.y < -50f)
        {
            //TODO: reposition for wall protection
            RepositionEntity(ServiceLocator.Get<PlayerService>().EncounterStartPosition, Quaternion.identity);
        }

        Vector3 pos = transform.position;
        Shader.SetGlobalVector("_SDRplayerWorldPosition", new Vector4(pos.x, pos.y + 1.5f, pos.z));
    }

    private bool m_dying;

    public override float TakeDamage(float damage, CombatEntity owner, ActionData.ActionHitBox hitData, out bool flinch)
    {
        if (m_dying)
        {
            flinch = false;
            return 0f;
        }

        if (CurrentHealth > 1)
        {
            damage = Mathf.Min(CurrentHealth - 1f, damage);
        }

        float damageDealt = base.TakeDamage(damage, owner, hitData, out flinch);

        if (damageDealt <= 0f || (hitData.HitSeverity == HitSeverity.NonDisruptive && CurrentHealth > 0))
        {
            return damageDealt;
        }

        ToggleSprint(false);
        RootMotionModifier = 1f;
        AnimationMotionModifier = Vector3.one;
        MoveTweenMotionModifier = Vector3.one;
        m_busy = true;
        m_timeBeforeMoveCancelable = 20f;
        m_currentVerticalSpeed = 0f;
        m_currentHorizontalSpeed = Vector2.zero;
        m_currentMoveCancel?.Invoke();
        InputBufferProcess?.Stop();
        m_hitProcess?.Stop();
        IsStunned = true;

        if (hitData.FaceDamageSource)
        {
            Vector3 direction = -owner.OwnForward;
            direction.y = 0f;
            FaceDirection(transform.InverseTransformVector(-owner.OwnForward));
        }

        m_animator.SetBool(m_animatorParameterDict[NonMoveAnimatorParameterType.isStunned], true);
        m_animator.SetTrigger(m_animatorParameterDict[NonMoveAnimatorParameterType.hit]);

        if (CurrentHealth <= 0)
        {
            if (m_dying)
            {
                return 0f;
            }

            m_dying = true;
            m_animator.SetFloat(m_animatorParameterDict[NonMoveAnimatorParameterType.damage], 2f);
            if (hitData.HitMoveEffect)
            {
                m_hitTween = hitData.HitMoveEffect.RunDirected(m_animator.transform.parent, owner.OwnForward);
            }

            m_animator.SetBool(m_animatorParameterDict[NonMoveAnimatorParameterType.isDead], true);

            //Die here
            MonoProcess.WaitForSecondsProcess(2f).Do(() =>
            {
                ServiceLocator.Get<TutorialService>().HideAllTutorials();
                ServiceLocator.Get<LevelManagementService>().PlayerDied();
            });
        }
        else
        {
            if (hitData.HitMoveEffect)
            {
                m_hitTween = hitData.HitMoveEffect.RunDirected(m_animator.transform.parent, owner.OwnForward);
            }

            m_animator.SetFloat(m_animatorParameterDict[NonMoveAnimatorParameterType.damage], hitData.HitSeverity switch
            {
                HitSeverity.LightHit_1 => 1f,
                HitSeverity.LightHit_2 => 1f,
                HitSeverity.HeavyHit_1 => 2f,
                HitSeverity.HeavyHit_2 => 2f,
                HitSeverity.LaunchHit => 3f,
                HitSeverity.SmashHit => 3f,
                HitSeverity.KnockbackHit => 4f,
                _ => 1.0f,
            }); //1f, 2f, 3f, 4f -> light , heavy, knockback, devestation

            ReturnControlAfterHitStun(m_hitSeverityStunDict[hitData.HitSeverity]);
        }

        return damageDealt;
    }

    private void ReturnControlAfterHitStun(float stunDuration)
    {
        m_hitProcess = MonoProcess.WaitForSecondsProcess(stunDuration) //Initial hit
            .Do(() =>
            {
                m_animator.SetBool(m_animatorParameterDict[NonMoveAnimatorParameterType.isStunned], false);
                m_animator.SetFloat(m_animatorParameterDict[NonMoveAnimatorParameterType.damage], 0f);
                IsStunned = false;
                m_busy = false;
            });
    }

    public override void ResetCombatEntity(bool resetHealth = true, bool resetMovement = true)
    {
        m_dying = false;
        base.ResetCombatEntity(resetHealth, resetMovement);
        m_animator.SetBool(m_animatorParameterDict[NonMoveAnimatorParameterType.isStunned], false);
        m_animator.SetBool(m_animatorParameterDict[NonMoveAnimatorParameterType.isDead], false);
        m_animator.SetFloat(m_animatorParameterDict[NonMoveAnimatorParameterType.damage], 0f);
        if (resetHealth)
        {
            CurrentHealth = m_maxHealth;
        }

        MonoProcess.WaitForSecondsProcess(0.85f).Do(() =>
        {
            IsStunned = false;
            m_busy = false;
        }); //Return control
    }

    private void OnMovement(Vector2 value)
    {
        if (value.magnitude < 0.01f)
        {
            ToggleSprint(false);
        }

        m_inputDirection = new Vector3(value.x, 0, value.y);
    }

    private void OnJump()
    {
        if (m_busy || m_jumpUsed || IsStunned)
        {
            return;
        }

        foreach (BasicMove move in m_basicMoveInputDict[PlayerInputActions.Jump])
        {
            if (UsableInCurrentState(move))
            {
                m_busy = true;
                ToggleSprint(false);
                PerformAction(move.ActionData, () => MoveEnded(move.ActionData), move.InputAction);
                break;
            }
        }
    }

    public bool GracePeriodActive { get; private set; } = false;
    public bool GodMode = false;

    public void SetGracePeriod(bool value)
    {
        GracePeriodActive = value;
    }

    public void SetGodMode(bool value)
    {
        GodMode = value;
        if (value)
        {
            OnDamageTaken.RemoveListener(ResetHealthToFull);
            OnDamageTaken.AddListener(ResetHealthToFull);
        }
        else
        {
            OnDamageTaken.RemoveListener(ResetHealthToFull);
        }
    }

    private void ResetHealthToFull(float hp)
    {
        CurrentHealth = MaxHealth;
    }


    [SerializeField] private Toggle m_cheatModeToggle;


    public void PerformCombatAction(ActionData action, PlayerInputActions initiatingAction)
    {
        m_currentMoveCancel?.Invoke();
        m_busy = true;
        PerformAction(action, () => MoveEnded(action), initiatingAction);
        ToggleSprint(false);
    }

    private void OnLightAttack()
    {
        OnAttack(PlayerInputActions.LightAttack);
    }

    //private void OnHeavyAttack()
    //{
    //    OnAttack(PlayerInputActions.HeavyAttack);
    //}

    private void OnSpecialAttack()
    {
        OnAttack(PlayerInputActions.Special);
    }

    public bool GracePeriodInEffect { get; set; }

    private void OnAttack(PlayerInputActions attackButton)
    {
        if (m_busy || IsStunned)
        {
            return;
        }

        if (m_currentState == CharacterState.InAir && m_airAttackUsed)
        {
            return;
        }

        if (m_currentState == CharacterState.InAir)
        {
            m_airAttackUsed = true;
        }

        float startTime = Time.realtimeSinceStartup;
        foreach (BasicMove move in m_basicMoveInputDict[attackButton])
        {
            if (UsableInCurrentState(move))
            {
                if (!(m_beatSystem.IsInGracePeriod && GracePeriodActive && !m_beatSystem.IsOnBeat))
                {
                    GracePeriodInEffect = false;
                    m_busy = true;
                    PerformAction(move.ActionData, () => MoveEnded(move.ActionData), move.InputAction);
                    ToggleSprint(false);
                    break;
                }
                else
                {
                    if (InputBufferProcess != null)
                    {
                        InputBufferProcess?.Stop();
                        InputBufferProcess = null;
                    }

                    InputBufferProcess = MonoProcess.New()
                        .WaitUntil(() => m_beatSystem.IsOnBeat)
                        .Do(() =>
                        {
                            if (m_busy)
                            {
                                return;
                            }

                            GracePeriodInEffect = true;

                            m_busy = true;
                            PerformAction(move.ActionData, () => MoveEnded(move.ActionData), move.InputAction);
                            ToggleSprint(false);
                        });
                    break;
                }
                /*if (m_beatSystem.IsOnBeat ||
                    !(m_beatSystem.IsInGracePeriod || CheatMode))
                {
                    m_busy = true;
                    PerformAction(move.ActionData, () => MoveEnded(move.ActionData), move.InputAction);
                    ToggleSprint(false);
                    break;
                }
                else
                {
                    if (InputBufferProcess != null)
                    {
                        InputBufferProcess?.Stop();
                        InputBufferProcess = null;
                    }
                    InputBufferProcess = MonoProcess.New().WaitUntil(() => m_beatSystem.IsOnBeat// || (!m_beatSystem.IsOnBeat && !m_beatSystem.IsInGracePeriod)
                        ).Do(() =>
                    {
                        if (m_busy)
                        {
                            return;
                        }
                        m_busy = true;
                        PerformAction(move.ActionData, () => MoveEnded(move.ActionData), move.InputAction);
                        ToggleSprint(false);
                    });
                    break;
                }*/
            }
        }
    }

    private void OnDodge()
    {
        if (m_busy || IsStunned)
        {
            return;
        }

        if (m_currentState == CharacterState.InAir && m_dashUsed)
        {
            return;
        }

        if (m_currentState == CharacterState.InAir)
        {
            m_dashUsed = true;
        }

        foreach (BasicMove move in m_basicMoveInputDict[PlayerInputActions.Dodge])
        {
            if (UsableInCurrentState(move))
            {
                /*m_busy = true;
                FaceMovement(1f); //Snap to movement direction
                PerformAction(move.ActionData, () => MoveEnded(move.ActionData), move.InputAction);
                break;*/
                if (m_beatSystem.IsInGracePeriod && GracePeriodActive && !m_beatSystem.IsOnBeat)
                {
                    if (InputBufferProcess != null)
                    {
                        InputBufferProcess?.Stop();
                        InputBufferProcess = null;
                    }

                    InputBufferProcess = MonoProcess.New().WaitUntil(() => m_beatSystem.IsOnBeat).Do(() =>
                    {
                        if (m_busy || IsStunned)
                        {
                            return;
                        }

                        m_busy = true;
                        FaceMovement(1f); //Snap to movement direction
                        PerformAction(move.ActionData, () => MoveEnded(move.ActionData), move.InputAction);
                    });
                    break;
                }
                else
                {
                    m_busy = true;
                    FaceMovement(1f); //Snap to movement direction
                    PerformAction(move.ActionData, () => MoveEnded(move.ActionData), move.InputAction);
                    break;
                }
            }
        }

        ToggleSprint(false);
    }

    private void OnLockOnSwitch()
    {
        //m_lockOnIndicator.ChangeLockOnTarget();
        //CheatMode = !CheatMode;
        //if (m_cheatModeToggle)
        //{
        //    m_cheatModeToggle.isOn = CheatMode;
        //}
    }

    public override void MoveStarted(ActionData actionData, Action cancelMoveSource,
        PlayerInputActions initiatingAction)
    {
        //Debug.LogError($"MoveStarted {actionData.name}");
        InputBufferProcess?.Stop();
        m_currentMoveJust = m_beatSystem.IsOnBeat;

        if (initiatingAction != PlayerInputActions.Jump)
        {
            m_currentHorizontalSpeed = Vector2.zero;
        }

        m_currentVerticalSpeed = 0f;

        m_timeBeforeMoveCancelable = actionData.MinimalMoveTime;
        m_busy = true;
        RootMotionModifier = 1f;

        m_currentActionData = actionData;

        if ((actionData == m_airDodge || actionData == m_groundDodge) &&
            m_currentCameraRelativeMovement.magnitude > 0f)
        {
            FaceMovement(1f);
        }
        else
        {
            FaceEnemy(GetCombatTarget(actionData));
        }

        if (actionData == m_airDodge)
        {
            m_dashUsed = true;
        }

        m_animator.ResetTrigger(m_animatorParameterDict[NonMoveAnimatorParameterType.AnimationFade]);

        base.MoveStarted(actionData, cancelMoveSource, initiatingAction);
    }

    private void MoveEnded(ActionData move)
    {
        m_currentActionData = null;
        RootMotionModifier = 1f;
        OnMoveEnded?.Invoke(move);
        m_currentMoveJust = false;
        m_currentMoveCancel?.Invoke();
        m_currentMoveCancel = null;
        if (!IsStunned)
        {
            m_busy = false;
        }

        m_animatorRootMotionTransfer = m_originalRootMotionTransfer;
    }

    private bool m_boostVisualActive;

    public void ActivateComboBoost(PowerUpType type, float value, bool overrideValue = false, Action onRemove = null)
    {
        ChangePowerUpValue(type, value, overrideValue, onRemove);
        if (!m_boostVisualActive)
        {
            m_boostVisualActive = true;
            m_boostParticleSystem.Play();
        }
    }

    public void DeactivateComboBoost(PowerUpType type)
    {
        RemovePowerUp(type);
        m_boostVisualActive = false;
        m_boostParticleSystem.Stop();
    }

    protected override void SetGrounded(bool grounded, Collider collider = null)
    {
        base.SetGrounded(grounded, collider);
        if (grounded)
        {
            m_jumpUsed = false;
            m_dashUsed = false;
            m_airAttackUsed = false;
        }
    }

    public override bool CanPerform(PlayerInputActions playerInputAction, ActionData action)
    {
        if (IsStunned || !m_inputService.Value.InputEnabled || !m_nonMovementControlsActive)
        {
            return false;
        }

        if (InAir &&
            ((playerInputAction == PlayerInputActions.Dodge && m_dashUsed) ||
             (playerInputAction == PlayerInputActions.Jump && m_jumpUsed)))
        {
            return false;
        }

        if (action.Ariel ^ InAir)
        {
            return false;
        }

        if (m_weaponMoveStrategy != null)
        {
            return m_weaponMoveStrategy.CanExecuteMove(action);
        }

        return true;
    }

    private bool UsableInCurrentState(BasicMove move)
    {
        if (m_currentState == CharacterState.InAir && m_timeOffGround > m_coyoteTime)
        {
            return move.State == CharacterState.InAir;
        }
        else
        {
            return move.State == m_currentState ||
                   (move.State == CharacterState.Grounded && m_currentState != CharacterState.InAir);
        }
    }

    public override void Jump()
    {
        m_currentVerticalSpeed = m_jumpPower;
        m_animator.SetTrigger(m_animatorParameterDict[NonMoveAnimatorParameterType.Jump]);

        m_jumpUsed = true;
    }

    private void GroundCheck(float deltaTime)
    {
        if (Grounded)
        {
            m_dashUsed = false;
            m_jumpUsed = false;
            m_airAttackUsed = false;

            m_framesOffGround = 0;
            m_timeOffGround = 0f;

            m_animator.SetFloat(m_animatorParameterDict[NonMoveAnimatorParameterType.IsInAir], InAir ? 1f : 0f);
            if (m_currentState != CharacterState.Sprinting)
            {
                m_currentState = InAir ? CharacterState.InAir : CharacterState.Grounded;
            }
        }
        else
        {
            m_framesOffGround++;
            m_timeOffGround += deltaTime;
            if ((m_framesOffGround > m_airStateDelayFrames && m_timeOffGround > m_airStateDelayTime) ||
                m_currentVerticalSpeed > 0f)
            {
                m_animator.SetFloat(m_animatorParameterDict[NonMoveAnimatorParameterType.IsInAir], InAir ? 1f : 0f);
                if (m_currentState != CharacterState.Sprinting)
                {
                    m_currentState = InAir ? CharacterState.InAir : CharacterState.Grounded;
                }
            }
        }
    }

    public void ToggleSprint(bool sprint)
    {
        if (sprint && m_currentCameraRelativeMovement.sqrMagnitude > 0f)
        {
            //Start sprint
            m_moveSpeed = m_sprintSpeed;
            m_animator.SetFloat(
                m_animatorParameterDict[NonMoveAnimatorParameterType.NormalizedSpeed],
                2f);
            m_currentState = CharacterState.Sprinting;
            m_currentHorizontalSpeed = m_currentCameraRelativeMovement * m_sprintSpeed;
        }
        else
        {
            //End sprint
            m_moveSpeed = m_walkSpeed;
            m_animator.SetFloat(
                m_animatorParameterDict[NonMoveAnimatorParameterType.NormalizedSpeed],
                1f);
            m_currentState = InAir ? CharacterState.InAir : CharacterState.Grounded;
        }
    }

    private void FaceMovement(float deltaTime)
    {
        if (m_busy && deltaTime < 1f)
        {
            return;
        }

        Vector3 locMovement = transform.InverseTransformVector(new Vector3(m_currentCameraRelativeMovement.x, 0f,
            m_currentCameraRelativeMovement.y));

        FaceDirection(locMovement, deltaTime);
    }

    //TODO: refactor into CombatEntity
    public override void FaceDirection(Vector3 direction, float delta = 1f)
    {
        direction.y = 0f;
        if (Mathf.Abs(direction.x) > 0.1f || Mathf.Abs(direction.z) > 0.1f)
        {
            Quaternion lookRotation = Quaternion.LookRotation(direction);
            m_graphicHolder.localRotation =
                Quaternion.Slerp(m_graphicHolder.localRotation,
                    lookRotation,
                    m_rotationSpeed * delta);
        }
    }

    private void ManageAnimatorParameters(float deltaTime)
    {
        if (m_currentState == CharacterState.Sprinting)
        {
            m_animator.SetFloat(
                m_animatorParameterDict[NonMoveAnimatorParameterType.NormalizedSpeed],
                IsStunned ? 0f : 2f);
        }
        else
        {
            if (m_forcedMovement)
            {
                m_animator.SetFloat(m_animatorParameterDict[NonMoveAnimatorParameterType.NormalizedSpeed], 1f);
            }
            else
            {
                m_animator.SetFloat(
                    m_animatorParameterDict[NonMoveAnimatorParameterType.NormalizedSpeed],
                    IsStunned ? 0f : m_currentCameraRelativeMovement.sqrMagnitude);
            }
        }

        float lean = -Vector3.SignedAngle(m_graphicHolder.transform.forward, m_lastFrameDirection, Vector3.up) / 10f;
        lean = Mathf.Lerp(m_lastFrameLean, lean, 0.5f);
        m_lastFrameDirection = m_graphicHolder.transform.forward;
        //lean = Mathf.Clamp(lean * 10f, -1, 1);
        m_animator.SetFloat(
            m_animatorParameterDict[NonMoveAnimatorParameterType.NormalizedLean],
            lean);
        m_lastFrameLean = lean;

        float verticalSpeed = m_currentVerticalSpeed / m_maximalHorizontalVelocity;
        m_animator.SetFloat(
            m_animatorParameterDict[NonMoveAnimatorParameterType.NormalizedVerticalSpeed],
            verticalSpeed);
    }

    private Vector2 m_currentHorizontalSpeed = Vector2.zero;
    private float m_currentVerticalSpeed = 0f;

    private void CalculateMovement(float deltaTime)
    {
        m_timeBeforeMoveCancelable -= deltaTime;
        m_timeBeforeMoveCancelable = Mathf.Max(-1f, m_timeBeforeMoveCancelable);

        Vector3 previousMovement = m_currentCameraRelativeMovement;
        m_currentCameraRelativeMovement = CameraRelativeMovement(m_inputDirection);

        //Cancel move because movement
        if (m_currentActionData != null && m_timeBeforeMoveCancelable < 0f &&
            (((m_currentCameraRelativeMovement.magnitude > 0f
               && previousMovement.magnitude < 0.3f)
              || m_currentActionData == m_groundDodge || m_currentActionData == m_airDodge)
             || (m_currentActionData.Ariel && !InAir)))
        {
            OnMoveCancelled?.Invoke(m_currentActionData);
            m_currentMoveCancel?.Invoke();
            m_currentActionData = null;
            if (!IsStunned)
            {
                AnimationMotionModifier = Vector3.one;
                MoveTweenMotionModifier = Vector3.one;
                m_busy = false;
            }

            //Trigger animation fade
            m_animator.SetTrigger(m_animatorParameterDict[NonMoveAnimatorParameterType.AnimationFade]);
        }

        //fall boost
        if (InAir)
        {
            m_currentVerticalSpeed += -1f * deltaTime * (m_currentActionData != null && m_timeBeforeMoveCancelable > 0f
                ? m_inMoveDropBoost
                : m_dropBoost);
        }
        else
        {
            m_currentVerticalSpeed = Mathf.Clamp(m_currentVerticalSpeed, 0f, m_jumpPower);

            if (m_currentVerticalSpeed < 0.1f)
            {
                m_currentVerticalSpeed = -m_currentHorizontalSpeed.magnitude;
            }
        }

        if (m_busy && m_currentState == CharacterState.Grounded)
        {
            m_currentHorizontalSpeed = Vector2.zero;
            ConcurrentMovement = new Vector3(m_currentHorizontalSpeed.x, m_currentVerticalSpeed,
                m_currentHorizontalSpeed.y);
            return;
        }

        m_currentVerticalSpeed =
            Mathf.Clamp(m_currentVerticalSpeed, -m_maximalHorizontalVelocity, m_maximalHorizontalVelocity);


        m_currentHorizontalSpeed += m_currentCameraRelativeMovement
                                    * (m_busy ? m_busyAcceleration : m_acceleration) * deltaTime;

        m_currentHorizontalSpeed = Vector2.ClampMagnitude(m_currentHorizontalSpeed, m_moveSpeed);

        if (m_inputDirection == Vector3.zero && !m_forcedMovement)
        {
            m_currentHorizontalSpeed = Vector2.zero;
        }

        ConcurrentMovement =
            new Vector3(m_currentHorizontalSpeed.x, m_currentVerticalSpeed, m_currentHorizontalSpeed.y);
    }

    private bool m_forcedMovement = false;

    public async UniTask ForceMovement(Vector3 target)
    {
        Vector3 originalDelta = target - transform.position;
        m_forcedMovement = true;
        while (Vector3.Distance(transform.position, target) > 0.1f
               && Vector3.Dot(originalDelta, target - transform.position) > 0f)
        {
            Vector3 movementVector = target - transform.position;
            movementVector.y = 0f;
            movementVector = m_walkSpeed * movementVector.normalized;
            m_currentHorizontalSpeed = new Vector3(movementVector.x, movementVector.z, 0f);
            FaceDirection(transform.InverseTransformVector(movementVector));
            await UniTask.NextFrame();
        }

        m_forcedMovement = false;
    }

    public void FaceEnemy(Transform target)
    {
        //Vector3 targetVector = Vector3.zero;
        //if (m_currentCameraRelativeMovement.sqrMagnitude < 0.1f && selectedFullSnap != null)
        if (target != null)
        {
            Vector3 targetVector = target.position - transform.position;
            targetVector.y = 0f;
            FaceDirection(transform.InverseTransformVector(targetVector));

            //Vector3 distance = targetVector;
            //distance.y = 0f;

            //RootMotionModifier = Mathf.Lerp(0f, 1f, distance.magnitude - 1f);    
        }
        else
        {
            FaceMovement(1f);
        }
    }

    public async UniTask WaitUntilComboEnd()
    {
        bool nonAttackActionUsed = false;
        OnMoveStarted.AddListenerUntil(
            (data, actions) =>
            {
                if (data.HitBoxCount < 1)
                {
                    nonAttackActionUsed = true;
                }
            }, (data, actions) => nonAttackActionUsed);

        await UniTask.WaitUntil(() => IsStunned || !Busy || nonAttackActionUsed);
    }

    public override Transform GetCombatTarget(ActionData actionData = null)
    {
        if (actionData == null)
        {
            actionData = m_currentActionData;
        }

        return GetTargetEnemy(actionData.FullSnapRadius, actionData.LightSnapAngle, actionData.FullSnapRadius,
            actionData.IgnoreHeight);
    }

    //TODO: cache result
    public Transform GetTargetEnemy(float fullSnapRadius, float halfSnapAngle, float halfSnapRadius, bool ignoreHeight)
    {
        (Transform fullSnapTarget, Transform partialSnapTarget) result = (null, null);

        Vector2 cameraRelativeMovement = CurrentCameraRelativeMovement;
        bool activeMovement = cameraRelativeMovement.sqrMagnitude > 0f;
        Transform animatorTransform = Animator.transform;
        Vector3 interestVector = activeMovement
            ? new Vector3(cameraRelativeMovement.x, 0f, cameraRelativeMovement.y)
            : new Vector3(animatorTransform.forward.x, 0f, animatorTransform.forward.z);

        if (m_pointOfInterestService.GetPointsOfType(PointOfInterestTypes.Enemy, out List<Transform> enemies))
        {
            float minDistanceFullSnap = 10000f;
            float minDistancePartialSnap = 10000f;
            foreach (Transform enemy in enemies)
            {
                Vector3 distanceVector = enemy.transform.position - transform.position;
                //TODO: optional ignore height in move?
                if (ignoreHeight)
                {
                    distanceVector.y = 0f;
                }

                //TODO: make square magnitude
                float distance = distanceVector.magnitude;

                //TODO: minimal reverse direction weight setable?
                float angleCoefficient = activeMovement
                    ? Mathf.Cos(Vector3.Angle(interestVector, distanceVector))
                        .CastToRange(-1f, 1f, 0.5f, 1f)
                    : 1f;

                if (distance < fullSnapRadius && enemy.gameObject.activeSelf && distance < minDistanceFullSnap)
                {
                    minDistanceFullSnap = distance * angleCoefficient;
                    result.fullSnapTarget = enemy;
                }

                if (Vector3.Angle(distanceVector, interestVector) < halfSnapAngle &&
                    distance < minDistancePartialSnap && distance < halfSnapRadius)
                {
                    minDistancePartialSnap = distance * angleCoefficient;
                    result.partialSnapTarget = enemy;
                }
            }
        }

        return result.fullSnapTarget ? result.fullSnapTarget : result.partialSnapTarget;
    }


//TODO: this requires more research and custom inspector shenanigins to do later
    //protected override void OnValidate()
    //{
    //    base.OnValidate();
    //    
    //    //This is a hack because m_playerInput.currentActionMap.actions is null here
    //    List<InputAction> actionList = new List<InputAction>();
    //    actionList.Add(movementAction);
    //    actionList.Add(jumpAction);
    //    actionList.Add(lightAttackAction);
    //    actionList.Add(heavyAttackAction);
    //    actionList.Add(dodgeAction);
//
    //    Debug.Log($"actionList has {actionList.Count} values");
    //    
    //    foreach (InputAction action in actionList)
    //    {
    //        if (!m_basicMovesTest.ContainsKey(action))
    //        {
    //            m_basicMovesTest[action] = new ButtonBasicMovesCollection();
    //        }

    public enum NonMoveAnimatorParameterType
    {
        NormalizedSpeed,
        NormalizedLean,
        NormalizedVerticalSpeed,
        IsInAir,
        Jump,

        //TODO: figure these out setup is weird
        isStunned,
        damage,
        isDead,
        hit,

        AnimationFade,

        Spawn,
    }

    public enum CharacterState
    {
        Grounded,
        InAir,
        Sprinting,
    }

    [Serializable]
    public class NonMoveAnimatorParameter
    {
        public NonMoveAnimatorParameterType Type;

        [TargetedAnimatorParameter("m_animator")]
        public TargetedAnimatorParameter Parameter;

        public int Hash => Parameter.Value;
    }

    [Serializable]
    public class BasicMove
    {
        public PlayerInputActions InputAction;
        public CharacterState State;
        public ActionData ActionData;
    }
}