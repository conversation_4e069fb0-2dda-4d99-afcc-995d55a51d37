using System;
using System.Collections.Generic;
using RibCageGames.Base;
using RibCageGames.Combat;
using UnityEngine;

/// <summary>
/// Delay B1 - repeating explosion
/// Enough stored damage starts a new fuse
/// Often associated with reggae and dub music, features long feedback, tempo-synced echoes, and heavy modulation, often used creatively with manual tweaks.
/// </summary>

[CreateAssetMenu(fileName = "DubDelayPowerup", menuName = "RibCageGames/AlphaNomos/BonusEffects/DubDelayPowerup")]
public class DubDelayPowerup : DelayPowerup
{
    [SerializeField] private List<DubDelayPowerupLevel> m_dubPowerLevels;
    public override void UpgradeBonusLevel()
    {
        int currentLevel = m_playerService.GetPowerUpLevel(PowerUpType.DelayUpgrade);
        //int effectiveLevel = Mathf.Clamp(currentLevel+ 1, 0, m_delayPowerupLevels.Count - 1);
        
        BonusEffect eb = new DubDelayBonus(m_dubPowerLevels[currentLevel], m_powerupLevel).ApplyBonusTo(m_playerService.PlayerController);
        
        ServiceLocator.Get<PlayerService>().AddBonusEffect(eb, this, m_powerupLevel);
    }

    [Serializable]
    public class DubDelayPowerupLevel : DelayPowerupLevel
    {
        [SerializeField] private float m_damageRequiredForRetrigger;
        public float DamageRequiredForRetrigger => m_damageRequiredForRetrigger;
    }
}

public class DubDelayBonus : DelayBonus
{
    public DubDelayBonus(DelayPowerup.DelayPowerupLevel level, int powerupLevel) : base(level, powerupLevel)
    {
    }
    
    protected override void FuseEnded()
    {
        if (m_enemyHit != null)
        {
            m_enemyHit.OnDamageTaken.RemoveListener(AddDamageToSum);

            float damageDealt = m_data.MinimalDamage + Mathf.Ceil(m_data.DamageMultiplier * m_damageSum);
            
            m_enemyHit.TakeDamage(damageDealt,
                m_owner,
                ActionData.ActionHitBox.DirectHitbox(m_data.HitSeverity, m_data.HitTween, false), out _);
            m_damageableRegistry.OnDamageToEntity?.Invoke(damageDealt, m_enemyHit, true);

            m_damageSum = 0f;
            
            if (m_damageSum > ((DubDelayPowerup.DubDelayPowerupLevel) m_data).DamageRequiredForRetrigger)
            {
                StartFuse(FuseEnded);
            }
            else
            {
                m_enemyHit = null;
            }
        }
    }
}