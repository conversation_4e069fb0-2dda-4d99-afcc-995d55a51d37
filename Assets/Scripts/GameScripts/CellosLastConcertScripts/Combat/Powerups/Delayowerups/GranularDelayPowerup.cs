using System;
using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using RibCageGames.Base;
using RibCageGames.Combat;
using UnityEngine;

/// <summary>
/// Delay B2 - Granular Delay - chain explosion
/// Small chain explosions. Explosion starts immediately and does damage based on initial hit
/// Fantastic for general damage output and staggering enemies
/// Splits the input signal into tiny "grains" and processes them with delay, creating glitchy, atmospheric, or textured effects
/// </summary>

[CreateAssetMenu(fileName = "GranularDelayPowerup", menuName = "RibCageGames/AlphaNomos/BonusEffects/GranularDelayPowerup")]
public class GranularDelayPowerup : DelayPowerup
{
    [SerializeField] private List<GranularDelayPowerupLevel> m_granularPowerLevels;
    public override void UpgradeBonusLevel()
    {
        int currentLevel = m_playerService.GetPowerUpLevel(PowerUpType.DelayUpgrade);
        
        BonusEffect eb = new GranularDelayBonus(m_granularPowerLevels[currentLevel], m_powerupLevel).ApplyBonusTo(m_playerService.PlayerController);
        
        ServiceLocator.Get<PlayerService>().AddBonusEffect(eb, this, m_powerupLevel);
    }

    [Serializable]
    public class GranularDelayPowerupLevel : DelayPowerupLevel
    {
        [SerializeField] private List<float> m_explosionDamageMultipliers;
        public List<float> ExplosionDamageMultipliers => m_explosionDamageMultipliers;
    }
}

public class GranularDelayBonus : DelayBonus
{
    public GranularDelayBonus(DelayPowerup.DelayPowerupLevel level, int powerupLevel) : base(level, powerupLevel)
    {
    }
    
    protected override async void AttackLanded(ActionData move, List<(DamageableRegistry.IDamageable damageable, float damage)> enemiesHit)
    {
        if (m_enemyHit != null)
        {
            return;
        }

        if (//m_owner.CurrentMoveJust && 
            //m_data.AppliedToMove(move) && 
            enemiesHit != null && enemiesHit.Count > 0)
        {
            StopFuse();
            m_enemyHit = (CombatEntity) enemiesHit[0].damageable;
            m_damageSum = enemiesHit[0].damage;

            m_fuseEffectOffset = m_enemyHit.LockOnOffset;
            m_monoService.OnUpdate.AddListener(MoveFuseEffect);
            
            m_fuseEffect.Play();
            m_explosionEffect.Play();
            
            foreach (float multiplier in ((GranularDelayPowerup.GranularDelayPowerupLevel)m_data).ExplosionDamageMultipliers)
            {
                await UniTask.WaitForSeconds(m_beatSystem.HalfBeatLength);
                TriggerExplosion(m_data.MinimalDamage + Mathf.Ceil(m_damageSum * multiplier));
            }
            
            m_damageSum = 0f;
            m_enemyHit = null;
        }
    }

    private void TriggerExplosion(float explosionDamage)
    {
        if (m_enemyHit != null)
        {
            m_enemyHit.TakeDamage(explosionDamage,
                m_owner,
                ActionData.ActionHitBox.DirectHitbox(m_data.HitSeverity, m_data.HitTween, false), out _);
            m_damageableRegistry.OnDamageToEntity?.Invoke(explosionDamage, m_enemyHit, true);
        }
    }
}