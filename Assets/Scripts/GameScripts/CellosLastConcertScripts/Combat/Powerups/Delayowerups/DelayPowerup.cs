using System;
using System.Collections.Generic;
using System.Linq;
using RibCageGames.Base;
using RibCageGames.Combat;
using RibCageGames.Input;
using RibCageGames.MonoUtils;
using RibCageGames.Music;
using RibCageGames.Sound;
using RibCageGames.UI;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.Serialization;

[CreateAssetMenu(fileName = "DelayPowerup", menuName = "RibCageGames/AlphaNomos/BonusEffects/DelayPowerup")]
public class DelayPowerup : Powerup
{
    [SerializeField] protected int m_powerupLevel;
    [FormerlySerializedAs("m_echoPowerupLevels")]
    [SerializeField] private List<DelayPowerupLevel> m_delayPowerupLevels;
    
    public override PowerUpType Type => PowerUpType.Delay;
    public override bool IsNamedPowerup => true;
    protected PlayerService m_playerService;
    
    public override void Initialize()
    {
        m_playerService = ServiceLocator.Get<PlayerService>();
    }
    
    //Refactor this into base powerup of some sort that is only reverb, flanger, daly etc
    public override void SelectPowerup()
    {
        if (m_playerService.PowerUpLevelDict.TryGetValue(Type, out (int, BonusEffect, Powerup currentPowerup) data))
        {
            //Debug.LogError($"Trigger evolutions for {data.currentPowerup} with {data.currentPowerup.PowerupEvolutions.Count}");
            if (ServiceLocator.Get<PopupService>().TryGetElement(out PowerupEvolutionSelectionScreen selectionScreen))
            {
                selectionScreen.SetPowerupOptions(
                    data.currentPowerup.PowerupEvolutions
                        .Select<Powerup, (Powerup, Action)>(x => (x, x.UpgradeBonusLevel))
                        .ToList());
            }
        }
        else
        {
            UpgradeBonusLevel();
        }
    }
    
    public override void UpgradeBonusLevel()
    {
        int currentLevel = m_playerService.GetPowerUpLevel(PowerUpType.DelayUpgrade);
        int effectiveLevel = Mathf.Clamp(currentLevel+ 1, 0, m_delayPowerupLevels.Count - 1);
        
        BonusEffect eb = new DelayBonus(m_delayPowerupLevels[effectiveLevel], effectiveLevel + 1).ApplyBonusTo(m_playerService.PlayerController);
        
        m_playerService.AddBonusEffect(eb, this, effectiveLevel);
    }
    
    private void OnValidate()
    {
        //TODO: make this promise uniqueness
        for (int i = 0; i < m_delayPowerupLevels.Count - 1; i++)
        {
            //foreach (ActionData move in m_delayPowerupLevels[i].AppliedMoves)
            //{
            //    if (move != null && !m_delayPowerupLevels[i + 1].AppliedMoves.Contains(move))
            //    {
            //        m_delayPowerupLevels[i + 1].AppliedMoves.Add(move);
            //    }
            //}
        }
        
        //for (int i = 0; i < m_delayPowerupLevels.Count; i++)
        //{
        //    HashSet<ActionData> uniqueSet = new HashSet<ActionData>(m_delayPowerupLevels[i].AppliedMoves);
        //    m_delayPowerupLevels[i].AppliedMoves = uniqueSet.ToList();
        //}
    }

    [Serializable]
    public class DelayPowerupLevel
    {
        [SerializeField] private float m_minimalDamage;
        [SerializeField] private float m_damageMultiplier;
        [SerializeField] private HitSeverity m_hitSeverity;
        [SerializeField] private MonoTweenerSettings m_hitTween;
        [SerializeField] private int m_beatDelay;
        //[SerializeField] private List<ActionData> m_appliedMoves;
        [SerializeField] private SoundEffect m_tickSoundEffect;
        [SerializeField] private SoundEffect m_activationSoundEffect;
        [SerializeField] private ParticleSystem m_fuseEffectPrefab;
        [SerializeField] private ParticleSystem m_explosionEffectPrefab;

        private Dictionary<ActionData, bool> m_appliedMovesDict;
        
        public float MinimalDamage => m_minimalDamage;
        public float DamageMultiplier => m_damageMultiplier;
        public HitSeverity HitSeverity => m_hitSeverity;
        public MonoTweenerSettings HitTween => m_hitTween;
        public int BeatDelay => m_beatDelay;
        //public List<ActionData> AppliedMoves
        //{
        //    get => m_appliedMoves;
        //    internal set => m_appliedMoves = value;
        //}

        public SoundEffect TickSoundEffect => m_tickSoundEffect;
        public SoundEffect ActivationSoundEffect => m_activationSoundEffect;
        public ParticleSystem FuseEffectPrefab => m_fuseEffectPrefab;
        public ParticleSystem ExplosionEffectPrefab => m_explosionEffectPrefab;
        
        public void Initialize()
        {
            //m_appliedMovesDict = new Dictionary<ActionData, bool>();
            //foreach (ActionData move in AppliedMoves)
            //{
            //    m_appliedMovesDict.TryAdd(move, true);
            //}
        }
        //public bool AppliedToMove(ActionData move)
        //{
        //    if (m_appliedMovesDict.TryGetValue(move, out var applied))
        //    {
        //        return applied;    
        //    }
        //    return false;
        //}
    }
}

public class DelayBonus : BonusEffect
{
    public override PowerUpType BonusType => PowerUpType.Delay;

    protected CharacterControllerBase m_owner = null;
    protected BeatSystem m_beatSystem;
    protected MonoService m_monoService;
    protected DamageableRegistry m_damageableRegistry;
    protected SoundService m_soundService;
    protected DelayPowerup.DelayPowerupLevel m_data;
    protected ParticleSystem m_fuseEffect;
    protected ParticleSystem m_explosionEffect;
    protected CombatEntity m_enemyHit;
    protected float m_damageSum;
    protected Vector3 m_fuseEffectOffset;
    protected Action m_removeBonusAction;
    protected readonly int m_level;

    public DelayBonus(DelayPowerup.DelayPowerupLevel level, int powerLevel)
    {
        level.Initialize();
        m_data = level;
        m_level = powerLevel;
        m_fuseEffect = GameObject.Instantiate(level.FuseEffectPrefab);
        GameObject.DontDestroyOnLoad(m_fuseEffect);
        m_fuseEffect.Stop();
        m_explosionEffect = GameObject.Instantiate(level.ExplosionEffectPrefab);
        GameObject.DontDestroyOnLoad(m_explosionEffect);
        m_explosionEffect.Stop();
    }

    public virtual BonusEffect ApplyBonusTo(CombatEntity target)
    {
        m_beatSystem = ServiceLocator.Get<BeatSystem>();
        
        m_monoService = ServiceLocator.Get<MonoService>();
        
        m_damageableRegistry = ServiceLocator.Get<DamageableRegistry>();

        m_soundService = ServiceLocator.Get<SoundService>();
        
        m_owner = (CharacterControllerBase) target;
                
        m_owner.OnMoveStarted.AddListener(MoveStarted);
        
        return this;
    }

    private void MoveStarted(ActionData action, PlayerInputActions input)
    {
        if (input == PlayerInputActions.Special)
        {
            m_owner.OnAttackLanded.AddListener(AttackLanded);
        }
    }

    protected virtual void StartFuse(Action onComplete)
    {
        UnityAction<bool> beatAction = null;
        int beatCount = 0;

        m_removeBonusAction?.Invoke();

        m_beatSystem.SetEffectValue(EffectType.Delay, m_level);

        beatAction = (_) =>
        {

            beatCount++;
            m_soundService.PlayClip(
                beatCount > m_data.BeatDelay ? m_data.ActivationSoundEffect : m_data.TickSoundEffect);

            if (beatCount > m_data.BeatDelay)
            {
                m_fuseEffect.Stop();
                m_explosionEffect.Play();

                onComplete?.Invoke();

                StopFuse();
            }
        };

        m_beatSystem.OnBeat.AddListener(beatAction);

        m_removeBonusAction = () =>
        {
            m_beatSystem.OnBeat.RemoveListener(beatAction);
            m_monoService.OnUpdate.RemoveListener(MoveFuseEffect);
            //Second null setting
            //Debug.LogError($"Enemy set to null here");
            m_enemyHit = null;
            m_removeBonusAction = null;
            m_beatSystem.SetEffectValue(EffectType.Delay, 0);
        };
    }

    protected virtual void AttackLanded(ActionData move, List<(DamageableRegistry.IDamageable damageable, float damage)> enemiesHit)
    {
        if (m_enemyHit != null)
        {
            return;
        }
        
        m_owner.OnAttackLanded.RemoveListener(AttackLanded);

        if (//m_owner.CurrentMoveJust && 
            //m_data.AppliedToMove(move) && 
            enemiesHit != null && enemiesHit.Count > 0)
        {
            StopFuse();
            m_enemyHit = (CombatEntity) enemiesHit[0].damageable;
            m_damageSum = 0f;

            m_fuseEffectOffset = m_enemyHit.LockOnOffset;
            m_monoService.OnUpdate.AddListener(MoveFuseEffect);
            
            m_fuseEffect.Play();
            m_enemyHit.OnDamageTaken.AddListener(AddDamageToSum);
            StartFuse(FuseEnded);
        }
    }

    protected void AddDamageToSum(float damage)
    {
        m_damageSum += damage;
    }

    protected virtual void FuseEnded()
    {
        if (m_enemyHit != null)
        {
            m_enemyHit.OnDamageTaken.RemoveListener(AddDamageToSum);

            float damageDealt = m_data.MinimalDamage + Mathf.Ceil(m_data.DamageMultiplier * m_damageSum);
            
            m_enemyHit.TakeDamage(damageDealt,
                m_owner,
                ActionData.ActionHitBox.DirectHitbox(m_data.HitSeverity, m_data.HitTween, false), out _);
            m_damageableRegistry.OnDamageToEntity?.Invoke(damageDealt, m_enemyHit, true);

            m_damageSum = 0f;
            //First null setting
            //Debug.LogError($"Enemy set to null here");
            m_enemyHit = null;
        }
    }

    protected virtual void MoveFuseEffect(float delta)
    {
        if (m_enemyHit == null) { return; }
        
        if (m_enemyHit.IsDead)
        {
            StopFuse();
        }
        else
        {
            m_fuseEffect.transform.position = m_enemyHit.ConnectedTransform.position + m_fuseEffectOffset;
            m_explosionEffect.transform.position = m_enemyHit.ConnectedTransform.position;
        }
    }

    protected void StopFuse()
    {
        m_removeBonusAction?.Invoke();
        m_fuseEffect.Stop();
    }


    public override void RemoveBonus()
    {
        if(m_owner == null) { return; }
        m_removeBonusAction?.Invoke();
        m_owner.OnAttackLanded.RemoveListener(AttackLanded);
        m_owner = null;
        m_beatSystem = null;
        m_data = null;
        GameObject.Destroy(m_fuseEffect.gameObject);
        GameObject.Destroy(m_explosionEffect.gameObject);
        m_removeBonusAction = null;
    }
}
