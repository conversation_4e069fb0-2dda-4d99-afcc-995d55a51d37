using System;
using System.Collections.Generic;
using RibCageGames.Base;
using RibCageGames.Combat;
using RibCageGames.Music;
using UnityEngine;
using UnityEngine.Events;

/// <summary>
/// Delay A1 - Reverse Delay - continual buildup explosion
/// Focuses on damage buildup by prolonging the fuse for as long as the enemy is attacked while keeping damage buildup
/// Plays the delayed signal backward, creating a surreal, ethereal effect. Popular in experimental and ambient music.
/// </summary>

[CreateAssetMenu(fileName = "ReverseDelayPowerup", menuName = "RibCageGames/AlphaNomos/BonusEffects/ReverseDelayPowerup")]
public class ReverseDelayPowerup : DelayPowerup
{
    [SerializeField] private List<ReverseDelayPowerupLevel> m_reversePowerLevels;
    public override void UpgradeBonusLevel()
    {
        int currentLevel = m_playerService.GetPowerUpLevel(PowerUpType.DelayUpgrade);
        
        BonusEffect eb = new ReverseDelayBonus(m_reversePowerLevels[currentLevel], m_powerupLevel).ApplyBonusTo(m_playerService.PlayerController);
        
        ServiceLocator.Get<PlayerService>().AddBonusEffect(eb, this, m_powerupLevel);
    }
    
    [Serializable]
    public class ReverseDelayPowerupLevel : DelayPowerupLevel {}
}

public class ReverseDelayBonus : DelayBonus
{
    private int m_beatCount = 0;
    public ReverseDelayBonus(DelayPowerup.DelayPowerupLevel level, int powerupLevel) : base(level, powerupLevel)
    {
    }

    protected override void StartFuse(Action onComplete)
    {
        UnityAction<bool> beatAction = null;
        m_beatCount = 0;

        m_removeBonusAction?.Invoke();

        m_beatSystem.SetEffectValue(EffectType.Delay, m_level);

        beatAction = (_) =>
        {
            m_beatCount++;
            m_soundService.PlayClip(m_beatCount > m_data.BeatDelay
                ? m_data.ActivationSoundEffect
                : m_data.TickSoundEffect);

            if (m_beatCount > m_data.BeatDelay)
            {
                m_fuseEffect.Stop();
                m_explosionEffect.Play();

                onComplete?.Invoke();

                StopFuse();
            }
        };

        m_beatSystem.OnBeat.AddListener(beatAction);

        m_removeBonusAction = () =>
        {
            m_beatSystem.OnBeat.RemoveListener(beatAction);
            m_monoService.OnUpdate.RemoveListener(MoveFuseEffect);
            m_enemyHit = null;
            m_removeBonusAction = null;
            m_beatSystem.SetEffectValue(EffectType.Delay, 0);
        };
    }

    protected override void AttackLanded(ActionData move, List<(DamageableRegistry.IDamageable damageable, float damage)> enemiesHit)
    {
        if (m_enemyHit != null)
        {
            m_beatCount = 0;
            return;
        }

        base.AttackLanded(move, enemiesHit);
    }
}