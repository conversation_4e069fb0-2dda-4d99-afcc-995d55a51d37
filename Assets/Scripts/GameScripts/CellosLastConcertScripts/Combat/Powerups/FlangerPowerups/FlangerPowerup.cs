using System;
using System.Collections.Generic;
using System.Linq;
using RibCageGames.Base;
using RibCageGames.Combat;
using RibCageGames.Input;
using RibCageGames.Music;
using RibCageGames.UI;
using UnityEngine;

[CreateAssetMenu(fileName = "FlangerPowerup", menuName = "RibCageGames/AlphaNomos/BonusEffects/FlangerPowerup")]
public class FlangerPowerup : Powerup
{
    [SerializeField] private List<FlangerPowerupLevel> m_flangerPowerupLevels;
    [SerializeField] protected int m_powerupLevel;

    public override PowerUpType Type => PowerUpType.Flanger;
    public override bool IsNamedPowerup => true;
    protected PlayerService m_playerService;
    
    public override void Initialize()
    {
        m_playerService = ServiceLocator.Get<PlayerService>();
    }
    
    //Refactor this into base powerup of some sort that is only reverb, flanger, daly etc
    public override void SelectPowerup()
    {
        if (m_playerService.PowerUpLevelDict.TryGetValue(Type, out (int, BonusEffect, Powerup currentPowerup) data))
        {
            if (ServiceLocator.Get<PopupService>().TryGetElement(out PowerupEvolutionSelectionScreen selectionScreen))
            {
                //Debug.LogError($"Trigger evolutions for {data.currentPowerup} with {data.currentPowerup.PowerupEvolutions.Count}");

                selectionScreen.SetPowerupOptions(
                    data.currentPowerup.PowerupEvolutions
                        .Select<Powerup, (Powerup, Action)>(x => (x, x.UpgradeBonusLevel))
                        .ToList());
            }
        }
        else
        {
            UpgradeBonusLevel();
        }
    }
    
    public override void UpgradeBonusLevel()
    {
        int currentLevel = m_playerService.GetPowerUpLevel(PowerUpType.FlangerUpgrade);
        
        BonusEffect eb = new FlangerBonus(
            m_flangerPowerupLevels[currentLevel],
            m_playerService.PlayerController.FlangerVFX, m_powerupLevel).ApplyBonusTo(m_playerService.PlayerController);
        
        ServiceLocator.Get<PlayerService>().AddBonusEffect(eb, this, m_powerupLevel);
    }
    
    [Serializable]
    public class FlangerPowerupLevel
    {
        [SerializeField] private float m_damageBonusMinimum;
        [SerializeField] private float m_damageBonusMaximum;
        [SerializeField] private int m_cycleSteps;
        protected bool m_effectActive;
        public virtual int CycleSteps => m_cycleSteps;
        public float DamageBonusMinimum => m_damageBonusMinimum;
        public float DamageBonusMaximum => m_damageBonusMaximum;
        public bool EffectActive => m_effectActive;

        public void Initialize() { }

        public virtual float DamageMultiplier(int currentStep)
        {
            m_effectActive = currentStep > (CycleSteps / 2f);
            return Mathf.Lerp(m_damageBonusMinimum, m_damageBonusMaximum, (currentStep + 1f) / m_cycleSteps);
        }
    }
}

public class FlangerBonus : BonusEffect
{
    public override PowerUpType BonusType => PowerUpType.Flanger;

    protected CharacterControllerBase m_owner = null;
    protected FlangerPowerup.FlangerPowerupLevel m_data;
    private VisualEffectHolder m_particleEffect;
    private int m_level;
    protected BeatSystem m_beatSystem;

    protected int m_currentCycleSteps;
    
    public override float FillAmount =>
        (m_currentCycleSteps + 1f) / m_data.CycleSteps;
    
    public FlangerBonus(FlangerPowerup.FlangerPowerupLevel level, VisualEffectHolder effectReference, int effectiveLevel)
    {
        m_particleEffect = effectReference;
        m_data = level;
        m_level = effectiveLevel;
        m_beatSystem = ServiceLocator.Get<BeatSystem>();
    }

    public virtual BonusEffect ApplyBonusTo(CombatEntity target)
    {    
        m_owner = (CharacterControllerBase) target;
        
        m_owner.OnMoveStarted.AddListener(ApplyFlanger);
        
        return this;
    }

    protected void SetEffectState(bool activate)
    {
        if (activate)
        {
            m_beatSystem.SetEffectValue(EffectType.Flanger, m_level);
            m_particleEffect?.Play();
        }
        else
        {
            m_beatSystem.SetEffectValue(EffectType.Flanger, 0);
            m_particleEffect?.Stop();
        }
    }
    
    protected virtual void ApplyFlanger(ActionData move, PlayerInputActions initiatingAction)
    {
        if (move.HitBoxCount < 1) { return; }
        
        ChangeBonus(true);
    }

    protected virtual void ChangeBonus(bool activate)
    {
        if (activate)
        {
            m_currentCycleSteps = (m_currentCycleSteps + 1) % Mathf.Max(m_data.CycleSteps, 1);
            float boost = m_data.DamageMultiplier(m_currentCycleSteps);
            m_owner.ChangePowerUpValue(PowerUpType.Flanger,
                boost,
                true);
            SetEffectState(m_data.EffectActive);
        }
        else
        {
            m_owner.RemovePowerUp(PowerUpType.Flanger);
            SetEffectState(false);
        }
    }

    public override void RemoveBonus()
    {
        if(m_owner == null) { return; }
        m_owner.OnMoveStarted.RemoveListener(ApplyFlanger);
        m_beatSystem.SetEffectValue(EffectType.Flanger, 0);
        ChangeBonus(false);
        m_owner = null;
        m_data = null;
    }
}
