using System;
using System.Collections.Generic;
using System.Threading;
using RibCageGames.Base;
using RibCageGames.Combat;
using RibCageGames.Input;
using UnityEngine;

/// <summary>
/// Flanger A2 - Stereo Flanger - fixed and intricate pattern of damage boosts on swings restarts on combo start
/// A stereo flanger processes the left and right channels independently, often with slightly different delay times or modulation waveforms. This creates a wider and more spatial flanging effect that can pan and move across the stereo field.
/// </summary>

[CreateAssetMenu(fileName = "StereoFlangerPowerup", menuName = "RibCageGames/AlphaNomos/BonusEffects/StereoFlangerPowerup")]
public class StereoFlangerPowerup : FlangerPowerup
{
    [SerializeField] private List<StereoFlangerPowerupLevel> m_stereoFowerLevels;
    public override void UpgradeBonusLevel()
    {
        int currentLevel = m_playerService.GetPowerUpLevel(PowerUpType.FlangerUpgrade);
        
        BonusEffect eb = new StereoFlangerBonus(m_stereoFowerLevels[currentLevel],
            m_playerService.PlayerController.FlangerVFX,
            m_powerupLevel).ApplyBonusTo(m_playerService.PlayerController);
        
        ServiceLocator.Get<PlayerService>().AddBonusEffect(eb, this, m_powerupLevel);
    }

    [Serializable]
    public class StereoFlangerPowerupLevel : FlangerPowerupLevel
    {
        [SerializeField] private float[] m_damagePattern;
        [SerializeField] private float m_effectThreshold;
        public override int CycleSteps => m_damagePattern.Length;
        public override float DamageMultiplier(int currentStep)
        {
            m_effectActive = m_damagePattern[currentStep] > m_effectThreshold;
            return m_damagePattern[currentStep];
        }
    }
}

public class StereoFlangerBonus : FlangerBonus
{
    private CancellationTokenSource m_processSource;
    public StereoFlangerBonus(FlangerPowerup.FlangerPowerupLevel level, VisualEffectHolder effectReference, int powerupLevel) : base(level, effectReference, powerupLevel)
    {
    }
    
    protected override async void ApplyFlanger(ActionData move, PlayerInputActions initiatingAction)
    {
        if (move.HitBoxCount < 1) { return; }
        
        ChangeBonus(true);

        await m_owner.WaitUntilComboEnd();

        ChangeBonus(false);
        m_currentCycleSteps = 0;
    }
}