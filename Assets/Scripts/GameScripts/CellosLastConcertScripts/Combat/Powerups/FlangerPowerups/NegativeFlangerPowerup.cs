using System;
using System.Collections.Generic;
using RibCageGames.Base;
using RibCageGames.Combat;
using UnityEngine;

/// <summary>
/// Flanger A - Negative Flanger - Rigid pattern with less variation
/// strong weak and regular attacks in sequence
/// Instead of adding the delayed signal, a negative flanger _subtracts_ it. This results in a more subtle and sometimes hollow or notched sound compared to a positive flanger. The movement of the frequency notches can create a different kind of swirling effect.
/// </summary>

// [CreateAssetMenu(fileName = "NegativeFlangerPowerup", menuName = "RibCageGames/AlphaNomos/BonusEffects/NegativeFlangerPowerup")]
public class NegativeFlangerPowerup : FlangerPowerup
{
    [SerializeField] private List<NegativeFlangerPowerupLevel> m_negativePowerLevels;
    public override void UpgradeBonusLevel()
    {
        int currentLevel = m_playerService.GetPowerUpLevel(PowerUpType.FlangerUpgrade);
        
        BonusEffect eb = new NegativeFlangerBonus(m_negativePowerLevels[currentLevel],
            m_playerService.PlayerController.FlangerVFX,
            m_powerupLevel).ApplyBonusTo(m_playerService.PlayerController);
        
        ServiceLocator.Get<PlayerService>().AddBonusEffect(eb, this, m_powerupLevel);
    }

    [Serializable]
    public class NegativeFlangerPowerupLevel : FlangerPowerupLevel
    {
        [SerializeField] private List<float> m_damageBoostValues;
        
        [SerializeField] private float m_effectThreshold;
        public override int CycleSteps => m_damageBoostValues.Count;
        
        public override float DamageMultiplier(int currentStep)
        {
            m_effectActive = m_damageBoostValues[currentStep] > m_effectThreshold;
            return m_damageBoostValues[currentStep];
        }
    }
}

public class NegativeFlangerBonus : FlangerBonus
{
    public NegativeFlangerBonus(FlangerPowerup.FlangerPowerupLevel level, VisualEffectHolder effectReference, int powerupLevel) : base(level, effectReference, powerupLevel)
    {
    }
}