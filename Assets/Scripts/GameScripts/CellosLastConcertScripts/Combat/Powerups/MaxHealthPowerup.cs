using System;
using System.Collections.Generic;
using RibCageGames.Base;
using RibCageGames.Combat;
using UnityEngine;

[CreateAssetMenu(fileName = "MaxHealthPowerup", menuName = "RibCageGames/AlphaNomos/BonusEffects/MaxHealthPowerup")]
public class MaxHealthPowerup : Powerup
{
    [SerializeField] private List<MaxHealthPowerupLevel> m_maxHealthLevels;
    
    public override PowerUpType Type => PowerUpType.MaxHealth;
    public override bool IsNamedPowerup => false;
    private readonly ServiceReference<PlayerService> m_playerService = new();
    
    //Refactor this into base powerup of some sort that is only reverb, flanger, daly etc
    public override void SelectPowerup()
    {
        UpgradeBonusLevel();
    }

    public override void UpgradeBonusLevel()
    {
        int currentLevel = m_playerService.Value.GetPowerUpLevel(PowerUpType.MaxHealth);
        int effectiveLevel = Mathf.Clamp(currentLevel+ 1, 0, m_maxHealthLevels.Count - 1);
        
        BonusEffect eb = new MaxHealthBonus(
            m_maxHealthLevels[effectiveLevel],
            m_maxHealthLevels[0]).ApplyBonusTo(m_playerService.Value.PlayerController);
        
        m_playerService.Value.AddBonusEffect(eb, this, effectiveLevel);
    }

    [Serializable]
    public class MaxHealthPowerupLevel
    {
        [SerializeField] private int m_maxHealth;

        public int MaxHealth => m_maxHealth;
    }
}

public class MaxHealthBonus : BonusEffect
{
    public override PowerUpType BonusType => PowerUpType.MaxHealth;
    
    private MaxHealthPowerup.MaxHealthPowerupLevel m_data;
    private MaxHealthPowerup.MaxHealthPowerupLevel m_baseData;
    
    private readonly ServiceReference<PlayerService> m_playerService = new();
    
    public MaxHealthBonus(MaxHealthPowerup.MaxHealthPowerupLevel level, MaxHealthPowerup.MaxHealthPowerupLevel baseLevel)
    {
        m_data = level;
        m_baseData = baseLevel;
    }

    public virtual BonusEffect ApplyBonusTo(CombatEntity target)
    {
        target.SetMaximalHealth(m_data.MaxHealth);
        return this;
    }

    public override void RemoveBonus()
    {
        m_playerService.Value.PlayerController.SetMaximalHealth(m_baseData.MaxHealth);
    }
}
