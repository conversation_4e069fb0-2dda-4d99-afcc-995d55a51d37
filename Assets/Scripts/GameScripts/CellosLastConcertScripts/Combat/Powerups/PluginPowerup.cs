using System;
using System.Collections.Generic;
using RibCageGames.Base;
using RibCageGames.Combat;
using UnityEngine;

[CreateAssetMenu(fileName = "PluginPowerup", menuName = "RibCageGames/AlphaNomos/BonusEffects/PluginPowerup")]
public class PluginPowerup : Powerup
{
    [SerializeField] private List<ActionDataModifier> m_pluginMods;
    
    public override PowerUpType Type => PowerUpType.Plugin;
    public override bool IsNamedPowerup => true;

    private PlayerService m_playerService;
    
    public override void Initialize()
    {
        m_playerService = ServiceLocator.Get<PlayerService>();
    }
    
    //Refactor this into base powerup of some sort that is only reverb, flanger, daly etc
    public override void SelectPowerup()
    {
        UpgradeBonusLevel();
    }
    
    public override void UpgradeBonusLevel()
    {
        PluginBonus eb = new PluginBonus(m_pluginMods);
        ServiceLocator.Get<PlayerService>().AddBonusEffect(eb, this, 1);
    }
}

public class PluginBonus : BonusEffect
{
    public override PowerUpType BonusType => PowerUpType.Plugin;
    
    private List<ActionDataModifier> m_pluginMods;

    public PluginBonus(List<ActionDataModifier> pluginMods)
    {
        m_pluginMods = pluginMods;
    }

    public virtual BonusEffect ApplyBonusTo(CombatEntity target)
    {
        foreach (ActionDataModifier mod in m_pluginMods)
        {
            mod.ApplyModToTarget();
        }

        return this;
    }

    public override void RemoveBonus()
    {
        foreach (ActionDataModifier mod in m_pluginMods)
        {
            mod.RevertMod();
        }
    }
}
