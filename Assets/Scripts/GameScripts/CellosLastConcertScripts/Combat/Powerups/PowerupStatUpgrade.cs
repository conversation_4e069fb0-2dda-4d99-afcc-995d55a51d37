using UnityEngine;
using System;
using System.Collections.Generic;
using System.Linq;
using RibCageGames.Base;
using RibCageGames.Combat;
using RibCageGames.UI;

[CreateAssetMenu(fileName = "PowerupStatUpgrade", menuName = "RibCageGames/AlphaNomos/BonusEffects/PowerupStatUpgrade")]
public class PowerupStatUpgrade : Powerup
{
    public override PowerUpType Type => PowerUpType.PowerupUpgrade;
    public override bool IsNamedPowerup => false;

    private readonly ServiceReference<PlayerService> m_playerService = new();
    private readonly ServiceReference<PopupService> m_popupService = new();

    public override void SelectPowerup()
    {
        if (m_popupService.Value.TryGetElement(out PowerupEvolutionSelectionScreen selectionScreen))
        {
            List<Powerup> powerups = m_playerService.Value.PowerUpLevelDict.Values
                .Select(x => x.Item3)
                .Where(p => p.IsNamedPowerup)
                .ToList();
            
            selectionScreen.SetPowerupOptions(
                powerups
                    .Select<Powerup, (Powerup, Action)>(
                        x => (x, () =>
                        {
                            switch (x.Type)
                            {
                                case PowerUpType.Delay:
                                    m_playerService.Value.AddBonusEffect(
                                        new StatUpgradeBonus(PowerUpType.DelayUpgrade),
                                        this, m_playerService.Value.GetPowerUpLevel(PowerUpType.DelayUpgrade) + 1);
                                    break;
                                case PowerUpType.Flanger:
                                    m_playerService.Value.AddBonusEffect(
                                        new StatUpgradeBonus(PowerUpType.FlangerUpgrade),
                                        this, m_playerService.Value.GetPowerUpLevel(PowerUpType.FlangerUpgrade) + 1);
                                    break;
                                case PowerUpType.Reverb:
                                    m_playerService.Value.AddBonusEffect(
                                        new StatUpgradeBonus(PowerUpType.ReverbUpgrade),
                                        this, m_playerService.Value.GetPowerUpLevel(PowerUpType.ReverbUpgrade) + 1);
                                    break;
                            }
                            
                        }))
                    .ToList());
        }
    }
    
    public override void UpgradeBonusLevel() {}
}

public class StatUpgradeBonus : BonusEffect
{
    private PowerUpType m_powerupType;
    
    public override PowerUpType BonusType => m_powerupType;
    
    public StatUpgradeBonus(PowerUpType type)
    {
        m_powerupType = type;
    }

    public virtual BonusEffect ApplyBonusTo(CombatEntity target)
    {
        return this;
    }

    public override void RemoveBonus()
    {
    }
}

