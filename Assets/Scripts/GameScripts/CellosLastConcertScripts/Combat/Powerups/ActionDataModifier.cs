using System;
using System.Collections.Generic;
using RibCageGames.Animation;
using RibCageGames.Base;
using RibCageGames.Combat;
using RibCageGames.Editor;
using RibCageGames.MonoUtils;
using UnityEngine;

//TODO: make base and beat based mods
[CreateAssetMenu(fileName = "Mod", menuName = "RibCageGames/ActionSystem/ActionDataModifier")]
public class ActionDataModifier : ScriptableObject
{
    [SerializeField] protected CombatEntity m_owner;
    [SerializeField] protected Animator m_animator;
    
    [SerializeField] private bool m_modifyBase;
    [SerializeField] [ConditionalField("m_modifyBase", true)]
    protected BaseModifier m_baseModifier;
    
    [SerializeField] private bool m_modifyTiming;
    [SerializeField] [ConditionalField("m_modifyTiming", true)]
    protected TimingModifier m_timingModifier;
    
    [SerializeField] private bool m_modifyAnimation;
    [SerializeField] [ConditionalField("m_modifyAnimation", true)]
    protected AnimationModifier m_animationModifier;
    
    [SerializeField] private bool m_modifyMovement;
    [SerializeField] [ConditionalField("m_modifyMovement", true)]
    protected MovementModifier m_movementModifier;
    
    [SerializeField] private bool m_modifyHitboxes;
    [SerializeField] [ConditionalField("m_modifyHitboxes", true)]
    protected BeatActionHitBoxList m_hitboxesModifier;

    [SerializeField] private bool m_modifyEffects;
    [SerializeField][ConditionalField("m_modifyEffects", true)]
    protected ActionEffectList m_effectsModifier;
    
    [SerializeField] private bool m_modifySoundEffects;
    [SerializeField][ConditionalField("m_modifySoundEffects", true)]
    protected SoundEffectList m_soundEffectModifier;
    
    [SerializeField] private bool m_modifyMoveEffects;
    [SerializeField][ConditionalField("m_modifyMoveEffects", true)]
    protected MoveEffectList m_moveEffectModifier;
    
    [SerializeField] private bool m_modifyFollowingOptions;
    [SerializeField][ConditionalField("m_modifyFollowingOptions", true)]
    protected FollowingOptionsModifier m_followingOptionsModifier;
    
    [SerializeField] private bool m_modifySnapping;
    [SerializeField][ConditionalField("m_modifySnapping", true)]
    protected SnappingModifier m_snappingModifier;

    [SerializeField] private BeatBasedActionData m_target;

    public void ApplyModToTarget()
    {
        //Debug.LogError($"ApplyModToTarget");
        if (m_modifyBase)
        {
            m_target.DisplayName = m_baseModifier.displayNameModifier;
            m_target.Ariel = m_baseModifier.arielModifier;
            m_target.MoveDamage = m_baseModifier.moveDamageModifier;
        }
        
        if (m_modifyTiming)
        {
            m_target.MoveDuration = m_timingModifier.moveDurationModifier;
            m_target.MinimalMoveTime = m_timingModifier.minimalMoveTimeModifier;
        }
        
        if (m_modifyAnimation)
        {
            m_target.AnimatorParameters = m_animationModifier.animatorParametersModifier.CopyFromByValue();
        }

        if (m_modifyMovement)
        {
            m_target.AnimationMotionModifier = m_movementModifier.rootMotionModifierModifier;
            m_target.TweenMotionModifier = m_movementModifier.tweenMotionModifierModifier;
            m_target.MoveTween = m_movementModifier.moveTweenModifier;
        }

        if (m_modifyHitboxes)
        {
            m_target.Hitboxes = m_hitboxesModifier.hitboxes.CopyFromByValue();
            m_target.BeatHitboxes = m_hitboxesModifier.beatHitboxes.CopyFromByValue();
        }
        
        if (m_modifyEffects)
        {
            m_target.Effects = m_effectsModifier.effects.CopyFromByValue();
            m_target.BeatEffects = m_effectsModifier.beatEffects.CopyFromByValue();
        }
        
        if (m_modifySoundEffects)
        {
            m_target.SoundEffects = m_soundEffectModifier.soundEffects.CopyFromByValue();
            m_target.BeatSounds = m_soundEffectModifier.beatSoundEffects.CopyFromByValue();
        }
        
        if (m_modifyMoveEffects)
        {
            m_target.ActionMoveEffects = m_moveEffectModifier.moveEffects.CopyFromByValue();
            m_target.BeatMoveEffects = m_moveEffectModifier.beatMoveEffects.CopyFromByValue();
        }

        if (m_modifyFollowingOptions)
        {
            m_target.FollowingActions = m_followingOptionsModifier.followingOptions.CopyFromByValue();
            m_target.BeatFollowingOptions = m_followingOptionsModifier.beatFollowingOptions.CopyFromByValue();
        }
        
        if (m_modifySnapping)
        {
            m_target.FullSnapRadius = m_snappingModifier.fullSnapRadiusModifier;
            m_target.LightSnapAngle = m_snappingModifier.lightSnapAngleModifier;
            m_target.LightSnapRadius = m_snappingModifier.lightSnapRadiusModifier;
            m_target.IgnoreHeight = m_snappingModifier.ignoreHeightModifier;
        }
    }
    
    public void RevertMod()
    {
        if (m_modifyBase)
        {
            m_target.DisplayName = m_target.OriginalDisplayName;
            m_target.Ariel = m_target.OriginalAriel;
            m_target.MoveDamage = m_target.OriginalMoveDamage;
        }
        
        if (m_modifyTiming)
        {
            m_target.MoveDuration = m_target.OriginalMoveDuration;
            m_target.MinimalMoveTime = m_target.OriginalMinimalMoveTime;
        }
        
        if (m_modifyAnimation)
        {
            m_target.AnimatorParameters = m_target.OriginalAnimatorParameters.CopyFromByValue();
        }

        if (m_modifyMovement)
        {
            m_target.AnimationMotionModifier = m_target.OriginalRootMotionModifier;
            m_target.TweenMotionModifier = m_target.OriginalTweenMotionModifier;
            m_target.MoveTween = m_target.OriginalMoveTween;
        }

        if (m_modifyHitboxes)
        {
            m_target.Hitboxes = m_target.OriginalHitboxes.CopyFromByValue();
            m_target.BeatHitboxes = m_target.OriginalBeatHitboxes.CopyFromByValue();
        }
        
        if (m_modifyEffects)
        {
            m_target.Effects = m_target.OriginalEffects.CopyFromByValue();
            m_target.BeatEffects = m_target.OriginalBeatEffects.CopyFromByValue();
        }
        
        if (m_modifySoundEffects)
        {
            m_target.SoundEffects = m_target.OriginalSoundEffects.CopyFromByValue();
            m_target.BeatSounds = m_target.OriginalBeatSounds.CopyFromByValue();
        }
        
        if (m_modifyMoveEffects)
        {
            m_target.ActionMoveEffects = m_target.OriginalActionMoveEffects.CopyFromByValue();
            m_target.BeatMoveEffects = m_target.OriginalBeatMoveEffects.CopyFromByValue();
        }

        if (m_modifyFollowingOptions)
        {
            m_target.FollowingActions = m_target.OriginalFollowingActions.CopyFromByValue();
            m_target.BeatFollowingOptions = m_target.OriginalBeatFollowingOptions.CopyFromByValue();
        }
        
        if (m_modifySnapping)
        {
            m_target.FullSnapRadius = m_target.OriginalFullSnapRadius;
            m_target.LightSnapAngle = m_target.OriginalLightSnapAngle;
            m_target.LightSnapRadius = m_target.OriginalLightSnapRadius;
            m_target.IgnoreHeight = m_target.OriginalIgnoreHeight;
        }
    }

    private void OnValidate()
    {
        //return;
        if (m_target)
        {
            m_owner = m_target.CombatEntityOwner;
            m_animator = m_owner.Animator;
        }
        else { return; }

        if (!m_baseModifier.Populated)
        {
            if (m_modifyBase)
            {
                m_baseModifier.CopyFromTarget(m_target);
            }
            else
            {
                m_baseModifier = null;
            }
        }

        if (!m_timingModifier.Populated)
        {
            if (m_modifyTiming)
            {
                m_timingModifier.CopyFromTarget(m_target);
            }
            else
            {
                m_timingModifier = null;
            }
        }

        if (!m_animationModifier.Populated)
        {
            if (m_modifyAnimation)
            {
                m_animationModifier.CopyFromTarget(m_target);
            }
            else
            {
                m_animationModifier = null;
            }
        }

        if (!m_movementModifier.Populated)
        {
            if (m_modifyMovement)
            {
                m_movementModifier.CopyFromTarget(m_target);
            }
            else
            {
                m_movementModifier = null;
            }
        }

        if (!m_hitboxesModifier.Populated)
        {
            if (m_modifyHitboxes)
            {
                m_hitboxesModifier.CopyFromTarget(m_target);
            }
            else
            {
                m_hitboxesModifier = null;
            }
        }

        if (!m_effectsModifier.Populated)
        {
            if (m_modifyEffects)
            {
                m_effectsModifier.CopyFromTarget(m_target);
            }
            else
            {
                m_effectsModifier = null;
            }
        }

        if (!m_soundEffectModifier.Populated)
        {
            if (m_modifySoundEffects)
            {
                m_soundEffectModifier.CopyFromTarget(m_target);
            }
            else
            {
                m_soundEffectModifier = null;
            }
        }

        if (!m_moveEffectModifier.Populated)
        {
            if (m_modifyMoveEffects)
            {
                m_moveEffectModifier.CopyFromTarget(m_target);
            }
            else
            {
                m_moveEffectModifier = null;
            }
        }

        if (!m_followingOptionsModifier.Populated)
        {
            if (m_modifyFollowingOptions)
            {
                m_followingOptionsModifier.CopyFromTarget(m_target);
            }
            else
            {
                m_followingOptionsModifier = null;
            }
        }

        if (!m_snappingModifier.Populated)
        {
            if (m_modifySnapping)
            {
                m_snappingModifier.CopyFromTarget(m_target);
            }
            else
            {
                m_snappingModifier = null;
            }
        }
    }
    
    [Serializable]
    protected class BaseModifier
    {
        [SerializeField] public string displayNameModifier;
        [SerializeField] public bool arielModifier;
        [SerializeField] public float moveDamageModifier;

        public void CopyFromTarget(BeatBasedActionData target)
        {
            displayNameModifier = target.OriginalDisplayName;
            arielModifier = target.OriginalAriel;
            moveDamageModifier = target.OriginalMoveDamage;
        }

        public bool Populated => !string.IsNullOrWhiteSpace(displayNameModifier);
    }
    
    [Serializable]
    protected class TimingModifier
    {
        [SerializeField] public float moveDurationModifier;
        [SerializeField] public float minimalMoveTimeModifier;
        
        public void CopyFromTarget(BeatBasedActionData target)
        {
            moveDurationModifier = target.OriginalMoveDuration;
            minimalMoveTimeModifier = target.OriginalMinimalMoveTime;
        }
        
        public bool Populated => moveDurationModifier > 0f || minimalMoveTimeModifier > 0f;
    }
    
    [Serializable]
    protected class AnimationModifier
    {
        [SerializeField][AnimatorParameterControl("m_animator")] public List<AnimatorParameterControl> animatorParametersModifier;
        [SerializeField] public float braceBeatOffsetModifier;
        
        public void CopyFromTarget(BeatBasedActionData target)
        {
            animatorParametersModifier = target.OriginalAnimatorParameters.CopyFromByValue();
        }
        
        public bool Populated => animatorParametersModifier.Count > 0 || braceBeatOffsetModifier > 0f;
    }
    
    [Serializable]
    protected class MovementModifier
    {
        [SerializeField] public Vector3 rootMotionModifierModifier;
        [SerializeField] public Vector3 tweenMotionModifierModifier;
        [SerializeField] public MonoTweenerSettings moveTweenModifier;
        
        public void CopyFromTarget(BeatBasedActionData target)
        {
            rootMotionModifierModifier = target.OriginalRootMotionModifier;
            tweenMotionModifierModifier = target.OriginalTweenMotionModifier;
            moveTweenModifier = target.OriginalMoveTween;
        }
        
        public bool Populated => rootMotionModifierModifier.sqrMagnitude > 0f || tweenMotionModifierModifier.sqrMagnitude > 0f || moveTweenModifier != null;
    }
    
    [Serializable]
    protected class ActionEffectList
    {
        [SerializeField][ComponentReferenceAccessor("m_owner", "m_particleSystemsParentIndex", typeof(GameObject))]
        public List<ActionData.ActionEffect> effects;
        [SerializeField][ComponentReferenceAccessor("m_owner", "m_particleSystemsParentIndex", typeof(GameObject))]
        public List<BeatBasedActionData.BeatActionEffect> beatEffects;
        
        public void CopyFromTarget(BeatBasedActionData target)
        {
            effects = target.OriginalEffects.CopyFromByValue();
            beatEffects = target.OriginalBeatEffects.CopyFromByValue();
        }
        
        public bool Populated => effects.Count > 0 || beatEffects.Count > 0;
    }
    
    [Serializable]
    protected class SoundEffectList
    {
        [SerializeField]
        public List<ActionData.ActionSoundEffect> soundEffects;
        [SerializeField]
        public List<BeatBasedActionData.BeatActionSoundEffect> beatSoundEffects;
        
        public void CopyFromTarget(BeatBasedActionData target)
        {
            soundEffects = target.OriginalSoundEffects.CopyFromByValue();
            beatSoundEffects = target.OriginalBeatSounds.CopyFromByValue();
        }
        
        public bool Populated => soundEffects.Count > 0 || beatSoundEffects.Count > 0;
    }
    
    [Serializable]
    protected class MoveEffectList
    {
        [SerializeField]
        public List<ActionData.ActionMoveEffect> moveEffects;
        [SerializeField]
        public List<BeatBasedActionData.BeatMoveEffect> beatMoveEffects;
        
        public void CopyFromTarget(BeatBasedActionData target)
        {
            moveEffects = target.OriginalActionMoveEffects.CopyFromByValue();
            beatMoveEffects = target.OriginalBeatMoveEffects.CopyFromByValue();
        }
        
        public bool Populated => moveEffects.Count > 0 || beatMoveEffects.Count > 0;
    }
    
    [Serializable]
    protected class FollowingOptionsModifier
    {
        [SerializeField]
        public List<ActionData.InputFollowingOption> followingOptions;
        [SerializeField]
        public List<BeatBasedActionData.BeatFollowingOption> beatFollowingOptions;
        
        public void CopyFromTarget(BeatBasedActionData target)
        {
            followingOptions = target.OriginalFollowingActions.CopyFromByValue();
            beatFollowingOptions = target.OriginalBeatFollowingOptions.CopyFromByValue();
        }
        
        public bool Populated => followingOptions.Count > 0 || beatFollowingOptions.Count > 0;
    }
    
    [Serializable]
    protected class SnappingModifier
    {
        [SerializeField]
        public float fullSnapRadiusModifier;
        [SerializeField]
        public float lightSnapRadiusModifier;
        [SerializeField]
        public float lightSnapAngleModifier;
        [SerializeField]
        public bool ignoreHeightModifier;
        
        public void CopyFromTarget(BeatBasedActionData target)
        {
            fullSnapRadiusModifier = target.OriginalFullSnapRadius;
            lightSnapRadiusModifier = target.OriginalLightSnapRadius;
            lightSnapAngleModifier = target.OriginalLightSnapRadius;
            ignoreHeightModifier = target.OriginalIgnoreHeight;
        }
        
        public bool Populated => fullSnapRadiusModifier > 0f || lightSnapRadiusModifier > 0f || lightSnapAngleModifier > 0f;
    }
    
    [Serializable]
    protected class BeatActionHitBoxList
    {
        [SerializeField][ComponentReferenceAccessor("m_owner", "m_hitBoxColliderIndex", typeof(Collider))]
        public List<ActionData.ActionHitBox> hitboxes;
        [SerializeField][ComponentReferenceAccessor("m_owner", "m_hitBoxColliderIndex", typeof(Collider))]
        public List<BeatBasedActionData.BeatActionHitBox> beatHitboxes;
        
        public void CopyFromTarget(BeatBasedActionData target)
        {
            hitboxes = target.OriginalHitboxes.CopyFromByValue();
            beatHitboxes = target.OriginalBeatHitboxes.CopyFromByValue();
        }
        
        public bool Populated => hitboxes.Count > 0 || beatHitboxes.Count > 0;
    }
}
