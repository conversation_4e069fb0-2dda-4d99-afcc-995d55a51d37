using UnityEngine;

[CreateAssetMenu(fileName = "RunCurrencyPowerup", menuName = "RibCageGames/AlphaNomos/BonusEffects/RunCurrencyPowerup")]
public class RunCurrencyPowerup : Powerup
{
    [SerializeField] private int m_amount; 
    private ServiceReference<InventoryService> m_inventoryService = new();
    
    public override PowerUpType Type => PowerUpType.RunCurrency;
    public override bool IsNamedPowerup => false;
    private readonly ServiceReference<PlayerService> m_playerService = new();
    
    //Refactor this into base powerup of some sort that is only reverb, flanger, daly etc
    public override void SelectPowerup()
    {
        m_inventoryService.Value.AddRunCurrency(m_amount, m_playerService.Value.PlayerController.transform.position);
    }
}