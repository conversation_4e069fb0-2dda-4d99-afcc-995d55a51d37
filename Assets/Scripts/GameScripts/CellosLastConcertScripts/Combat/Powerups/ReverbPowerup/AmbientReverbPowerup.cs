using System;
using System.Collections.Generic;
using RibCageGames.Base;
using RibCageGames.Combat;
using RibCageGames.Input;
using UnityEngine;

/// <summary>
/// Reverb B1 - Ambient Reverb - on beat attacks recover charge
/// Designed for subtle and natural reverb that enhances space without standing out. Common in modern pop and film scoring.
/// </summary>

[CreateAssetMenu(fileName = "AmbientReverbPowerup", menuName = "RibCageGames/AlphaNomos/BonusEffects/AmbientReverbPowerup")]
public class AmbientReverbPowerup : ReverbPowerup
{
    [SerializeField] private List<AmbientReverbPowerupLevel> m_ambientPowerLevels;
    public override void UpgradeBonusLevel()
    {
        int currentLevel = m_playerService.GetPowerUpLevel(PowerUpType.ReverbUpgrade);
        
        BonusEffect eb = new AmbientReverbBonus(
            m_ambientPowerLevels[currentLevel],
            m_playerService.PlayerController.ReverbVFX, m_powerupLevel).ApplyBonusTo(m_playerService.PlayerController);
        
        ServiceLocator.Get<PlayerService>().AddBonusEffect(eb, this, m_powerupLevel);
    }

    [Serializable]
    public class AmbientReverbPowerupLevel : ReverbPowerupLevel
    {
        [SerializeField] private float m_onBeatStorageBoost;
        
        public float OnBeatStorageBoost => m_onBeatStorageBoost;
    }
}

public class AmbientReverbBonus : ReverbBonus
{
    public AmbientReverbBonus(ReverbPowerup.ReverbPowerupLevel level, VisualEffectHolder effectReference, int powerupLevel) : base(level, effectReference, powerupLevel)
    {
    }

    protected override void ApplyReverb(ActionData move, PlayerInputActions initiatingAction)
    {
        base.ApplyReverb(move, initiatingAction);
        if (m_owner.CurrentMoveJust)
        {
            m_currentStorage += ((AmbientReverbPowerup.AmbientReverbPowerupLevel) m_data).OnBeatStorageBoost;
        }
    }
}