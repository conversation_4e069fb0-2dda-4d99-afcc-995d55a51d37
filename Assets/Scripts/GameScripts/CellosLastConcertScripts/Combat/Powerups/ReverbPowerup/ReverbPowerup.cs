using System;
using System.Collections.Generic;
using System.Linq;
using RibCageGames.Base;
using RibCageGames.Combat;
using RibCageGames.Editor;
using RibCageGames.Input;
using RibCageGames.Music;
using RibCageGames.UI;
using UnityEngine;

[CreateAssetMenu(fileName = "ReverbPowerup", menuName = "RibCageGames/AlphaNomos/BonusEffects/ReverbPowerup")]
public class ReverbPowerup : Powerup
{
    [SerializeField] protected int m_powerupLevel;
    [SerializeField] private List<ReverbPowerupLevel> m_reverbPowerupLevels;

    public override PowerUpType Type => PowerUpType.Reverb;
    public override bool IsNamedPowerup => true;

    protected PlayerService m_playerService;
    
    public override void Initialize()
    {
        m_playerService = ServiceLocator.Get<PlayerService>();
    }
    
    //Refactor this into base powerup of some sort that is only reverb, flanger, daly etc
    public override void SelectPowerup()
    {
        if (m_playerService.PowerUpLevelDict.TryGetValue(Type, out (int, BonusEffect, Powerup currentPowerup) data))
        {
            if (ServiceLocator.Get<PopupService>().TryGetElement(out PowerupEvolutionSelectionScreen selectionScreen))
            {
                //Debug.LogError($"Trigger evolutions for {data.currentPowerup} with {data.currentPowerup.PowerupEvolutions.Count}");
                selectionScreen.SetPowerupOptions(
                    data.currentPowerup.PowerupEvolutions
                        .Select<Powerup, (Powerup, Action)>(x => (x, x.UpgradeBonusLevel))
                        .ToList());
            }
        }
        else
        {
            UpgradeBonusLevel();
        }
    }
    
    public override void UpgradeBonusLevel()
    {
        int currentLevel = m_playerService.GetPowerUpLevel(PowerUpType.ReverbUpgrade);
        
        BonusEffect eb = new ReverbBonus(
            m_reverbPowerupLevels[currentLevel],
            m_playerService.PlayerController.ReverbVFX, m_powerupLevel).ApplyBonusTo(m_playerService.PlayerController);
        
        m_playerService.AddBonusEffect(eb, this, m_powerupLevel);
    }
    
    [Serializable]
    private class ParticleSystemReference
    {
        [SerializeField] private int m_particleSystemsParentIndex;
        public int ParticleSystemsParentIndex => m_particleSystemsParentIndex;
    }

    [Serializable]
    public class ReverbPowerupLevel
    {
        [SerializeField] private float m_damageMultiplier;
        [SerializeField] private float m_maxStorage;
        [SerializeField] private float m_replenishRate;
        [SerializeField] private float m_replenishDelay;
        
        public float DamageMultiplier => m_damageMultiplier;
        public float MaxStorage => m_maxStorage;
        public float ReplenishRate => m_replenishRate;
        public float ReplenishDelay => m_replenishDelay;
    }
}

public class ReverbBonus : BonusEffect
{
    public override PowerUpType BonusType => PowerUpType.Reverb;
    public override float FillAmount => m_currentStorage / m_data.MaxStorage;

    protected CharacterControllerBase m_owner = null;
    protected ReverbPowerup.ReverbPowerupLevel m_data;
    private VisualEffectHolder m_particleEffect;
    protected float m_currentStorage;
    protected MonoProcess m_replenishProcess;
    protected bool m_bonusApplied = false;
    protected int m_effectLevel;
    protected BeatSystem m_beatSystem;

    public ReverbBonus(ReverbPowerup.ReverbPowerupLevel level, VisualEffectHolder effectReference, int effectLevel)
    {
        m_particleEffect = effectReference;
        m_data = level;
        m_effectLevel = effectLevel;
        m_beatSystem = ServiceLocator.Get<BeatSystem>();
    }

    public virtual BonusEffect ApplyBonusTo(CombatEntity target)
    {    
        m_owner = (CharacterControllerBase) target;
        
        m_owner.OnMoveStarted.AddListener(ApplyReverb);
        m_owner.OnAttackLanded.AddListener(PlayEffect);
        
        m_owner.OnMoveEnded.AddListener(StartReplenish);
        m_owner.OnMoveCancelled.AddListener(StartReplenish);

        StartReplenish(null);

        return this;
    }

    protected void PlayEffect(ActionData move, List<(DamageableRegistry.IDamageable, float)> targets)
    {
        if (m_bonusApplied)
        {
            m_particleEffect.Play();   
        }
    }

    protected virtual void StartReplenish(ActionData move)
    {
        m_replenishProcess?.Stop();
        m_replenishProcess =
            MonoProcess.WaitForSecondsProcess(m_data.ReplenishDelay)
                .WaitUntil(() =>
                {
                    m_currentStorage += Time.deltaTime * m_data.ReplenishRate;
                    m_currentStorage = Mathf.Clamp(m_currentStorage, 0f, m_data.MaxStorage);
                    return m_currentStorage >= m_data.MaxStorage;
                });
    }

    protected virtual void ApplyReverb(ActionData move, PlayerInputActions initiatingAction)
    {
        if (move.HitBoxCount < 1) { return; }
        
        m_replenishProcess?.Stop();
        m_currentStorage -= 1f;
        if (m_currentStorage > 0f)
        {
            ChangeBonus(true);
        }
        else
        {
            ChangeBonus(false);            
        }

        m_currentStorage = Mathf.Clamp(m_currentStorage, 0f, m_data.MaxStorage);
    }

    protected virtual void ChangeBonus(bool activate)
    {
        if (activate)
        {
            m_beatSystem.SetEffectValue(EffectType.Reverb, m_effectLevel);
            m_bonusApplied = true;
            m_owner.ChangePowerUpValue(PowerUpType.Reverb, m_data.DamageMultiplier);
        }
        else
        {
            m_beatSystem.SetEffectValue(EffectType.Reverb, 0);
            m_bonusApplied = false;
            m_owner.RemovePowerUp(PowerUpType.Reverb);
        }
    }

    public override void RemoveBonus()
    {
        if(m_owner == null) { return; }
        m_beatSystem.SetEffectValue(EffectType.Reverb, 0);
        m_owner.OnMoveStarted.RemoveListener(ApplyReverb);
        m_owner.OnAttackLanded.RemoveListener(PlayEffect);
        m_owner.OnMoveEnded.RemoveListener(StartReplenish);
        m_owner.OnMoveCancelled.RemoveListener(StartReplenish);
        m_replenishProcess?.Stop();
        m_owner = null;
        m_data = null;
    }
}
