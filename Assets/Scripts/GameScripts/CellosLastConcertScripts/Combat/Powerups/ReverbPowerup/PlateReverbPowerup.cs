using System;
using System.Collections.Generic;
using RibCageGames.Base;
using RibCageGames.Combat;
using RibCageGames.Input;
using UnityEngine;

/// <summary>
/// Reverb A - Plate Reverb
/// More focused on using the buildup well
/// Single usage of reverb when fully charged, after starting smoothly drops over a duration
/// Uses a vibrating metal plate to generate a rich, dense reverb. Known for its smooth decay and warm tone, it’s popular for vocals and drums.
/// </summary>

[CreateAssetMenu(fileName = "PlateReverbPowerup", menuName = "RibCageGames/AlphaNomos/BonusEffects/PlateReverbPowerup")]
public class PlateReverbPowerup : ReverbPowerup
{
    [SerializeField] private List<PlateReverbPowerupLevel> m_platePowerLevels;
    public override void UpgradeBonusLevel()
    {
        int currentLevel = m_playerService.GetPowerUpLevel(PowerUpType.ReverbUpgrade);
        
        BonusEffect eb = new PlateReverbBonus(
            m_platePowerLevels[currentLevel],
            m_playerService.PlayerController.ReverbVFX, m_powerupLevel).ApplyBonusTo(m_playerService.PlayerController);
        
        ServiceLocator.Get<PlayerService>().AddBonusEffect(eb, this, m_powerupLevel);
    }

    [Serializable]
    public class PlateReverbPowerupLevel : ReverbPowerupLevel
    {
        [SerializeField] private float m_fadeDuration;
        
        public float FadeDuration => m_fadeDuration;
    }
}

public class PlateReverbBonus : ReverbBonus
{
    public override float FillAmount => Mathf.Max(
        m_currentStorage / m_data.MaxStorage, m_charge);

    private float m_charge;
    
    public PlateReverbBonus(ReverbPowerup.ReverbPowerupLevel level, VisualEffectHolder effectReference, int powerupLevel) : base(level, effectReference, powerupLevel)
    {
    }
    
    protected override void ApplyReverb(ActionData move, PlayerInputActions initiatingAction)
    {
        if (move.HitBoxCount < 1) { return; }
        
        if (m_currentStorage >= 0.99f * m_data.MaxStorage)
        {
            m_replenishProcess?.Stop();
            m_currentStorage = 0f;
            ChangeBonus(true);
            float maxCharge = ((PlateReverbPowerup.PlateReverbPowerupLevel)m_data).FadeDuration;
            m_charge = 1f;
            MonoProcess.New()
                .WaitUntil(() =>
                    {
                        m_charge -= Time.deltaTime / maxCharge;
                        m_owner.ChangePowerUpValue(PowerUpType.Reverb, m_data.DamageMultiplier * m_charge, true);
                        return m_charge < 0f;
                    }).Do(() =>
                {
                    ChangeBonus(false);
                    StartReplenish(null);
                });
        }
    }
    
    protected override void StartReplenish(ActionData move)
    {
        if(m_charge > 0f){ return; }
        base.StartReplenish(move);
    }
}