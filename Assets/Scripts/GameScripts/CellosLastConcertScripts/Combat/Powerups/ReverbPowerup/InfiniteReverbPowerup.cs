using System;
using System.Collections.Generic;
using RibCageGames.Base;
using RibCageGames.Combat;
using RibCageGames.Input;
using UnityEngine;

/// <summary>
/// Reverb A1 - Infinite Reverb - Charge persists throughout a combo
/// Less reverb damage that persists through the combo
/// Creates an endlessly sustained reverb tail, perfect for ambient drones and experimental textures.
/// </summary>

[CreateAssetMenu(fileName = "InfiniteReverbPowerup", menuName = "RibCageGames/AlphaNomos/BonusEffects/InfiniteReverbPowerup")]
public class InfiniteReverbPowerup : ReverbPowerup
{
    [SerializeField] private List<InfiniteReverbPowerupLevel> m_infinitePowerLevels;
    public override void UpgradeBonusLevel()
    {
        int currentLevel = m_playerService.GetPowerUpLevel(PowerUpType.ReverbUpgrade);
        
        BonusEffect eb = new InfiniteReverbBonus(
            m_infinitePowerLevels[currentLevel],
            m_playerService.PlayerController.ReverbVFX, m_powerupLevel).ApplyBonusTo(m_playerService.PlayerController);
        
        ServiceLocator.Get<PlayerService>().AddBonusEffect(eb, this, m_powerupLevel);
    }

    [Serializable]
    public class InfiniteReverbPowerupLevel : ReverbPowerupLevel {}
}

public class InfiniteReverbBonus : ReverbBonus
{
    public InfiniteReverbBonus(ReverbPowerup.ReverbPowerupLevel level, VisualEffectHolder effectReference, int powerupLevel) : base(level, effectReference, powerupLevel)
    {
    }
    
    protected override async void ApplyReverb(ActionData move, PlayerInputActions initiatingAction)
    {
        if (move.HitBoxCount < 1) { return; }
        
        m_replenishProcess?.Stop();
        if (m_currentStorage >= 0.99f * m_data.MaxStorage)
        {
            m_currentStorage = 0f;
            ChangeBonus(true);
            await m_owner.WaitUntilComboEnd();
            ChangeBonus(false);
        }
    }
}