using System;
using System.Collections.Generic;
using System.Threading;
using RibCageGames.Base;
using RibCageGames.Editor;
using RibCageGames.Input;
using RibCageGames.Music;
using UnityEngine;
using UnityEngine.Events;
using ActionData = RibCageGames.Combat.ActionData;

[CreateAssetMenu(fileName = "Action", menuName = "RibCageGames/CellosLastConcert/BeatBasedActionData")]
public class BeatBasedActionData : ActionData
{
    [SerializeField] protected internal List<BeatFollowingOption> m_beatFollowingOptions;
    
    [SerializeField]
    [ComponentReferenceAccessor("m_owner", "m_hitBoxColliderIndex", typeof(Collider))]
    protected List<BeatActionHitBox> m_beatHitboxes;
    
    [SerializeField][ComponentReferenceAccessor("m_owner", "m_particleSystemsParentIndex", typeof(GameObject))]
    protected List<BeatActionEffect> m_beatEffects;
    
    [SerializeField] protected internal List<BeatActionSoundEffect> m_beatSounds;
    [SerializeField] protected List<BeatMoveEffect> m_beatMoveEffects;
    [SerializeField] protected float m_minimalCancelTime = 0f;
    
    public virtual float MinimalCancelTime {
        get => r_minimalCancelTime;
    }
    
    public override int HitBoxCount => m_hitboxes.Count + m_beatHitboxes.Count;
    
    #region RuntimeValues

    protected List<BeatActionHitBox> r_beatHitboxes;
    protected internal List<BeatActionEffect> r_beatEffects;
    protected List<BeatActionSoundEffect> r_beatSounds;
    protected List<BeatMoveEffect> r_beatMoveEffects;
    protected List<BeatFollowingOption> r_beatFollowingOptions;
    protected float r_minimalCancelTime;
    
    #endregion
    
    #region Runtime accesors

    public virtual List<BeatActionHitBox> BeatHitboxes
    {
        get => r_beatHitboxes;
        set => r_beatHitboxes = value;
    }
    
    public virtual List<BeatActionEffect> BeatEffects
    {
        get => r_beatEffects;
        set => r_beatEffects = value;
    }
    
    public virtual List<BeatActionSoundEffect> BeatSounds
    {
        get => r_beatSounds;
        set => r_beatSounds = value;
    }
    
    public virtual List<BeatMoveEffect> BeatMoveEffects
    {
        get => r_beatMoveEffects;
        set => r_beatMoveEffects = value;
    }
    
    public virtual List<BeatFollowingOption> BeatFollowingOptions
    {
        get => r_beatFollowingOptions;
        set => r_beatFollowingOptions = value;
    }
    
    #endregion
    
    #region EditorTime accesors

    public virtual List<BeatActionHitBox> OriginalBeatHitboxes => m_beatHitboxes;
    public virtual List<BeatActionEffect> OriginalBeatEffects => m_beatEffects;
    public virtual List<BeatActionSoundEffect> OriginalBeatSounds => m_beatSounds;
    public virtual List<BeatMoveEffect> OriginalBeatMoveEffects => m_beatMoveEffects;
    public virtual List<BeatFollowingOption> OriginalBeatFollowingOptions => m_beatFollowingOptions;

    #endregion

    [NonSerialized] protected ServiceReference<BeatSystem> m_beatSystem = new();
    [NonSerialized] protected ServiceReference<PlayerService> m_playerService = new();

    protected override void UpdateRuntimeValues()
    {
        if (!Application.isPlaying)
        {
            return;
        }
        
        base.UpdateRuntimeValues();
        r_beatFollowingOptions = m_beatFollowingOptions.CopyFromByValue();
        r_beatHitboxes = m_beatHitboxes.CopyFromByValue();
        r_beatEffects = m_beatEffects.CopyFromByValue();
        r_beatSounds = m_beatSounds.CopyFromByValue();
        r_beatMoveEffects = m_beatMoveEffects.CopyFromByValue();
        r_minimalCancelTime = m_minimalCancelTime;
    }

    public virtual void PerformConsecutive(PerformParams performParams, bool isOnBeat, CancellationToken cancellationToken = default(CancellationToken), CancellationTokenSource cancelSource = null)
    {
        
        BeatPerformData performData = ConstructBeatPerformData();
        performData.isOnBeat = isOnBeat;
        //performData.previousMoveOnBeat = m_playerService.Value.PlayerController.CurrentMoveJust;
        performData.DirectHit = isOnBeat;
        performParams.initiatingPlayerAction = PlayerInputActions.Default;
        Perform(performParams, performData, cancellationToken, cancelSource);
    }
    
    public override void Perform(PerformParams performParams, CancellationToken cancellationToken = default(CancellationToken), CancellationTokenSource cancelSource = null)
    {
        BeatPerformData performData = ConstructBeatPerformData();
        Perform(performParams, performData, cancellationToken, cancelSource);
    }
    
    protected virtual BeatPerformData ConstructBeatPerformData()
    {
        return new BeatPerformData
        {
            moveDuration = MoveDuration,
            minimalMoveTime = MinimalMoveTime,
            animationMotionModifier = AnimationMotionModifier,
            tweenMotionModifier = TweenMotionModifier,
            moveTween = MoveTween,
            moveDamage = MoveDamage,
            animatorParameters = AnimatorParameters,
            hitboxes = Hitboxes,
            soundEffects = SoundEffects,
            effects = Effects,
            actionMoveEffects = ActionMoveEffects,
            followingActions = FollowingActions,
            fullSnapRadius = FullSnapRadius,
            lightSnapRadius = LightSnapRadius,
            lightSnapAngle = LightSnapAngle,
            
            isOnBeat = m_beatSystem.Value.IsOnBeat,
            DirectHit = m_beatSystem.Value.IsOnBeat,
            
            beatFollowingOptions = BeatFollowingOptions, 
            beatHitboxes = BeatHitboxes, 
            beatEffects = BeatEffects, 
            beatSounds = BeatSounds, 
            beatMoveEffects = BeatMoveEffects,
            
            inputTimeOffset = m_beatSystem.Value.CurrentBeatDistance,
        };
    }

    protected override void PerformFollowingOption(ActionData followingAction, PerformParams performParams, PerformProcess performProcess, PerformData performData, PlayerInputActions initiatingPlayerAction)
    {
        if (performProcess.transitioningToFollowup)
        {
            return;
        }
        
        performProcess.transitioningToFollowup = true;

        if (((CharacterControllerBase)performParams.owner).GracePeriodActive && m_beatSystem.Value.IsInGracePeriod && !m_beatSystem.Value.IsOnBeat)
        {
            MonoProcess.New().WaitUntil(() => m_beatSystem.Value.IsOnBeat).Do(() =>
            {
                if (performParams.owner.CanPerform(PlayerInputActions.Default, followingAction))
                {
                    ((CharacterControllerBase)performParams.owner).GracePeriodInEffect = true;
                    performParams.initiatingPlayerAction = initiatingPlayerAction;
                    performProcess.ProcessCancel.Invoke();
                    CancellationTokenSource source = new CancellationTokenSource();
                    followingAction.Perform(performParams, source.Token, cancelSource: source);
                }
            });
        }
        else
        {
            if (performParams.owner.CanPerform(PlayerInputActions.Default, followingAction))
            {
                ((CharacterControllerBase)performParams.owner).GracePeriodInEffect = false;
                performParams.initiatingPlayerAction = initiatingPlayerAction;
                performProcess.ProcessCancel.Invoke();
                CancellationTokenSource source = new CancellationTokenSource();
                followingAction.Perform(performParams, source.Token, cancelSource: source);
            }
        }
    }

    protected override Action RegisterFollowingActions(PerformParams performParams, PerformProcess performProcess,
        PerformData performData, MonoProcess onCompleteProcess, PlayerInputActions initiatingPlayerAction,
        CancellationToken cancellationToken)
    {
        performParams.prevoiousMoveJust = m_playerService.Value.PlayerController.CurrentMoveJust;
        Action baseUnregister = base.RegisterFollowingActions(performParams, performProcess, performData,
            onCompleteProcess, initiatingPlayerAction, cancellationToken);

        int fullBeatCount = 0;
        int halfBeatCount = 0;

        UnityAction<bool> beatAction = null;

        BeatPerformData beatPerformData = (BeatPerformData)performData;

        if (beatPerformData.beatFollowingOptions.Count < 1)
        {
            return baseUnregister;
        }

        MonoProcess timerProcess = MonoProcess.New(cancellationToken: cancellationToken)
            .WaitForSeconds(beatPerformData.moveDuration);

        MonoProcess delayProcess;

        BeatFollowingOption selectedOption = beatPerformData.beatFollowingOptions[0]; //More than 1 needed?

        if (selectedOption.BeatSync.BeatCount == 0)
        {
            delayProcess = MonoProcess.New(cancellationToken: cancellationToken)
                .WaitForSeconds(selectedOption.BeatSync.MinimalPerformTime);
            delayProcess.Do(() =>
            {
                try
                {
                    if (performParams.owner.CanPerform(PlayerInputActions.Default, selectedOption.Action)
                        && (selectedOption.RequiredHeldInput == PlayerInputActions.Default ||
                            ((InputVoidEvent)m_inputService.InGameInputs[selectedOption.RequiredHeldInput])
                            .CurrentValue))
                    {
                        if (performProcess.transitioningToFollowup)
                        {
                            return;
                        }

                        performProcess.transitioningToFollowup = true;
                        performParams.initiatingPlayerAction = initiatingPlayerAction;
                        performParams.prevoiousMoveJust = m_playerService.Value.PlayerController.CurrentMoveJust;
                        performProcess.ProcessCancel.Invoke();
                        CancellationTokenSource source = new CancellationTokenSource();
                        selectedOption.Action.PerformConsecutive(performParams, beatPerformData.isOnBeat, source.Token,
                            cancelSource: source);
                    }
                }
                catch (Exception e)
                {
                    Debug.LogError($"Broken beat based follow action on {name}");
                }
            });

            return baseUnregister;
        }
        else
        {
            InputVoidEvent holdAction =
                selectedOption.RequiredHeldInput != PlayerInputActions.Default
                    ? (InputVoidEvent)m_inputService.InGameInputs[selectedOption.RequiredHeldInput]
                    : null;
            bool keptHeld = selectedOption.RequiredHeldInput != PlayerInputActions.Default
                ? holdAction.CurrentValue
                : false;

            beatAction = (isFullBeat) =>
            {
                if (selectedOption.RequiredHeldInput != PlayerInputActions.Default)
                {
                    keptHeld = keptHeld && holdAction.CurrentValue;
                }

                if (timerProcess.ElapsedTime > selectedOption.BeatSync.MinimalPerformTime)// - performData.inputTimeOffset)
                {
                    //Make sure not to include same beat
                    if (timerProcess.ElapsedTime > m_beatSystem.Value.HalfBeatLength - (m_beatSystem.Value.InputWindow / 2f))
                    {
                        if (isFullBeat)
                        {

                            fullBeatCount++;
                        }

                        else
                        {
                            halfBeatCount++;
                        }
                    }


                    if (selectedOption.BeatSync.ShouldActivate(fullBeatCount, halfBeatCount)
                        && performParams.owner.CanPerform(PlayerInputActions.Default, selectedOption.Action)
                        && (selectedOption.RequiredHeldInput == PlayerInputActions.Default || keptHeld))
                    {

                        if (performProcess.transitioningToFollowup)
                        {
                            return;
                        }

                        performProcess.transitioningToFollowup = true;
                        performParams.initiatingPlayerAction = initiatingPlayerAction;
                        performParams.prevoiousMoveJust = m_playerService.Value.PlayerController.CurrentMoveJust;
                        performProcess.ProcessCancel.Invoke();
                        CancellationTokenSource source = new CancellationTokenSource();
                        selectedOption.Action.Perform(performParams, source.Token, cancelSource: source);
                    }
                }
            };

            m_beatSystem.Value.OnBeat.AddCancellableListener(beatAction, cancellationToken);
            return baseUnregister;
        }
    }

    protected override void AddHitboxes(PerformParams performParams, PerformProcess performProcess, PerformData performData, CancellationToken cancellationToken)
    {
        base.AddHitboxes(performParams, performProcess, performData, cancellationToken);

        BeatPerformData beatPerformData = (BeatPerformData) performData;
        
        if (beatPerformData.beatHitboxes.Count > 0)
        {
            foreach (BeatActionHitBox hitbox in beatPerformData.beatHitboxes)
            {
                MonoProcess beatProcess = MonoProcess.New(cancellationToken: cancellationToken);
                CountBeatsProcess(beatProcess, hitbox.BeatSync,
                    () =>
                    {
                        m_damageableRegistry.ApplyDamageArea(
                            performParams.hitboxColliders[hitbox.HitBoxColliderIndex],
                            performParams.owner.BaseDamage * hitbox.DamageMultiplier * performData.moveDamage,
                            performData.DirectHit,
                            performParams.owner,
                            hitbox, 
                            damageablesHit =>
                            {
                                performParams.owner.AttackLanded(this, damageablesHit);
                            });
                    }, cancellationToken);
            }
        }
    }

    protected override Action AddEffects(PerformParams performParams, PerformProcess performProcess, PerformData performData, CancellationToken cancellationToken)
    {
        Action baseCancel = base.AddEffects(performParams, performProcess, performData, cancellationToken);
        List<Action> effectCancels = new List<Action>();
        BeatPerformData beatPerformData = (BeatPerformData) performData;
        
        if (beatPerformData.beatEffects.Count > 0)
        {
            List<Action> beatCancels = new List<Action>();

            foreach (BeatActionEffect effect in beatPerformData.beatEffects)
            {
                MonoProcess beatProcess = MonoProcess.New(cancellationToken: cancellationToken);
                beatCancels.Add(
                    CountBeatsProcess(beatProcess, effect.BeatSync,
                        () =>
                        {
                            performParams.effectholders[effect.ParticleSystemsParentIndex].Play();
                        }, cancellationToken));
                effectCancels.Add(() => {
                    if (effect.StopOnMoveEnd) {
                        performParams.effectholders[effect.ParticleSystemsParentIndex].Stop();
                    }
                });
            }

            Action moveCancel = performProcess.ProcessCancel;

            performProcess.ProcessCancel = () =>
            {
                moveCancel?.Invoke();
                foreach (Action cancel in beatCancels)
                {
                    cancel?.Invoke();
                }
            };
        }

        void FullCancel()
        {
            baseCancel?.Invoke();
            foreach (Action cancel in effectCancels)
            {
                cancel?.Invoke();
            }
        }

        return FullCancel;
    }

    protected override Action AddSounds(PerformParams performParams, PerformProcess performProcess, PerformData performData, CancellationToken cancellationToken)
    {
        Action baseCancel = base.AddSounds(performParams, performProcess, performData, cancellationToken);
        
        List<Action> soundCancels = new List<Action>();
        BeatPerformData beatPerformData = (BeatPerformData) performData;
        
        if (beatPerformData.beatSounds.Count > 0)
        {
            List<Action> beatCancels = new List<Action>();
            
            foreach (BeatActionSoundEffect sound in beatPerformData.beatSounds)
            {
                MonoProcess beatProcess = MonoProcess.New(cancellationToken: cancellationToken);
                beatCancels.Add(
                    CountBeatsProcess(beatProcess, sound.BeatSync,
                        () =>
                        {   
                            PlaySoundEffect(sound, beatCancels);
                        }, cancellationToken));
            }

            Action moveCancel = performProcess.ProcessCancel;

            performProcess.ProcessCancel = () =>
            {
                moveCancel?.Invoke();
                foreach (Action cancel in beatCancels)
                {
                    cancel?.Invoke();
                }
            };
        }

        return () =>
        {
            baseCancel?.Invoke();
            foreach (Action cancels in soundCancels)
            {
                cancels?.Invoke();
            }
        };
    }
    
    protected override void AddMoveEffects(PerformParams performParams, PerformProcess performProcess, PerformData performData, CancellationToken cancellationToken)
    {
        base.AddMoveEffects(performParams, performProcess, performData, cancellationToken);
        
        BeatPerformData beatPerformData = (BeatPerformData) performData;
        
        if (beatPerformData.beatMoveEffects.Count > 0)
        {
            foreach (BeatMoveEffect moveEffect in beatPerformData.beatMoveEffects)
            {
                MonoProcess beatProcess = MonoProcess.New(cancellationToken: cancellationToken);
                CountBeatsProcess(beatProcess, moveEffect.BeatSync,
                    () => { moveEffect.MoveEffect.Activate(performParams.owner); }, cancellationToken);
            }
        }
    }

    private Action CountBeatsProcess(MonoProcess process, BeatSync beatSync, Action action, CancellationToken cancellationToken)
    {
        process.WaitForSeconds(beatSync.MinimalPerformTime);

        int mainBeat = 0;
        int offBeat = 0;

        void BeatAction(bool isFullBeat)
        {
            if (isFullBeat)
            {
                mainBeat++;
            }
            else
            {
                offBeat++;
            }
        }

        process.Do(() => m_beatSystem.Value.OnBeat.AddCancellableListener(BeatAction, cancellationToken))
            .WaitUntil(() => beatSync.ShouldActivate(mainBeat, offBeat));
        //TODO: remove these?
        process
            .Do(action)
            .Do(() =>
            {
                m_beatSystem.Value.OnBeat.RemoveListener(BeatAction);
            });

        return () =>
        {
            process.Stop();
            m_beatSystem.Value.OnBeat.RemoveListener(BeatAction);
        };
    }

    [Serializable]
    public class BeatActionHitBox : ActionHitBox, IConcreteCloneable<BeatActionHitBox>
    {
        [SerializeField] private BeatSync beatSync;

        public BeatSync BeatSync => beatSync;
        
        public new BeatActionHitBox Copy()
        {
            BeatActionHitBox copy = new BeatActionHitBox()
            {
                m_hitBoxColliderIndex = m_hitBoxColliderIndex,
                m_hitBoxStartTime = m_hitBoxStartTime,
                m_damageMultiplier = m_damageMultiplier,
                m_hitMoveEffect = m_hitMoveEffect,
                m_hitSeverity = m_hitSeverity,
                m_faceDamageSource = m_faceDamageSource,
                beatSync = beatSync.Copy(),
            };

            return copy;
        }
    }

    [Serializable]
    public class BeatActionEffect : ActionEffect, IConcreteCloneable<BeatActionEffect>
    {
        [SerializeField] private BeatSync beatSync;

        public BeatSync BeatSync => beatSync;
        
        public new BeatActionEffect Copy()
        {
            BeatActionEffect copy = new BeatActionEffect()
            {
                m_particleSystemsParentIndex = m_particleSystemsParentIndex,
                m_effectStartTime = m_effectStartTime,
                beatSync = beatSync.Copy(),
            };

            return copy;
        }
    }
        
    [Serializable]
    public class BeatActionSoundEffect : ActionSoundEffect, IConcreteCloneable<BeatActionSoundEffect>
    {
        [SerializeField] private BeatSync beatSync;

        public BeatSync BeatSync => beatSync;
        
        public new BeatActionSoundEffect Copy()
        {
            BeatActionSoundEffect copy = new BeatActionSoundEffect()
            {
                m_soundStartTime = m_soundStartTime,
                m_stopOnCancel = m_stopOnCancel,
                m_fadeDuration = m_fadeDuration,
                m_fixedDuration = m_fixedDuration,
                m_soundSource = m_soundSource,
                beatSync = beatSync.Copy(),
            };

            return copy;
        }
    }
    
    [Serializable]
    public class BeatMoveEffect : ActionMoveEffect, IConcreteCloneable<BeatMoveEffect>
    {
        [SerializeField] private BeatSync beatSync;

        public BeatSync BeatSync => beatSync;
        
        public new BeatMoveEffect Copy()
        {
            BeatMoveEffect copy = new BeatMoveEffect()
            {
                m_moveEffect = m_moveEffect,
                m_effectTime = m_effectTime,
                beatSync = beatSync.Copy(),
            };

            return copy;
        }
    }

    [Serializable]
    public class BeatFollowingOption : IConcreteCloneable<BeatFollowingOption>
    {
        [SerializeField] private BeatSync beatSync;
        [SerializeField] private BeatBasedActionData action;
        
        [SerializeField] private PlayerInputActions m_requiredHeldInput = PlayerInputActions.Default;

        public BeatSync BeatSync => beatSync;
        public BeatBasedActionData Action => action;
        public PlayerInputActions RequiredHeldInput => m_requiredHeldInput;
        
        public BeatFollowingOption Copy()
        {
            BeatFollowingOption copy = new BeatFollowingOption
            {
                beatSync = beatSync.Copy(),
                action = action,
                m_requiredHeldInput = m_requiredHeldInput,
            };
            return copy;
        }

        public object Clone()
        {
            return Copy();
        }
    }
    
    [Serializable]
    public class BeatSync
    {
        [SerializeField] private float minimalPerformTime;
        [SerializeField] private bool includeHalfBeat;
        [SerializeField] private int beatCount;
            
        public bool IncludeHalfBeat => includeHalfBeat;
        public int BeatCount => beatCount;
        public float MinimalPerformTime => minimalPerformTime;

        public bool ShouldActivate(int fullBeatCount, int halfBeatCount)
        {
            return (fullBeatCount >= BeatCount) || (IncludeHalfBeat && fullBeatCount + halfBeatCount >= BeatCount);
        }

        public BeatSync(float minTime, bool includeHalfBeat, int beatCount)
        {
            this.minimalPerformTime = minTime;
            this.includeHalfBeat = includeHalfBeat;
            this.beatCount = beatCount;
        }

        private BeatSync() {}

        public BeatSync Copy()
        {
            BeatSync copy = new BeatSync (minimalPerformTime, includeHalfBeat, beatCount);
            return copy;
        }
    }
    
    protected class BeatPerformData : PerformData
    {
        public bool isOnBeat;
        public bool previousMoveOnBeat;
        public List<BeatFollowingOption> beatFollowingOptions;
        public List<BeatActionHitBox> beatHitboxes;
        public List<BeatActionEffect> beatEffects;
        public List<BeatActionSoundEffect> beatSounds;
        public List<BeatMoveEffect> beatMoveEffects;
    }
    
    
    //Use to copy data from regular ActionData
    //[ContextMenu("CopyAll")]
    //public void CopyAllFromTarget()
    //{
    //    this.m_owner = source.owner;
    //    this.m_moveDuration = source.moveDuration;
    //    this.m_rootMotionModifier = source.rootMotionModifier;
    //    this.m_moveTween = source.moveTween;
    //    var x = new AnimatorParameterControl[source.animatorParameters.Count];
    //    source.animatorParameters.CopyTo(x);
    //    this.m_animatorParameters = x.ToList();
    //    this.m_hitboxes = source.hitboxes;
    //    this.m_soundEffects = source.soundEffects;
    //    this.m_effects = source.effects;
    //    this.m_followingActions = source.followingActions;
    //    
    //}
//
    //[SerializeField] private ActionData source;
}
