using System;
using System.Collections.Generic;
using System.Threading;
using Cysharp.Threading.Tasks;
using RibCageGames.Base;
using RibCageGames.Combat;
using RibCageGames.Input;
using RibCageGames.Music;
using RibCageGames.UI;
using UnityEngine;
using UnityEngine.Events;

public class TutorialDodgeSequenceController : MonoBehaviour
{
    [SerializeField] private BaseEnemyController m_rangedScrub;
    [SerializeField] private DummyEnemyHeal m_enemyHeal;
    [SerializeField] private DialogPopupSettings m_activateDialog;
    [SerializeField] private DialogPopupSettings m_startPracticeDialog;
    [SerializeField] private DialogPopupSettings m_dontAttackDialog;
    [SerializeField] private DialogPopupSettings m_dodgeTimingDialog;
    [SerializeField] private DialogPopupSettings m_endPracticeDialog;
    [SerializeField] private ActionData m_rangedScrubWindupAction;
    [SerializeField] private ActionData m_rangedScrubAction;
    [SerializeField] private ActionData m_rangedScrubFullAction;
    [SerializeField] private Collider m_activationCollider;
    [SerializeField] private int m_dodgesRequired;
    
    [SerializeField] private float m_sequenceFinishEventDelay;
    
    [SerializeField] private UnityEvent m_onActivate;
    [SerializeField] private UnityEvent m_dodgePracticeStart;
    [SerializeField] private UnityEvent m_onSequenceFinished;

    private PlayerService m_playerService;
    private PopupService m_popupService;
    private BeatSystem m_beatSystem;

    private void Start()
    {
        m_rangedScrub.Initialize();
        m_rangedScrub.ActivatePoolable();
        m_rangedScrub.gameObject.SetActive(false);
        m_playerService = ServiceLocator.Get<PlayerService>();
        m_popupService = ServiceLocator.Get<PopupService>();
        m_beatSystem = ServiceLocator.Get<BeatSystem>();
        m_enemyHeal.enabled = true;
    }

    public void Activate()
    {
        m_activationCollider.gameObject.SetActive(false);
        
        ServiceLocator.Get<InputService>().DisableInput();
        
        BeatSystem beatSystem = ServiceLocator.Get<BeatSystem>();
        
        beatSystem.OnHalfBeat.AddSingleUseListener(() =>
        {
            m_rangedScrub.PerformCombatAction(m_rangedScrubWindupAction);
            MonoProcess.NextFrame
                .Do(() =>
                    beatSystem.OnHalfBeat.AddSingleUseListener(ActivateOnBeat));
        });
    }


    private void ActivateOnBeat()
    {
        m_onActivate?.Invoke();
        m_rangedScrub.FaceDirection(m_playerService.PlayerController.transform.position - m_rangedScrub.transform.position);
        m_rangedScrub.PerformCombatAction(m_rangedScrubAction);
        
        if (ServiceLocator.Get<PopupService>().TryGetElement(out MetronomeSelector metronomeSelector))
        {
            metronomeSelector.CurrentMetronome.ShowFakeDangerIndicator(true);
        }

        m_popupService.ShowDialogPopup(m_activateDialog, StartDodgingPractice);
        //.Do(m_onSequenceFinished.Invoke)
    }

    private bool m_dodgePratciceDone = false;
    private bool m_dodgingPaused = false;
    
    private int m_attacksDodged = 0;

    private void StartDodgingPractice()
    {
        if (ServiceLocator.Get<PopupService>().TryGetElement(out MetronomeSelector metronomeSelector))
        {
            metronomeSelector.CurrentMetronome.ShowFakeDangerIndicator(false);
        }
        
        ServiceLocator.Get<PlayerService>().PlayerController.DeregisterNonMovementControls(
            new List<PlayerInputActions> { PlayerInputActions.Dodge, });
        
        m_playerService.OnAttackLanded.AddListenerUntil(
            (action, damageables) =>
            {
                if(m_dodgePratciceDone) { return; }
                m_popupService.ShowDialogPopup(m_dontAttackDialog, QueueEnemyAttack);
            },
            (action, damageables) => m_dodgePratciceDone);
        
        m_dodgingPaused = false;
        //Debug.LogError($"m_dodgingPaused = {m_dodgingPaused}");
        m_popupService.ShowDialogWithDelay(1f, m_startPracticeDialog, QueueEnemyAttack);
        
        //Start enemy attacks
        m_popupService.TryGetElement(out TutorialActionCount countText);
        countText.ActivateTextWithFormat($"Dodges on beat:", $"{{0}} / {{1}}", m_dodgesRequired);
        
        m_dodgePracticeStart?.Invoke();
        
        m_playerService.PlayerController.OnMoveStarted.AddListenerUntil(
            (action, input) =>
            {
                if(m_dodgePratciceDone || !(input == PlayerInputActions.Dodge || input == PlayerInputActions.Jump)) { return; }
                if (m_playerService.PlayerController.CurrentMoveJust)
                {
                    m_attacksDodged++;
                    countText.UpdateCount(m_attacksDodged, m_dodgesRequired);
                    if (m_attacksDodged >= m_dodgesRequired)
                    {
                        countText.Deactivate();
                        m_dodgePratciceDone = true;
                        ServiceLocator.Get<PlayerService>().PlayerController.RegisterNonMovementControls();
                        m_popupService.ShowDialogPopup(m_endPracticeDialog, FinishDodgingPractice);
                    }
                }
                else
                {
                    m_dodgingPaused = true;
                    m_popupService.ShowDialogWithDelay(0.5f, m_dodgeTimingDialog, () =>
                    {
                        m_dodgingPaused = false;
                        QueueEnemyAttack();
                    });
                }
            },
            (action, input) => m_dodgePratciceDone);
    }

    private CancellationTokenSource m_attackQueueSource;
    private void QueueEnemyAttack()
    {
        m_attackQueueSource?.Cancel();
        m_attackQueueSource = new CancellationTokenSource();
        QueueEnemyAttackProcess(m_attackQueueSource.Token);
    }
    
    private async void QueueEnemyAttackProcess(CancellationToken cancellationToken)
    {
        if (m_dodgePratciceDone || m_dodgingPaused) { return; }
        await UniTask.WaitForSeconds(1f);
        if (m_dodgePratciceDone || m_dodgingPaused) { return; }
        
        m_beatSystem.OnMainBeat.AddSingleUseListener(() =>
        {
            if(m_dodgePratciceDone || cancellationToken.IsCancellationRequested) { return; }
            m_rangedScrub.FaceDirection(m_playerService.PlayerController.transform.position - m_rangedScrub.transform.position);
            m_rangedScrub.PerformCombatAction(m_rangedScrubFullAction);    
        }, cancellationToken);

        await UniTask.WaitForSeconds(3f);
        if (m_dodgePratciceDone || m_dodgingPaused || cancellationToken.IsCancellationRequested) { return; }
        QueueEnemyAttackProcess(cancellationToken);
    }
    
    private void FinishDodgingPractice()
    {
        m_enemyHeal.enabled = false;
        m_onSequenceFinished.Invoke();
    }
}
