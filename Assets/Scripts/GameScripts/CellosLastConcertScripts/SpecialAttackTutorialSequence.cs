using System.Collections.Generic;
using RibCageGames.Base;
using RibCageGames.Combat;
using RibCageGames.Input;
using UnityEngine;
using UnityEngine.Events;

public class SpecialAttackTutorialSequence : MonoBehaviour
{
    [SerializeField] private BaseEnemyController m_scrub;
    [SerializeField] private List<ActionData> m_spawnActions;
    [SerializeField] private ActionData m_walkUpAction;
    [SerializeField] private UnityEvent m_onComplete;
    [SerializeField] private UnityEvent m_enemyDied;

    public void Activate()
    {
        m_scrub.Initialize();
        m_scrub.ResetPoolable(false);
        m_scrub.ActivatePoolable();
        
        m_scrub.Disengage();
        ServiceLocator.Get<InputService>().DisableInput();
        
        //m_scrub.PerformCombatAction(m_spawnAction, NavigateToPlayer);
        PerformStep(0);
    }

    private void PerformStep(int index)
    {
        m_scrub.Disengage();
        if (index >= 0 && index < m_spawnActions.Count)
        {
            m_scrub.PerformCombatAction(m_spawnActions[index], () => PerformStep(index + 1));
        }
        else
        {
            ((NutcrackerEnemyController) m_scrub).SetDamageTakenDuringCharge(0.95f);
            NavigateToPlayer();
        }
    }

    private void NavigateToPlayer()
    {
        CharacterControllerBase player = ServiceLocator.Get<PlayerService>().PlayerController;
        Vector3 pos = player.transform.position + player.OwnForward.normalized;
        m_scrub.NavigateTo(pos);
        //Action cancel = 
            m_scrub.PerformCombatAction(m_walkUpAction);
        MonoProcess.New().WaitUntil(() =>
                (m_scrub.transform.position - player.transform.position).magnitude < 2f)
            .Do(() =>
            {
                m_scrub.CancelCurrentMove();
                //cancel.Invoke();
                m_scrub.StopNavigating();
                m_onComplete?.Invoke();
                m_scrub.OnDamageTaken.AddSingleUseListener((_) =>
                {
                    m_scrub.Engage(false);
                    m_scrub.AttackingAllowed = true;
                });
                m_scrub.OnDead.AddSingleUseListener((_) => m_enemyDied.Invoke());
            });
    }
}
