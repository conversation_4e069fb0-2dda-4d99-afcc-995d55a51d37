using System.Collections;
using System.Collections.Generic;
using RibCageGames.Base;
using UnityEngine;

public class Prototype_Camera_Movement : MonoBehaviour
{
    // Start is called before the first frame update
    private Vector3 _inputDirection;
    [SerializeField] private Camera _mainCam;
    [SerializeField] private Rigidbody _rb;
    [SerializeField] private GameObject _graphicHolder;

    private Vector3 _moveDirection;
    private Quaternion _lookRotation;

    [SerializeField] private float _rotationSpeed = 10;
    public float _moveSpeed = 5;

    //animator use
    [HideInInspector] public float _horSpeed;
    [HideInInspector] public float _verSpeed;
    [HideInInspector] public float _lean;
    [HideInInspector] public bool _jumped = false;
    private Vector3 _lastFrameDir;
    private Vector3 _planeVelocity;

    [SerializeField] private float _acceleration = 5;
    [SerializeField] private float _airControl = 0.5f;
    [SerializeField] private float _maxVelocity = 15;

    [SerializeField] private float _jumpPower = 50;
    [SerializeField] private float _dropBoost = 8;
    public bool _isInAir;


    //private float m_time = 0f;
    //private void Start() {
    //    PrintTime();
    //    MonoProcess x = MonoProcess.NextFrame.Do(PrintTime);
    //    x.WaitForFrame().Do(PrintTime);
    //    m_time += 1f;
    //}

    //private void PrintTime() {
    //    Debug.Log("Time now is:" + m_time);
    //}

    private void FixedUpdate()
    {
        Movement();
    }
    // Update is called once per frame
    void Update()
    {
        //m_time += 1f;
        GetInputDirection(0);
        GroundCheck();

        //horizontal and vertical speed for animation
        _horSpeed = Mathf.Clamp(_planeVelocity.magnitude, 0, _moveSpeed) / _moveSpeed;
        //a stupid glitch fix
        if(_horSpeed < 0.001f)
        {
            _horSpeed = 0;
        }
        //print(_planeVelocity.magnitude);
        _verSpeed = (_rb.linearVelocity.y / _maxVelocity) * 2;

        //calculate leaning angle
        _lean = Vector3.Dot(_graphicHolder.transform.forward , _lastFrameDir);
        _lastFrameDir = _graphicHolder.transform.right;
        _lean = Mathf.Clamp(_lean * 10, -1, 1);

        //velocity check
        if (_rb.linearVelocity.magnitude > _maxVelocity)
        {
            _rb.linearVelocity = _rb.linearVelocity.normalized * _maxVelocity;
        }

        //orient graphic holder
        Vector3 _locMovement = transform.InverseTransformVector(_moveDirection);


        _locMovement = transform.InverseTransformVector(_moveDirection);

        if (Mathf.Abs(_locMovement.x) > 0.1f || Mathf.Abs(_locMovement.z) > 0.1f)
        {
            _locMovement = new Vector3(_locMovement.x, 0, _locMovement.z).normalized;
            _lookRotation = Quaternion.LookRotation(_locMovement);
            _graphicHolder.transform.localRotation = Quaternion.Lerp(_graphicHolder.transform.localRotation, _lookRotation, _rotationSpeed * Time.deltaTime);
        }


        //jump
        if (Input.GetKeyDown(KeyCode.Space) && !_isInAir)
        {
            _rb.AddForce(transform.up * _jumpPower, ForceMode.Impulse);
            _jumped = true;
        }
        //fall boost
        if (_isInAir)
        {
            _rb.AddForce(-transform.up * _dropBoost * Time.deltaTime * 100, ForceMode.Acceleration);
        }
    }

    private void GetInputDirection(int _method)
    {
        switch (_method)
        {
            case 0: //keyboard  ... yeah its stupid
            Vector3 _leftVec = Vector3.zero;
            Vector3 _rightVec = Vector3.zero;
            Vector3 _upVec = Vector3.zero;
            Vector3 _downVec = Vector3.zero;

            if (Input.GetKey(KeyCode.A))
            {
                _leftVec = Vector3.left;
            }

            if (Input.GetKey(KeyCode.D))
            {
                _rightVec = Vector3.right;
            }

            if (Input.GetKey(KeyCode.W))
            {
                _upVec = Vector3.up;
            }

            if (Input.GetKey(KeyCode.S))
            {
                _downVec = Vector3.down;
            }


            Vector3 _moveSum = _leftVec + _rightVec + _upVec + _downVec;

            _inputDirection = _moveSum.normalized;

            break;
            case 1: //360_Controller

            break;
        }
    }

    private void Movement() {
        _planeVelocity = new Vector3(_rb.linearVelocity.x , 0 , _rb.linearVelocity.z);

        Vector3 _fixedInput = new Vector3(_inputDirection.x, 0, _inputDirection.y);
        Vector3 _camForward = _mainCam.transform.forward;
        _camForward.y = 0;

        float _camRotation = Vector3.SignedAngle(_camForward, Vector3.forward, Vector3.up);
        _moveDirection = Quaternion.Euler(0, -_camRotation, 0) * _fixedInput;

        //movementScript
        //limit movement velocity, but still allow external forces
        Vector3 _moveDelta = (((_moveDirection * _moveSpeed) - _planeVelocity));
        _moveDelta = new Vector3(_moveDelta.x * _moveDirection.x, _moveDelta.y * _moveDirection.y, _moveDelta.z * _moveDirection.z);

        //modify delta depending on input direction
        _moveDelta = new Vector3(Mathf.Clamp01(_moveDelta.x), Mathf.Clamp01(_moveDelta.y), Mathf.Clamp01(_moveDelta.z));
        Vector3 _fixedMoveDirection = new Vector3(_moveDirection.x * _moveDelta.x, _moveDirection.y * _moveDelta.y, _moveDirection.z * _moveDelta.z);

        //apply air control
        float _airMod;
        if (_isInAir)
        {
            _airMod = _airControl;
        } else
        {
            _airMod = 1.0f;
        }

        //apply movement
        if (_inputDirection != Vector3.zero)
        {
            _rb.AddForce(_airMod * _fixedMoveDirection * _acceleration * Time.deltaTime, ForceMode.VelocityChange);
        }
    }
    
    private void GroundCheck() {
        RaycastHit hit;

        if (Physics.Raycast(transform.position + (transform.up * 0.1f) , -transform.up, out hit, 1000.0f))
        {
            //print(_isInAir +""+ hit.transform.name);
            if (hit.distance < 0.4f)
            {
                _isInAir = false;
            } else
            {
                _isInAir = true;
            }
        }
    }
}
