using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class Prototype_AnimatorHandler : MonoBeh<PERSON>our
{
    [SerializeField]private Animator _animController;
    [SerializeField] private Prototype_Camera_Movement _movementHandler;
    // Update is called once per frame
    void Update() {
        _animController.SetFloat("normSpeed", _movementHandler._horSpeed);
        _animController.SetFloat("normLean", _movementHandler._lean);
        _animController.SetFloat("normVspeed", _movementHandler._verSpeed);
        _animController.SetBool("isInAir", _movementHandler._isInAir);

        if (_movementHandler._jumped)
        {
            _movementHandler._jumped = false;
            _animController.SetTrigger("jump");
        }
    }
}
